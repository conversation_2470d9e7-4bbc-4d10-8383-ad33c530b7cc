{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import{ExclamationTriangleIcon,ClockIcon,ArrowPathIcon,ArrowRightOnRectangleIcon}from'@heroicons/react/24/outline';import{useSessionWarning}from'../../hooks/useSessionPersistence';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SessionWarning=()=>{const{showWarning,timeRemaining,extendSession,logout}=useSessionWarning();const[countdown,setCountdown]=useState(0);useEffect(()=>{if(showWarning&&timeRemaining>0){setCountdown(Math.ceil(timeRemaining/1000));const countdownInterval=setInterval(()=>{const remaining=Math.ceil(timeRemaining/1000);setCountdown(remaining);if(remaining<=0){clearInterval(countdownInterval);logout();}},1000);return()=>clearInterval(countdownInterval);}},[showWarning,timeRemaining,logout]);const formatTime=seconds=>{const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return`${minutes}:${remainingSeconds.toString().padStart(2,'0')}`;};return/*#__PURE__*/_jsx(AnimatePresence,{children:showWarning&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"fixed inset-0 bg-black bg-opacity-50 z-50\"}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:0.95,y:20},className:\"fixed inset-0 z-50 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-xl max-w-md w-full p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"w-8 h-8 text-yellow-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mr-3\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"\\u062A\\u062D\\u0630\\u064A\\u0631 \\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u062C\\u0644\\u0633\\u0629\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:\"\\u0633\\u062A\\u0646\\u062A\\u0647\\u064A \\u062C\\u0644\\u0633\\u062A\\u0643 \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B \\u0628\\u0633\\u0628\\u0628 \\u0639\\u062F\\u0645 \\u0627\\u0644\\u0646\\u0634\\u0627\\u0637. \\u0647\\u0644 \\u062A\\u0631\\u064A\\u062F \\u0627\\u0644\\u0627\\u0633\\u062A\\u0645\\u0631\\u0627\\u0631\\u061F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center p-4 bg-yellow-50 rounded-lg\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-6 h-6 text-yellow-600 ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold text-yellow-800\",children:formatTime(countdown)})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 text-center mt-2\",children:\"\\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062A\\u0628\\u0642\\u064A \\u0642\\u0628\\u0644 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C \\u0627\\u0644\\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:extendSession,className:\"flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(ArrowPathIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u062A\\u0645\\u062F\\u064A\\u062F \\u0627\\u0644\\u062C\\u0644\\u0633\\u0629\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:logout,className:\"flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",children:[/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 p-3 bg-red-50 rounded-lg\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-red-800 text-center\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u062A\\u0646\\u0628\\u064A\\u0647:\"}),\" \\u0633\\u064A\\u062A\\u0645 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u062E\\u0631\\u0648\\u062C\\u0643 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B \\u0639\\u0646\\u062F \\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0648\\u0642\\u062A\"]})})]})})]})});};export default SessionWarning;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "ExclamationTriangleIcon", "ClockIcon", "ArrowPathIcon", "ArrowRightOnRectangleIcon", "useSessionWarning", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SessionWarning", "showWarning", "timeRemaining", "extendSession", "logout", "countdown", "setCountdown", "Math", "ceil", "countdownInterval", "setInterval", "remaining", "clearInterval", "formatTime", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "children", "div", "initial", "opacity", "animate", "exit", "className", "scale", "y", "onClick"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/SessionWarning.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ExclamationTriangleIcon,\n  ClockIcon,\n  ArrowPathIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useSessionWarning } from '../../hooks/useSessionPersistence';\n\nconst SessionWarning: React.FC = () => {\n  const { showWarning, timeRemaining, extendSession, logout } = useSessionWarning();\n  const [countdown, setCountdown] = useState(0);\n\n  useEffect(() => {\n    if (showWarning && timeRemaining > 0) {\n      setCountdown(Math.ceil(timeRemaining / 1000));\n      \n      const countdownInterval = setInterval(() => {\n        const remaining = Math.ceil(timeRemaining / 1000);\n        setCountdown(remaining);\n        \n        if (remaining <= 0) {\n          clearInterval(countdownInterval);\n          logout();\n        }\n      }, 1000);\n\n      return () => clearInterval(countdownInterval);\n    }\n  }, [showWarning, timeRemaining, logout]);\n\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <AnimatePresence>\n      {showWarning && (\n        <>\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-50\"\n          />\n          \n          {/* Warning Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n          >\n            <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full p-6\">\n              {/* Header */}\n              <div className=\"flex items-center mb-4\">\n                <div className=\"flex-shrink-0\">\n                  <ExclamationTriangleIcon className=\"w-8 h-8 text-yellow-500\" />\n                </div>\n                <div className=\"mr-3\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">\n                    تحذير انتهاء الجلسة\n                  </h3>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"mb-6\">\n                <p className=\"text-gray-600 mb-4\">\n                  ستنتهي جلستك قريباً بسبب عدم النشاط. هل تريد الاستمرار؟\n                </p>\n                \n                {/* Countdown */}\n                <div className=\"flex items-center justify-center p-4 bg-yellow-50 rounded-lg\">\n                  <ClockIcon className=\"w-6 h-6 text-yellow-600 ml-2\" />\n                  <span className=\"text-2xl font-bold text-yellow-800\">\n                    {formatTime(countdown)}\n                  </span>\n                </div>\n                \n                <p className=\"text-sm text-gray-500 text-center mt-2\">\n                  الوقت المتبقي قبل تسجيل الخروج التلقائي\n                </p>\n              </div>\n\n              {/* Actions */}\n              <div className=\"flex space-x-3 space-x-reverse\">\n                <button\n                  onClick={extendSession}\n                  className=\"flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n                >\n                  <ArrowPathIcon className=\"w-5 h-5 ml-2\" />\n                  تمديد الجلسة\n                </button>\n                \n                <button\n                  onClick={logout}\n                  className=\"flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"w-5 h-5 ml-2\" />\n                  تسجيل الخروج\n                </button>\n              </div>\n\n              {/* Auto-logout warning */}\n              <div className=\"mt-4 p-3 bg-red-50 rounded-lg\">\n                <p className=\"text-sm text-red-800 text-center\">\n                  <strong>تنبيه:</strong> سيتم تسجيل خروجك تلقائياً عند انتهاء الوقت\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default SessionWarning;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,uBAAuB,CACvBC,SAAS,CACTC,aAAa,CACbC,yBAAyB,KACpB,6BAA6B,CACpC,OAASC,iBAAiB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtE,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAEC,WAAW,CAAEC,aAAa,CAAEC,aAAa,CAAEC,MAAO,CAAC,CAAGX,iBAAiB,CAAC,CAAC,CACjF,KAAM,CAACY,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACd,GAAIe,WAAW,EAAIC,aAAa,CAAG,CAAC,CAAE,CACpCI,YAAY,CAACC,IAAI,CAACC,IAAI,CAACN,aAAa,CAAG,IAAI,CAAC,CAAC,CAE7C,KAAM,CAAAO,iBAAiB,CAAGC,WAAW,CAAC,IAAM,CAC1C,KAAM,CAAAC,SAAS,CAAGJ,IAAI,CAACC,IAAI,CAACN,aAAa,CAAG,IAAI,CAAC,CACjDI,YAAY,CAACK,SAAS,CAAC,CAEvB,GAAIA,SAAS,EAAI,CAAC,CAAE,CAClBC,aAAa,CAACH,iBAAiB,CAAC,CAChCL,MAAM,CAAC,CAAC,CACV,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMQ,aAAa,CAACH,iBAAiB,CAAC,CAC/C,CACF,CAAC,CAAE,CAACR,WAAW,CAAEC,aAAa,CAAEE,MAAM,CAAC,CAAC,CAExC,KAAM,CAAAS,UAAU,CAAIC,OAAe,EAAK,CACtC,KAAM,CAAAC,OAAO,CAAGR,IAAI,CAACS,KAAK,CAACF,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAG,gBAAgB,CAAGH,OAAO,CAAG,EAAE,CACrC,MAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACrE,CAAC,CAED,mBACExB,IAAA,CAACP,eAAe,EAAAgC,QAAA,CACbnB,WAAW,eACVJ,KAAA,CAAAE,SAAA,EAAAqB,QAAA,eAEEzB,IAAA,CAACR,MAAM,CAACkC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrBG,SAAS,CAAC,2CAA2C,CACtD,CAAC,cAGF/B,IAAA,CAACR,MAAM,CAACkC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,IAAI,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC5CJ,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CACxCH,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,IAAI,CAAEC,CAAC,CAAE,EAAG,CAAE,CACzCF,SAAS,CAAC,yDAAyD,CAAAN,QAAA,cAEnEvB,KAAA,QAAK6B,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAEhEvB,KAAA,QAAK6B,SAAS,CAAC,wBAAwB,CAAAN,QAAA,eACrCzB,IAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC5BzB,IAAA,CAACN,uBAAuB,EAACqC,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC5D,CAAC,cACN/B,IAAA,QAAK+B,SAAS,CAAC,MAAM,CAAAN,QAAA,cACnBzB,IAAA,OAAI+B,SAAS,CAAC,mCAAmC,CAAAN,QAAA,CAAC,0GAElD,CAAI,CAAC,CACF,CAAC,EACH,CAAC,cAGNvB,KAAA,QAAK6B,SAAS,CAAC,MAAM,CAAAN,QAAA,eACnBzB,IAAA,MAAG+B,SAAS,CAAC,oBAAoB,CAAAN,QAAA,CAAC,+RAElC,CAAG,CAAC,cAGJvB,KAAA,QAAK6B,SAAS,CAAC,8DAA8D,CAAAN,QAAA,eAC3EzB,IAAA,CAACL,SAAS,EAACoC,SAAS,CAAC,8BAA8B,CAAE,CAAC,cACtD/B,IAAA,SAAM+B,SAAS,CAAC,oCAAoC,CAAAN,QAAA,CACjDP,UAAU,CAACR,SAAS,CAAC,CAClB,CAAC,EACJ,CAAC,cAENV,IAAA,MAAG+B,SAAS,CAAC,wCAAwC,CAAAN,QAAA,CAAC,mNAEtD,CAAG,CAAC,EACD,CAAC,cAGNvB,KAAA,QAAK6B,SAAS,CAAC,gCAAgC,CAAAN,QAAA,eAC7CvB,KAAA,WACEgC,OAAO,CAAE1B,aAAc,CACvBuB,SAAS,CAAC,sIAAsI,CAAAN,QAAA,eAEhJzB,IAAA,CAACJ,aAAa,EAACmC,SAAS,CAAC,cAAc,CAAE,CAAC,sEAE5C,EAAQ,CAAC,cAET7B,KAAA,WACEgC,OAAO,CAAEzB,MAAO,CAChBsB,SAAS,CAAC,gIAAgI,CAAAN,QAAA,eAE1IzB,IAAA,CAACH,yBAAyB,EAACkC,SAAS,CAAC,cAAc,CAAE,CAAC,sEAExD,EAAQ,CAAC,EACN,CAAC,cAGN/B,IAAA,QAAK+B,SAAS,CAAC,+BAA+B,CAAAN,QAAA,cAC5CvB,KAAA,MAAG6B,SAAS,CAAC,kCAAkC,CAAAN,QAAA,eAC7CzB,IAAA,WAAAyB,QAAA,CAAQ,iCAAM,CAAQ,CAAC,kOACzB,EAAG,CAAC,CACD,CAAC,EACH,CAAC,CACI,CAAC,EACb,CACH,CACc,CAAC,CAEtB,CAAC,CAED,cAAe,CAAApB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}