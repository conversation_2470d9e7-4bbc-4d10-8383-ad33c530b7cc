{"ast": null, "code": "import React from'react';import{NavLink,useLocation}from'react-router-dom';import{motion}from'framer-motion';import{HomeIcon,FolderIcon,AcademicCapIcon,UsersIcon,ClipboardDocumentListIcon,DocumentTextIcon,ChartBarIcon,CogIcon,XMarkIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AdminSidebar=_ref=>{let{isOpen,onClose}=_ref;const location=useLocation();const menuItems=[{name:'لوحة التحكم',href:'/admin',icon:HomeIcon,exact:true},{name:'الأقسام',href:'/admin/categories',icon:FolderIcon},{name:'الكورسات',href:'/admin/courses',icon:AcademicCapIcon},{name:'الطلاب',href:'/admin/students',icon:UsersIcon},{name:'الاختبارات',href:'/admin/quizzes',icon:ClipboardDocumentListIcon},{name:'الشهادات',href:'/admin/certificates',icon:DocumentTextIcon},{name:'التحليلات',href:'/admin/analytics',icon:ChartBarIcon},{name:'الإعدادات',href:'/admin/settings',icon:CogIcon}];const isActive=(href,exact)=>{if(exact){return location.pathname===href;}return location.pathname.startsWith(href);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex md:flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col w-64\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col flex-grow bg-white border-l border-gray-200 pt-5 pb-4 overflow-y-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center flex-shrink-0 px-4 mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary-600 rounded-lg p-2\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-8 h-8 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-bold text-gray-900\",children:\"ALaa Abd Hamied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"})]})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-5 flex-1 px-2 space-y-1\",children:menuItems.map(item=>{const Icon=item.icon;const active=isActive(item.href,item.exact);return/*#__PURE__*/_jsxs(NavLink,{to:item.href,className:`\n                      group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                      ${active?'bg-primary-100 text-primary-900 border-l-4 border-primary-600':'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}\n                    `,children:[/*#__PURE__*/_jsx(Icon,{className:`\n                        ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                        ${active?'text-primary-600':'text-gray-400 group-hover:text-gray-500'}\n                      `}),item.name]},item.name);})})]})})}),/*#__PURE__*/_jsx(motion.div,{initial:{x:-300},animate:{x:isOpen?0:-300},transition:{type:'tween',duration:0.3},className:\"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl md:hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary-600 rounded-lg p-2\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-bold text-gray-900\",children:\"ALaa Abd Hamied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-6 h-6\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",children:menuItems.map(item=>{const Icon=item.icon;const active=isActive(item.href,item.exact);return/*#__PURE__*/_jsxs(NavLink,{to:item.href,onClick:onClose,className:`\n                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                    ${active?'bg-primary-100 text-primary-900 border-l-4 border-primary-600':'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}\n                  `,children:[/*#__PURE__*/_jsx(Icon,{className:`\n                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                      ${active?'text-primary-600':'text-gray-400 group-hover:text-gray-500'}\n                    `}),item.name]},item.name);})})]})})]});};export default AdminSidebar;", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "motion", "HomeIcon", "FolderIcon", "AcademicCapIcon", "UsersIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "ChartBarIcon", "CogIcon", "XMarkIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AdminSidebar", "_ref", "isOpen", "onClose", "location", "menuItems", "name", "href", "icon", "exact", "isActive", "pathname", "startsWith", "children", "className", "map", "item", "Icon", "active", "to", "div", "initial", "x", "animate", "transition", "type", "duration", "onClick"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/AdminSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  HomeIcon,\n  FolderIcon,\n  AcademicCapIcon,\n  UsersIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  ChartBarIcon,\n  CogIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\ninterface AdminSidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onClose }) => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      name: 'لوحة التحكم',\n      href: '/admin',\n      icon: HomeIcon,\n      exact: true\n    },\n    {\n      name: 'الأقسام',\n      href: '/admin/categories',\n      icon: FolderIcon\n    },\n    {\n      name: 'الكورسات',\n      href: '/admin/courses',\n      icon: AcademicCapIcon\n    },\n    {\n      name: 'الطلاب',\n      href: '/admin/students',\n      icon: UsersIcon\n    },\n    {\n      name: 'الاختبارات',\n      href: '/admin/quizzes',\n      icon: ClipboardDocumentListIcon\n    },\n    {\n      name: 'الشهادات',\n      href: '/admin/certificates',\n      icon: DocumentTextIcon\n    },\n    {\n      name: 'التحليلات',\n      href: '/admin/analytics',\n      icon: ChartBarIcon\n    },\n    {\n      name: 'الإعدادات',\n      href: '/admin/settings',\n      icon: CogIcon\n    }\n  ];\n\n  const isActive = (href: string, exact?: boolean) => {\n    if (exact) {\n      return location.pathname === href;\n    }\n    return location.pathname.startsWith(href);\n  };\n\n  return (\n    <>\n      {/* Desktop & Tablet Sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64\">\n          <div className=\"flex flex-col flex-grow bg-white border-l border-gray-200 pt-5 pb-4 overflow-y-auto\">\n            {/* Logo */}\n            <div className=\"flex items-center flex-shrink-0 px-4 mb-8\">\n              <div className=\"bg-primary-600 rounded-lg p-2\">\n                <AcademicCapIcon className=\"w-8 h-8 text-white\" />\n              </div>\n              <div className=\"mr-3\">\n                <h2 className=\"text-lg font-bold text-gray-900\">ALaa Abd Hamied</h2>\n                <p className=\"text-sm text-gray-500\">لوحة المدير</p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n              {menuItems.map((item) => {\n                const Icon = item.icon;\n                const active = isActive(item.href, item.exact);\n                \n                return (\n                  <NavLink\n                    key={item.name}\n                    to={item.href}\n                    className={`\n                      group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                      ${active\n                        ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                      }\n                    `}\n                  >\n                    <Icon\n                      className={`\n                        ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                        ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}\n                      `}\n                    />\n                    {item.name}\n                  </NavLink>\n                );\n              })}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Sidebar */}\n      <motion.div\n        initial={{ x: -300 }}\n        animate={{ x: isOpen ? 0 : -300 }}\n        transition={{ type: 'tween', duration: 0.3 }}\n        className=\"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl md:hidden\"\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-primary-600 rounded-lg p-2\">\n                <AcademicCapIcon className=\"w-6 h-6 text-white\" />\n              </div>\n              <div className=\"mr-3\">\n                <h2 className=\"text-lg font-bold text-gray-900\">ALaa Abd Hamied</h2>\n                <p className=\"text-sm text-gray-500\">لوحة المدير</p>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\">\n            {menuItems.map((item) => {\n              const Icon = item.icon;\n              const active = isActive(item.href, item.exact);\n              \n              return (\n                <NavLink\n                  key={item.name}\n                  to={item.href}\n                  onClick={onClose}\n                  className={`\n                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                    ${active\n                      ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <Icon\n                    className={`\n                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                      ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}\n                    `}\n                  />\n                  {item.name}\n                </NavLink>\n              );\n            })}\n          </nav>\n        </div>\n      </motion.div>\n    </>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,CAAEC,WAAW,KAAQ,kBAAkB,CACvD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,UAAU,CACVC,eAAe,CACfC,SAAS,CACTC,yBAAyB,CACzBC,gBAAgB,CAChBC,YAAY,CACZC,OAAO,CACPC,SAAS,KACJ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAOrC,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,MAAM,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CACpE,KAAM,CAAAG,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAsB,SAAS,CAAG,CAChB,CACEC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAEvB,QAAQ,CACdwB,KAAK,CAAE,IACT,CAAC,CACD,CACEH,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAEtB,UACR,CAAC,CACD,CACEoB,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAErB,eACR,CAAC,CACD,CACEmB,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAEpB,SACR,CAAC,CACD,CACEkB,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAEnB,yBACR,CAAC,CACD,CACEiB,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,qBAAqB,CAC3BC,IAAI,CAAElB,gBACR,CAAC,CACD,CACEgB,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAEjB,YACR,CAAC,CACD,CACEe,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAEhB,OACR,CAAC,CACF,CAED,KAAM,CAAAkB,QAAQ,CAAGA,CAACH,IAAY,CAAEE,KAAe,GAAK,CAClD,GAAIA,KAAK,CAAE,CACT,MAAO,CAAAL,QAAQ,CAACO,QAAQ,GAAKJ,IAAI,CACnC,CACA,MAAO,CAAAH,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC,CAC3C,CAAC,CAED,mBACEV,KAAA,CAAAE,SAAA,EAAAc,QAAA,eAEElB,IAAA,QAAKmB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9ClB,IAAA,QAAKmB,SAAS,CAAC,oBAAoB,CAAAD,QAAA,cACjChB,KAAA,QAAKiB,SAAS,CAAC,qFAAqF,CAAAD,QAAA,eAElGhB,KAAA,QAAKiB,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACxDlB,IAAA,QAAKmB,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5ClB,IAAA,CAACR,eAAe,EAAC2B,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC/C,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBlB,IAAA,OAAImB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACpElB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,+DAAW,CAAG,CAAC,EACjD,CAAC,EACH,CAAC,cAGNlB,IAAA,QAAKmB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACxCR,SAAS,CAACU,GAAG,CAAEC,IAAI,EAAK,CACvB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,KAAM,CAAAU,MAAM,CAAGR,QAAQ,CAACM,IAAI,CAACT,IAAI,CAAES,IAAI,CAACP,KAAK,CAAC,CAE9C,mBACEZ,KAAA,CAACf,OAAO,EAENqC,EAAE,CAAEH,IAAI,CAACT,IAAK,CACdO,SAAS,CAAE;AAC/B;AACA,wBAAwBI,MAAM,CACJ,+DAA+D,CAC/D,oDAAoD;AAC9E,qBACsB,CAAAL,QAAA,eAEFlB,IAAA,CAACsB,IAAI,EACHH,SAAS,CAAE;AACjC;AACA,0BAA0BI,MAAM,CAAG,kBAAkB,CAAG,yCAAyC;AACjG,uBAAwB,CACH,CAAC,CACDF,IAAI,CAACV,IAAI,GAhBLU,IAAI,CAACV,IAiBH,CAAC,CAEd,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGNX,IAAA,CAACX,MAAM,CAACoC,GAAG,EACTC,OAAO,CAAE,CAAEC,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBC,OAAO,CAAE,CAAED,CAAC,CAAEpB,MAAM,CAAG,CAAC,CAAG,CAAC,GAAI,CAAE,CAClCsB,UAAU,CAAE,CAAEC,IAAI,CAAE,OAAO,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7CZ,SAAS,CAAC,gEAAgE,CAAAD,QAAA,cAE1EhB,KAAA,QAAKiB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAEnChB,KAAA,QAAKiB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,eAC7EhB,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChClB,IAAA,QAAKmB,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5ClB,IAAA,CAACR,eAAe,EAAC2B,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC/C,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBlB,IAAA,OAAImB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACpElB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,+DAAW,CAAG,CAAC,EACjD,CAAC,EACH,CAAC,cACNlB,IAAA,WACEgC,OAAO,CAAExB,OAAQ,CACjBW,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9ElB,IAAA,CAACF,SAAS,EAACqB,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cAGNnB,IAAA,QAAKmB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDR,SAAS,CAACU,GAAG,CAAEC,IAAI,EAAK,CACvB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,KAAM,CAAAU,MAAM,CAAGR,QAAQ,CAACM,IAAI,CAACT,IAAI,CAAES,IAAI,CAACP,KAAK,CAAC,CAE9C,mBACEZ,KAAA,CAACf,OAAO,EAENqC,EAAE,CAAEH,IAAI,CAACT,IAAK,CACdoB,OAAO,CAAExB,OAAQ,CACjBW,SAAS,CAAE;AAC7B;AACA,sBAAsBI,MAAM,CACJ,+DAA+D,CAC/D,oDAAoD;AAC5E,mBACoB,CAAAL,QAAA,eAEFlB,IAAA,CAACsB,IAAI,EACHH,SAAS,CAAE;AAC/B;AACA,wBAAwBI,MAAM,CAAG,kBAAkB,CAAG,yCAAyC;AAC/F,qBAAsB,CACH,CAAC,CACDF,IAAI,CAACV,IAAI,GAjBLU,IAAI,CAACV,IAkBH,CAAC,CAEd,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CACI,CAAC,EACb,CAAC,CAEP,CAAC,CAED,cAAe,CAAAN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}