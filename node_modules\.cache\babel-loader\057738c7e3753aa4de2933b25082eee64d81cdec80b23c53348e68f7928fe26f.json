{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{UserIcon,PencilIcon,CheckIcon,XMarkIcon,EnvelopeIcon,IdentificationIcon,CalendarIcon,CameraIcon,EyeIcon,EyeSlashIcon}from'@heroicons/react/24/outline';import{supabase}from'../../config/supabase';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EditableStudentProfile=_ref=>{var _user$createdAt;let{user}=_ref;const[isEditing,setIsEditing]=useState(false);const[loading,setLoading]=useState(false);const[formData,setFormData]=useState({name:user.name||'',email:user.email||'',avatar_url:user.avatar_url||''});const[showAccessCode,setShowAccessCode]=useState(false);const[studentStats,setStudentStats]=useState({totalCourses:0,completedCourses:0,certificates:0,joinDate:user.created_at||((_user$createdAt=user.createdAt)===null||_user$createdAt===void 0?void 0:_user$createdAt.toISOString())||new Date().toISOString()});useEffect(()=>{loadStudentStats();},[user]);const loadStudentStats=async()=>{try{var _user$createdAt2;// Get enrollments\nconst{data:enrollments}=await supabase.from('student_enrollments').select('*').eq('student_id',user.id);// Get certificates\nconst{data:certificates}=await supabase.from('certificates').select('*').eq('student_id',user.id);const totalCourses=(enrollments===null||enrollments===void 0?void 0:enrollments.length)||0;const completedCourses=(enrollments===null||enrollments===void 0?void 0:enrollments.filter(e=>e.progress===100).length)||0;const certificatesCount=(certificates===null||certificates===void 0?void 0:certificates.length)||0;setStudentStats({totalCourses,completedCourses,certificates:certificatesCount,joinDate:user.created_at||((_user$createdAt2=user.createdAt)===null||_user$createdAt2===void 0?void 0:_user$createdAt2.toISOString())||new Date().toISOString()});}catch(error){console.error('Error loading student stats:',error);}};const handleSave=async()=>{if(!formData.name.trim()){toast.error('الاسم مطلوب');return;}if(formData.email&&!isValidEmail(formData.email)){toast.error('البريد الإلكتروني غير صحيح');return;}try{setLoading(true);const{data,error}=await supabase.from('students').update({name:formData.name.trim(),email:formData.email.trim()||null,avatar_url:formData.avatar_url.trim()||null,updated_at:new Date().toISOString()}).eq('id',user.id).select().single();if(error)throw error;// Update localStorage\nconst currentStudent=JSON.parse(localStorage.getItem('currentStudent')||'{}');const updatedStudent={...currentStudent,...data};localStorage.setItem('currentStudent',JSON.stringify(updatedStudent));toast.success('تم تحديث الملف الشخصي بنجاح');setIsEditing(false);// Refresh page to reflect changes\nwindow.location.reload();}catch(error){console.error('Error updating profile:',error);toast.error('فشل في تحديث الملف الشخصي');}finally{setLoading(false);}};const handleCancel=()=>{setFormData({name:user.name||'',email:user.email||'',avatar_url:user.avatar_url||''});setIsEditing(false);};const isValidEmail=email=>{const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;return emailRegex.test(email);};const copyAccessCode=()=>{navigator.clipboard.writeText(user.access_code||user.accessCode);toast.success('تم نسخ كود الوصول');};const getAvatarUrl=()=>{if(formData.avatar_url){return formData.avatar_url;}return`https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name)}&background=3B82F6&color=fff&size=200`;};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"}),!isEditing?/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsEditing(true),className:\"inline-flex items-center px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4 ml-2\"}),\"\\u062A\\u0639\\u062F\\u064A\\u0644\"]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleSave,disabled:loading,className:\"inline-flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\",children:[/*#__PURE__*/_jsx(CheckIcon,{className:\"w-4 h-4 ml-2\"}),loading?'جاري الحفظ...':'حفظ']}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleCancel,disabled:loading,className:\"inline-flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",children:[/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-4 h-4 ml-2\"}),\"\\u0625\\u0644\\u063A\\u0627\\u0621\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative inline-block\",children:[/*#__PURE__*/_jsx(\"img\",{src:getAvatarUrl(),alt:formData.name,className:\"w-32 h-32 rounded-full mx-auto object-cover border-4 border-white shadow-lg\"}),isEditing&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(CameraIcon,{className:\"w-8 h-8 text-white\"})})]}),isEditing&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0635\\u0648\\u0631\\u0629 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:formData.avatar_url,onChange:e=>setFormData({...formData,avatar_url:e.target.value}),placeholder:\"https://example.com/avatar.jpg\",className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"}),isEditing?/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.name,onChange:e=>setFormData({...formData,name:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645\\u0643 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-gray-400 ml-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:formData.name||'غير محدد'})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"}),isEditing?/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:formData.email,onChange:e=>setFormData({...formData,email:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"<EMAIL>\"}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(EnvelopeIcon,{className:\"w-5 h-5 text-gray-400 ml-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:formData.email||'غير محدد'})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(IdentificationIcon,{className:\"w-5 h-5 text-gray-400 ml-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900 font-mono flex-1\",children:showAccessCode?user.access_code||user.accessCode:'••••••••'}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowAccessCode(!showAccessCode),className:\"p-1 text-gray-400 hover:text-gray-600 ml-2\",children:showAccessCode?/*#__PURE__*/_jsx(EyeSlashIcon,{className:\"w-4 h-4\"}):/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:copyAccessCode,className:\"px-2 py-1 text-xs bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors\",children:\"\\u0646\\u0633\\u062E\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0643\\u0648\\u062F \\u0644\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0627\\u0646\\u0636\\u0645\\u0627\\u0645\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(CalendarIcon,{className:\"w-5 h-5 text-gray-400 ml-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-900\",children:new Date(studentStats.joinDate).toLocaleDateString('ar-SA',{year:'numeric',month:'long',day:'numeric'})})]})]})]})})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-blue-600 mb-1\",children:studentStats.totalCourses}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-blue-800\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-green-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-green-600 mb-1\",children:studentStats.completedCourses}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-green-800\",children:\"\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-4 bg-yellow-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-yellow-600 mb-1\",children:studentStats.certificates}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-yellow-800\",children:\"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u062D\\u0627\\u0635\\u0644 \\u0639\\u0644\\u064A\\u0647\\u0627\"})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"\\u062D\\u0633\\u0627\\u0628\\u0643 \\u0646\\u0634\\u0637 \\u0648\\u064A\\u0645\\u0643\\u0646\\u0643 \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0644\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:\"\\u0646\\u0634\\u0637\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"\\u062A\\u0644\\u0642\\u064A \\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A \\u062D\\u0648\\u0644 \\u0627\\u0644\\u062A\\u062D\\u062F\\u064A\\u062B\\u0627\\u062A \\u0648\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"relative inline-flex items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"sr-only peer\",defaultChecked:true}),/*#__PURE__*/_jsx(\"div\",{className:\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"})]})]})]})]})]});};export default EditableStudentProfile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "UserIcon", "PencilIcon", "CheckIcon", "XMarkIcon", "EnvelopeIcon", "IdentificationIcon", "CalendarIcon", "CameraIcon", "EyeIcon", "EyeSlashIcon", "supabase", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "EditableStudentProfile", "_ref", "_user$createdAt", "user", "isEditing", "setIsEditing", "loading", "setLoading", "formData", "setFormData", "name", "email", "avatar_url", "showAccessCode", "setShowAccessCode", "studentStats", "setStudentStats", "totalCourses", "completedCourses", "certificates", "joinDate", "created_at", "createdAt", "toISOString", "Date", "loadStudentStats", "_user$createdAt2", "data", "enrollments", "from", "select", "eq", "id", "length", "filter", "e", "progress", "certificatesCount", "error", "console", "handleSave", "trim", "isValidEmail", "update", "updated_at", "single", "currentStudent", "JSON", "parse", "localStorage", "getItem", "updatedStudent", "setItem", "stringify", "success", "window", "location", "reload", "handleCancel", "emailRegex", "test", "copyAccessCode", "navigator", "clipboard", "writeText", "access_code", "accessCode", "getAvatarUrl", "encodeURIComponent", "className", "children", "div", "initial", "opacity", "y", "animate", "onClick", "disabled", "src", "alt", "type", "value", "onChange", "target", "placeholder", "toLocaleDateString", "year", "month", "day", "transition", "delay", "defaultChecked"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/EditableStudentProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserIcon,\n  PencilIcon,\n  CheckIcon,\n  XMarkIcon,\n  EnvelopeIcon,\n  IdentificationIcon,\n  CalendarIcon,\n  CameraIcon,\n  EyeIcon,\n  EyeSlashIcon\n} from '@heroicons/react/24/outline';\nimport { Student } from '../../types';\nimport { supabaseService } from '../../services/supabaseService';\nimport { supabase } from '../../config/supabase';\nimport { toast } from 'react-hot-toast';\n\ninterface Props {\n  user: Student;\n}\n\ninterface ProfileFormData {\n  name: string;\n  email: string;\n  avatar_url: string;\n}\n\nconst EditableStudentProfile: React.FC<Props> = ({ user }) => {\n  const [isEditing, setIsEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState<ProfileFormData>({\n    name: user.name || '',\n    email: user.email || '',\n    avatar_url: (user as any).avatar_url || ''\n  });\n  const [showAccessCode, setShowAccessCode] = useState(false);\n  const [studentStats, setStudentStats] = useState({\n    totalCourses: 0,\n    completedCourses: 0,\n    certificates: 0,\n    joinDate: (user as any).created_at || user.createdAt?.toISOString() || new Date().toISOString()\n  });\n\n  useEffect(() => {\n    loadStudentStats();\n  }, [user]);\n\n  const loadStudentStats = async () => {\n    try {\n      // Get enrollments\n      const { data: enrollments } = await supabase\n        .from('student_enrollments')\n        .select('*')\n        .eq('student_id', user.id);\n\n      // Get certificates\n      const { data: certificates } = await supabase\n        .from('certificates')\n        .select('*')\n        .eq('student_id', user.id);\n\n      const totalCourses = enrollments?.length || 0;\n      const completedCourses = enrollments?.filter(e => e.progress === 100).length || 0;\n      const certificatesCount = certificates?.length || 0;\n\n      setStudentStats({\n        totalCourses,\n        completedCourses,\n        certificates: certificatesCount,\n        joinDate: (user as any).created_at || user.createdAt?.toISOString() || new Date().toISOString()\n      });\n    } catch (error) {\n      console.error('Error loading student stats:', error);\n    }\n  };\n\n  const handleSave = async () => {\n    if (!formData.name.trim()) {\n      toast.error('الاسم مطلوب');\n      return;\n    }\n\n    if (formData.email && !isValidEmail(formData.email)) {\n      toast.error('البريد الإلكتروني غير صحيح');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const { data, error } = await supabase\n        .from('students')\n        .update({\n          name: formData.name.trim(),\n          email: formData.email.trim() || null,\n          avatar_url: formData.avatar_url.trim() || null,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', user.id)\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Update localStorage\n      const currentStudent = JSON.parse(localStorage.getItem('currentStudent') || '{}');\n      const updatedStudent = { ...currentStudent, ...data };\n      localStorage.setItem('currentStudent', JSON.stringify(updatedStudent));\n\n      toast.success('تم تحديث الملف الشخصي بنجاح');\n      setIsEditing(false);\n\n      // Refresh page to reflect changes\n      window.location.reload();\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      toast.error('فشل في تحديث الملف الشخصي');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setFormData({\n      name: user.name || '',\n      email: user.email || '',\n      avatar_url: (user as any).avatar_url || ''\n    });\n    setIsEditing(false);\n  };\n\n  const isValidEmail = (email: string) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const copyAccessCode = () => {\n    navigator.clipboard.writeText((user as any).access_code || user.accessCode);\n    toast.success('تم نسخ كود الوصول');\n  };\n\n  const getAvatarUrl = () => {\n    if (formData.avatar_url) {\n      return formData.avatar_url;\n    }\n    return `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name)}&background=3B82F6&color=fff&size=200`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Profile Header */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-white rounded-lg p-6 shadow-sm border\"\n      >\n        <div className=\"flex items-start justify-between mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">الملف الشخصي</h1>\n          {!isEditing ? (\n            <button\n              onClick={() => setIsEditing(true)}\n              className=\"inline-flex items-center px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n            >\n              <PencilIcon className=\"w-4 h-4 ml-2\" />\n              تعديل\n            </button>\n          ) : (\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={handleSave}\n                disabled={loading}\n                className=\"inline-flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"\n              >\n                <CheckIcon className=\"w-4 h-4 ml-2\" />\n                {loading ? 'جاري الحفظ...' : 'حفظ'}\n              </button>\n              <button\n                onClick={handleCancel}\n                disabled={loading}\n                className=\"inline-flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                <XMarkIcon className=\"w-4 h-4 ml-2\" />\n                إلغاء\n              </button>\n            </div>\n          )}\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Avatar Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"text-center\">\n              <div className=\"relative inline-block\">\n                <img\n                  src={getAvatarUrl()}\n                  alt={formData.name}\n                  className=\"w-32 h-32 rounded-full mx-auto object-cover border-4 border-white shadow-lg\"\n                />\n                {isEditing && (\n                  <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center\">\n                    <CameraIcon className=\"w-8 h-8 text-white\" />\n                  </div>\n                )}\n              </div>\n              \n              {isEditing && (\n                <div className=\"mt-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    رابط الصورة الشخصية\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={formData.avatar_url}\n                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}\n                    placeholder=\"https://example.com/avatar.jpg\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm\"\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Profile Information */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"space-y-6\">\n              {/* Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الاسم الكامل\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                    placeholder=\"أدخل اسمك الكامل\"\n                  />\n                ) : (\n                  <div className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                    <UserIcon className=\"w-5 h-5 text-gray-400 ml-3\" />\n                    <span className=\"text-gray-900\">{formData.name || 'غير محدد'}</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Email */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                ) : (\n                  <div className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                    <EnvelopeIcon className=\"w-5 h-5 text-gray-400 ml-3\" />\n                    <span className=\"text-gray-900\">{formData.email || 'غير محدد'}</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Access Code */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  كود الوصول\n                </label>\n                <div className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                  <IdentificationIcon className=\"w-5 h-5 text-gray-400 ml-3\" />\n                  <span className=\"text-gray-900 font-mono flex-1\">\n                    {showAccessCode ? ((user as any).access_code || user.accessCode) : '••••••••'}\n                  </span>\n                  <button\n                    onClick={() => setShowAccessCode(!showAccessCode)}\n                    className=\"p-1 text-gray-400 hover:text-gray-600 ml-2\"\n                  >\n                    {showAccessCode ? (\n                      <EyeSlashIcon className=\"w-4 h-4\" />\n                    ) : (\n                      <EyeIcon className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                  <button\n                    onClick={copyAccessCode}\n                    className=\"px-2 py-1 text-xs bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors\"\n                  >\n                    نسخ\n                  </button>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  استخدم هذا الكود لتسجيل الدخول\n                </p>\n              </div>\n\n              {/* Join Date */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  تاريخ الانضمام\n                </label>\n                <div className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                  <CalendarIcon className=\"w-5 h-5 text-gray-400 ml-3\" />\n                  <span className=\"text-gray-900\">\n                    {new Date(studentStats.joinDate).toLocaleDateString('ar-SA', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric'\n                    })}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Statistics */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        className=\"bg-white rounded-lg p-6 shadow-sm border\"\n      >\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">إحصائياتي</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-blue-600 mb-1\">\n              {studentStats.totalCourses}\n            </div>\n            <div className=\"text-sm text-blue-800\">إجمالي الكورسات</div>\n          </div>\n\n          <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-green-600 mb-1\">\n              {studentStats.completedCourses}\n            </div>\n            <div className=\"text-sm text-green-800\">كورسات مكتملة</div>\n          </div>\n\n          <div className=\"text-center p-4 bg-yellow-50 rounded-lg\">\n            <div className=\"text-2xl font-bold text-yellow-600 mb-1\">\n              {studentStats.certificates}\n            </div>\n            <div className=\"text-sm text-yellow-800\">شهادات حاصل عليها</div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Account Settings */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white rounded-lg p-6 shadow-sm border\"\n      >\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">إعدادات الحساب</h2>\n        \n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-900\">حالة الحساب</h3>\n              <p className=\"text-xs text-gray-500\">حسابك نشط ويمكنك الوصول لجميع الكورسات</p>\n            </div>\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              نشط\n            </span>\n          </div>\n\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-900\">الإشعارات</h3>\n              <p className=\"text-xs text-gray-500\">تلقي إشعارات حول التحديثات والكورسات الجديدة</p>\n            </div>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input type=\"checkbox\" className=\"sr-only peer\" defaultChecked />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"></div>\n            </label>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default EditableStudentProfile;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,SAAS,CACTC,YAAY,CACZC,kBAAkB,CAClBC,YAAY,CACZC,UAAU,CACVC,OAAO,CACPC,YAAY,KACP,6BAA6B,CAGpC,OAASC,QAAQ,KAAQ,uBAAuB,CAChD,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAYxC,KAAM,CAAAC,sBAAuC,CAAGC,IAAA,EAAc,KAAAC,eAAA,IAAb,CAAEC,IAAK,CAAC,CAAAF,IAAA,CACvD,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2B,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAkB,CACxD6B,IAAI,CAAEP,IAAI,CAACO,IAAI,EAAI,EAAE,CACrBC,KAAK,CAAER,IAAI,CAACQ,KAAK,EAAI,EAAE,CACvBC,UAAU,CAAGT,IAAI,CAASS,UAAU,EAAI,EAC1C,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,CAC/CoC,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAGjB,IAAI,CAASkB,UAAU,IAAAnB,eAAA,CAAIC,IAAI,CAACmB,SAAS,UAAApB,eAAA,iBAAdA,eAAA,CAAgBqB,WAAW,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACD,WAAW,CAAC,CAChG,CAAC,CAAC,CAEFzC,SAAS,CAAC,IAAM,CACd2C,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACtB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAAsB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,KAAAC,gBAAA,CACF;AACA,KAAM,CAAEC,IAAI,CAAEC,WAAY,CAAC,CAAG,KAAM,CAAAlC,QAAQ,CACzCmC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE5B,IAAI,CAAC6B,EAAE,CAAC,CAE5B;AACA,KAAM,CAAEL,IAAI,CAAER,YAAa,CAAC,CAAG,KAAM,CAAAzB,QAAQ,CAC1CmC,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE5B,IAAI,CAAC6B,EAAE,CAAC,CAE5B,KAAM,CAAAf,YAAY,CAAG,CAAAW,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEK,MAAM,GAAI,CAAC,CAC7C,KAAM,CAAAf,gBAAgB,CAAG,CAAAU,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEM,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAK,GAAG,CAAC,CAACH,MAAM,GAAI,CAAC,CACjF,KAAM,CAAAI,iBAAiB,CAAG,CAAAlB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEc,MAAM,GAAI,CAAC,CAEnDjB,eAAe,CAAC,CACdC,YAAY,CACZC,gBAAgB,CAChBC,YAAY,CAAEkB,iBAAiB,CAC/BjB,QAAQ,CAAGjB,IAAI,CAASkB,UAAU,IAAAK,gBAAA,CAAIvB,IAAI,CAACmB,SAAS,UAAAI,gBAAA,iBAAdA,gBAAA,CAAgBH,WAAW,CAAC,CAAC,GAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACD,WAAW,CAAC,CAChG,CAAC,CAAC,CACJ,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAE,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAAChC,QAAQ,CAACE,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAAE,CACzB9C,KAAK,CAAC2C,KAAK,CAAC,aAAa,CAAC,CAC1B,OACF,CAEA,GAAI9B,QAAQ,CAACG,KAAK,EAAI,CAAC+B,YAAY,CAAClC,QAAQ,CAACG,KAAK,CAAC,CAAE,CACnDhB,KAAK,CAAC2C,KAAK,CAAC,4BAA4B,CAAC,CACzC,OACF,CAEA,GAAI,CACF/B,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAAEoB,IAAI,CAAEW,KAAM,CAAC,CAAG,KAAM,CAAA5C,QAAQ,CACnCmC,IAAI,CAAC,UAAU,CAAC,CAChBc,MAAM,CAAC,CACNjC,IAAI,CAAEF,QAAQ,CAACE,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAC1B9B,KAAK,CAAEH,QAAQ,CAACG,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAI,IAAI,CACpC7B,UAAU,CAAEJ,QAAQ,CAACI,UAAU,CAAC6B,IAAI,CAAC,CAAC,EAAI,IAAI,CAC9CG,UAAU,CAAE,GAAI,CAAApB,IAAI,CAAC,CAAC,CAACD,WAAW,CAAC,CACrC,CAAC,CAAC,CACDQ,EAAE,CAAC,IAAI,CAAE5B,IAAI,CAAC6B,EAAE,CAAC,CACjBF,MAAM,CAAC,CAAC,CACRe,MAAM,CAAC,CAAC,CAEX,GAAIP,KAAK,CAAE,KAAM,CAAAA,KAAK,CAEtB;AACA,KAAM,CAAAQ,cAAc,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAI,IAAI,CAAC,CACjF,KAAM,CAAAC,cAAc,CAAG,CAAE,GAAGL,cAAc,CAAE,GAAGnB,IAAK,CAAC,CACrDsB,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAEL,IAAI,CAACM,SAAS,CAACF,cAAc,CAAC,CAAC,CAEtExD,KAAK,CAAC2D,OAAO,CAAC,6BAA6B,CAAC,CAC5CjD,YAAY,CAAC,KAAK,CAAC,CAEnB;AACAkD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C3C,KAAK,CAAC2C,KAAK,CAAC,2BAA2B,CAAC,CAC1C,CAAC,OAAS,CACR/B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmD,YAAY,CAAGA,CAAA,GAAM,CACzBjD,WAAW,CAAC,CACVC,IAAI,CAAEP,IAAI,CAACO,IAAI,EAAI,EAAE,CACrBC,KAAK,CAAER,IAAI,CAACQ,KAAK,EAAI,EAAE,CACvBC,UAAU,CAAGT,IAAI,CAASS,UAAU,EAAI,EAC1C,CAAC,CAAC,CACFP,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,KAAM,CAAAqC,YAAY,CAAI/B,KAAa,EAAK,CACtC,KAAM,CAAAgD,UAAU,CAAG,4BAA4B,CAC/C,MAAO,CAAAA,UAAU,CAACC,IAAI,CAACjD,KAAK,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAkD,cAAc,CAAGA,CAAA,GAAM,CAC3BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAE7D,IAAI,CAAS8D,WAAW,EAAI9D,IAAI,CAAC+D,UAAU,CAAC,CAC3EvE,KAAK,CAAC2D,OAAO,CAAC,mBAAmB,CAAC,CACpC,CAAC,CAED,KAAM,CAAAa,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI3D,QAAQ,CAACI,UAAU,CAAE,CACvB,MAAO,CAAAJ,QAAQ,CAACI,UAAU,CAC5B,CACA,MAAO,oCAAoCwD,kBAAkB,CAAC5D,QAAQ,CAACE,IAAI,CAAC,uCAAuC,CACrH,CAAC,CAED,mBACEX,KAAA,QAAKsE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBvE,KAAA,CAAChB,MAAM,CAACwF,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BL,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpDvE,KAAA,QAAKsE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDzE,IAAA,OAAIwE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qEAAY,CAAI,CAAC,CACjE,CAAClE,SAAS,cACTL,KAAA,WACE6E,OAAO,CAAEA,CAAA,GAAMvE,YAAY,CAAC,IAAI,CAAE,CAClCgE,SAAS,CAAC,wHAAwH,CAAAC,QAAA,eAElIzE,IAAA,CAACZ,UAAU,EAACoF,SAAS,CAAC,cAAc,CAAE,CAAC,iCAEzC,EAAQ,CAAC,cAETtE,KAAA,QAAKsE,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CvE,KAAA,WACE6E,OAAO,CAAEpC,UAAW,CACpBqC,QAAQ,CAAEvE,OAAQ,CAClB+D,SAAS,CAAC,wIAAwI,CAAAC,QAAA,eAElJzE,IAAA,CAACX,SAAS,EAACmF,SAAS,CAAC,cAAc,CAAE,CAAC,CACrC/D,OAAO,CAAG,eAAe,CAAG,KAAK,EAC5B,CAAC,cACTP,KAAA,WACE6E,OAAO,CAAElB,YAAa,CACtBmB,QAAQ,CAAEvE,OAAQ,CAClB+D,SAAS,CAAC,kHAAkH,CAAAC,QAAA,eAE5HzE,IAAA,CAACV,SAAS,EAACkF,SAAS,CAAC,cAAc,CAAE,CAAC,iCAExC,EAAQ,CAAC,EACN,CACN,EACE,CAAC,cAENtE,KAAA,QAAKsE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDzE,IAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BvE,KAAA,QAAKsE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvE,KAAA,QAAKsE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCzE,IAAA,QACEiF,GAAG,CAAEX,YAAY,CAAC,CAAE,CACpBY,GAAG,CAAEvE,QAAQ,CAACE,IAAK,CACnB2D,SAAS,CAAC,6EAA6E,CACxF,CAAC,CACDjE,SAAS,eACRP,IAAA,QAAKwE,SAAS,CAAC,uFAAuF,CAAAC,QAAA,cACpGzE,IAAA,CAACN,UAAU,EAAC8E,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CACN,EACE,CAAC,CAELjE,SAAS,eACRL,KAAA,QAAKsE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzE,IAAA,UAAOwE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0GAEhE,CAAO,CAAC,cACRzE,IAAA,UACEmF,IAAI,CAAC,KAAK,CACVC,KAAK,CAAEzE,QAAQ,CAACI,UAAW,CAC3BsE,QAAQ,CAAG/C,CAAC,EAAK1B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEI,UAAU,CAAEuB,CAAC,CAACgD,MAAM,CAACF,KAAM,CAAC,CAAE,CAC1EG,WAAW,CAAC,gCAAgC,CAC5Cf,SAAS,CAAC,yHAAyH,CACpI,CAAC,EACC,CACN,EACE,CAAC,CACH,CAAC,cAGNxE,IAAA,QAAKwE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BvE,KAAA,QAAKsE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBvE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,UAAOwE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,qEAEhE,CAAO,CAAC,CACPlE,SAAS,cACRP,IAAA,UACEmF,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEzE,QAAQ,CAACE,IAAK,CACrBwE,QAAQ,CAAG/C,CAAC,EAAK1B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEE,IAAI,CAAEyB,CAAC,CAACgD,MAAM,CAACF,KAAM,CAAC,CAAE,CACpEZ,SAAS,CAAC,iHAAiH,CAC3He,WAAW,CAAC,wFAAkB,CAC/B,CAAC,cAEFrF,KAAA,QAAKsE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DzE,IAAA,CAACb,QAAQ,EAACqF,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACnDxE,IAAA,SAAMwE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE9D,QAAQ,CAACE,IAAI,EAAI,UAAU,CAAO,CAAC,EACjE,CACN,EACE,CAAC,cAGNX,KAAA,QAAAuE,QAAA,eACEzE,IAAA,UAAOwE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,mGAEhE,CAAO,CAAC,CACPlE,SAAS,cACRP,IAAA,UACEmF,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEzE,QAAQ,CAACG,KAAM,CACtBuE,QAAQ,CAAG/C,CAAC,EAAK1B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEG,KAAK,CAAEwB,CAAC,CAACgD,MAAM,CAACF,KAAM,CAAC,CAAE,CACrEZ,SAAS,CAAC,iHAAiH,CAC3He,WAAW,CAAC,mBAAmB,CAChC,CAAC,cAEFrF,KAAA,QAAKsE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DzE,IAAA,CAACT,YAAY,EAACiF,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACvDxE,IAAA,SAAMwE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE9D,QAAQ,CAACG,KAAK,EAAI,UAAU,CAAO,CAAC,EAClE,CACN,EACE,CAAC,cAGNZ,KAAA,QAAAuE,QAAA,eACEzE,IAAA,UAAOwE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,yDAEhE,CAAO,CAAC,cACRvE,KAAA,QAAKsE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DzE,IAAA,CAACR,kBAAkB,EAACgF,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC7DxE,IAAA,SAAMwE,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC7CzD,cAAc,CAAKV,IAAI,CAAS8D,WAAW,EAAI9D,IAAI,CAAC+D,UAAU,CAAI,UAAU,CACzE,CAAC,cACPrE,IAAA,WACE+E,OAAO,CAAEA,CAAA,GAAM9D,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAClDwD,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAErDzD,cAAc,cACbhB,IAAA,CAACJ,YAAY,EAAC4E,SAAS,CAAC,SAAS,CAAE,CAAC,cAEpCxE,IAAA,CAACL,OAAO,EAAC6E,SAAS,CAAC,SAAS,CAAE,CAC/B,CACK,CAAC,cACTxE,IAAA,WACE+E,OAAO,CAAEf,cAAe,CACxBQ,SAAS,CAAC,4FAA4F,CAAAC,QAAA,CACvG,oBAED,CAAQ,CAAC,EACN,CAAC,cACNzE,IAAA,MAAGwE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kKAE1C,CAAG,CAAC,EACD,CAAC,cAGNvE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,UAAOwE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,iFAEhE,CAAO,CAAC,cACRvE,KAAA,QAAKsE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DzE,IAAA,CAACP,YAAY,EAAC+E,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACvDxE,IAAA,SAAMwE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5B,GAAI,CAAA9C,IAAI,CAACT,YAAY,CAACK,QAAQ,CAAC,CAACiE,kBAAkB,CAAC,OAAO,CAAE,CAC3DC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACE,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACI,CAAC,cAGbzF,KAAA,CAAChB,MAAM,CAACwF,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Be,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BrB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpDzE,IAAA,OAAIwE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wDAAS,CAAI,CAAC,cAErEvE,KAAA,QAAKsE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvE,KAAA,QAAKsE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDzE,IAAA,QAAKwE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDvD,YAAY,CAACE,YAAY,CACvB,CAAC,cACNpB,IAAA,QAAKwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uFAAe,CAAK,CAAC,EACzD,CAAC,cAENvE,KAAA,QAAKsE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzE,IAAA,QAAKwE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CACpDvD,YAAY,CAACG,gBAAgB,CAC3B,CAAC,cACNrB,IAAA,QAAKwE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,2EAAa,CAAK,CAAC,EACxD,CAAC,cAENvE,KAAA,QAAKsE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtDzE,IAAA,QAAKwE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CACrDvD,YAAY,CAACI,YAAY,CACvB,CAAC,cACNtB,IAAA,QAAKwE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,8FAAiB,CAAK,CAAC,EAC7D,CAAC,EACH,CAAC,EACI,CAAC,cAGbvE,KAAA,CAAChB,MAAM,CAACwF,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Be,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BrB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpDzE,IAAA,OAAIwE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cAE1EvE,KAAA,QAAKsE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvE,KAAA,QAAKsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAC1EvE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,OAAIwE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,+DAAW,CAAI,CAAC,cAClEzE,IAAA,MAAGwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6MAAsC,CAAG,CAAC,EAC5E,CAAC,cACNzE,IAAA,SAAMwE,SAAS,CAAC,qGAAqG,CAAAC,QAAA,CAAC,oBAEtH,CAAM,CAAC,EACJ,CAAC,cAENvE,KAAA,QAAKsE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAC1EvE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,OAAIwE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,wDAAS,CAAI,CAAC,cAChEzE,IAAA,MAAGwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iPAA4C,CAAG,CAAC,EAClF,CAAC,cACNvE,KAAA,UAAOsE,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eACjEzE,IAAA,UAAOmF,IAAI,CAAC,UAAU,CAACX,SAAS,CAAC,cAAc,CAACsB,cAAc,MAAE,CAAC,cACjE9F,IAAA,QAAKwE,SAAS,CAAC,+XAA+X,CAAM,CAAC,EAChZ,CAAC,EACL,CAAC,EACH,CAAC,EACI,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAArE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}