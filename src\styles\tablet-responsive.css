/* Tablet Responsive Styles for ALaa Academy Platform */
/* Optimized for 768px - 1024px screens */

/* Base tablet styles */
@media (min-width: 768px) and (max-width: 1024px) {
  
  /* Layout adjustments */
  .container {
    max-width: 100%;
    padding: 0 1rem;
  }

  /* Admin Dashboard Sidebar */
  .admin-sidebar {
    width: 240px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 40;
  }

  .admin-sidebar.collapsed {
    width: 64px;
  }

  .admin-main-content {
    margin-left: 240px;
    transition: margin-left 0.3s ease;
  }

  .admin-main-content.sidebar-collapsed {
    margin-left: 64px;
  }

  /* Student Dashboard */
  .student-sidebar {
    width: 220px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 40;
  }

  .student-main-content {
    margin-left: 220px;
    padding: 1rem;
  }

  /* Navigation improvements */
  .nav-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .nav-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* Cards and content */
  .card {
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
  }

  .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .card-body {
    padding: 1.25rem;
  }

  /* Grid layouts */
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  /* Tables */
  .table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
  }

  .table {
    min-width: 100%;
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.75rem;
    white-space: nowrap;
  }

  /* Forms */
  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
  }

  .form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Buttons */
  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
  }

  .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  .btn-secondary {
    background-color: #6b7280;
    color: white;
    border: 1px solid #6b7280;
  }

  .btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
  }

  /* Video Player */
  .video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    border-radius: 0.75rem;
    overflow: hidden;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  /* Course Grid */
  .course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .course-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Modal adjustments */
  .modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
  }

  .modal-backdrop {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-content {
    position: relative;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 32rem;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Stats cards */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }

  .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .stat-value {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* Progress bars */
  .progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.5s ease;
  }

  /* Touch-friendly elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved spacing for touch */
  .btn + .btn {
    margin-left: 0.75rem;
  }

  /* Search and filters */
  .search-container {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
  }

  .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    color: #9ca3af;
  }

  /* Responsive text */
  .text-responsive {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .heading-responsive {
    font-size: 1.5rem;
    line-height: 1.3;
    font-weight: 600;
  }

  /* Improved scrollbars */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Animation improvements */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .slide-in {
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }
}

/* Portrait tablet specific styles */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .admin-sidebar,
  .student-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-sidebar.open,
  .student-sidebar.open {
    transform: translateX(0);
  }

  .admin-main-content,
  .student-main-content {
    margin-left: 0;
    padding: 1rem;
  }

  .mobile-menu-button {
    display: block;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 50;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .course-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Landscape tablet specific styles */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .course-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .video-container {
    padding-bottom: 42.25%; /* Adjust for landscape */
  }
}
