import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { User as FirebaseUser } from 'firebase/auth';

// Services
import { authService } from './services/authService';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import LoginPage from './pages/LoginPage';
import AdminDashboard from './pages/admin/AdminDashboard';
import StudentDashboard from './pages/student/StudentDashboard';
import AIAssistant from './components/AIAssistant/AIAssistant';
import SessionWarning from './components/common/SessionWarning';

// Types
import { User } from './types';

// Styles
import './App.css';

// Helper functions to check user types
const isAdmin = (user: User): boolean => {
  return user.role === 'admin';
};

const isStudent = (user: User): boolean => {
  return user.role === 'student';
};

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);

  useEffect(() => {
    const unsubscribe = authService.onAuthStateChange(async (firebaseUser) => {
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        // User is signed in, get user data
        try {
          // This would be implemented based on your auth logic
          // For now, we'll handle it in the login components
        } catch (error) {
          console.error('Error getting user data:', error);
        }
      } else {
        setUser(null);
      }
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleLogin = (userData: User) => {
    setUser(userData);
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="App min-h-screen bg-gray-50" dir="rtl">
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route 
            path="/login" 
            element={
              user ? (
                <Navigate to={user.role === 'admin' ? '/admin' : '/student'} replace />
              ) : (
                <LoginPage onLogin={handleLogin} />
              )
            } 
          />
          
          {/* Protected Admin Routes */}
          <Route
            path="/admin/*"
            element={
              user && isAdmin(user) ? (
                <AdminDashboard user={user as any} onLogout={handleLogout} />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          {/* Protected Student Routes */}
          <Route
            path="/student/*"
            element={
              user && isStudent(user) ? (
                <StudentDashboard user={user as any} onLogout={handleLogout} />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
          
          {/* Default Route */}
          <Route 
            path="/" 
            element={
              <Navigate to={
                user 
                  ? (user.role === 'admin' ? '/admin' : '/student')
                  : '/login'
              } replace />
            } 
          />
        </Routes>
        
        {/* AI Assistant - Available for students */}
        {user && user.role === 'student' && <AIAssistant />}
        
        {/* Toast Notifications */}
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl'
            },
            success: {
              style: {
                background: '#10b981',
              },
            },
            error: {
              style: {
                background: '#ef4444',
              },
            },
          }}
        />

        {/* Session Warning */}
        <SessionWarning />
      </Router>
    </div>
  );
}

export default App;
