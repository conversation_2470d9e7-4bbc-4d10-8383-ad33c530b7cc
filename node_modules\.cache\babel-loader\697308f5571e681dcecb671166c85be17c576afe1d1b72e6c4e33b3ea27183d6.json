{"ast": null, "code": "export const mockCourses=[{id:'138ec959-163c-4ce9-9ad3-2b0597bdcfea',title:'أساسيات البرمجة بـ JavaScript',description:'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',categoryId:'f47ac10b-58cc-4372-a567-0e02b2c3d479',instructorId:'a1b2c3d4-e5f6-7890-abcd-ef1234567890',thumbnailUrl:'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',price:299,duration:25,level:'beginner',videos:[],pdfs:[],quizzes:[],enrolledStudents:15,isActive:true,createdAt:new Date('2024-01-01'),updatedAt:new Date('2024-01-01')},{id:'41c61139-b980-4f5d-8036-c006b1ed6bc2',title:'تطوير المواقع بـ React',description:'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',categoryId:'f47ac10b-58cc-4372-a567-0e02b2c3d479',instructorId:'a1b2c3d4-e5f6-7890-abcd-ef1234567890',thumbnailUrl:'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',price:499,duration:40,level:'intermediate',videos:[],pdfs:[],quizzes:[],enrolledStudents:12,isActive:true,createdAt:new Date('2024-01-15'),updatedAt:new Date('2024-01-15')},{id:'3de6741f-0444-439e-a0f2-5f169c4d1970',title:'تطوير تطبيقات الهاتف بـ React Native',description:'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',categoryId:'b2c3d4e5-f6a7-8901-bcde-f23456789012',instructorId:'a1b2c3d4-e5f6-7890-abcd-ef1234567890',thumbnailUrl:'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',price:699,duration:50,level:'intermediate',videos:[],pdfs:[],quizzes:[],enrolledStudents:8,isActive:true,createdAt:new Date('2024-02-01'),updatedAt:new Date('2024-02-01')}];export const mockVideos=[{id:'c0628c50-5bc5-4d9e-adce-63e135037949',courseId:'138ec959-163c-4ce9-9ad3-2b0597bdcfea',title:'مقدمة في JavaScript',videoUrl:'https://example.com/video1.mp4',duration:15,orderIndex:1,isActive:true,createdAt:new Date('2024-01-01')},{id:'d1739d61-6cd6-5e0f-bede-74e246148a5a',courseId:'138ec959-163c-4ce9-9ad3-2b0597bdcfea',title:'المتغيرات والثوابت',videoUrl:'https://example.com/video2.mp4',duration:20,orderIndex:2,isActive:true,createdAt:new Date('2024-01-01')}];export const mockQuizzes=[{id:'5d3d6ffe-45ea-424c-87b5-328e5a191bae',courseId:'138ec959-163c-4ce9-9ad3-2b0597bdcfea',title:'اختبار JavaScript الأساسي',description:'اختبار لقياس فهمك لأساسيات JavaScript',questions:[{id:'e2f4a6b8-c9d1-4e3f-a5b7-c8d9e0f1a2b3',question:'ما هو JavaScript؟',type:'multiple-choice',options:['لغة برمجة','قاعدة بيانات','نظام تشغيل','متصفح'],correctAnswer:0,points:1}],passingScore:70,timeLimit:30,attempts:3,isActive:true,createdAt:new Date('2024-01-01')}];export const mockCertificates=[{id:'f3a5b7c9-d1e3-4f5a-b7c9-d1e3f5a7b9c1',studentId:'b4d6f8a0-c2e4-4f6a-b8c0-e2f4a6b8c0d2',courseId:'138ec959-163c-4ce9-9ad3-2b0597bdcfea',templateUrl:'/templates/cert-template.pdf',certificateUrl:'/certificates/cert-001.pdf',issuedAt:new Date('2024-01-20'),verificationCode:'CERT-2024-001'}];", "map": {"version": 3, "names": ["mockCourses", "id", "title", "description", "categoryId", "instructorId", "thumbnailUrl", "price", "duration", "level", "videos", "pdfs", "quizzes", "enrolledStudents", "isActive", "createdAt", "Date", "updatedAt", "mockVideos", "courseId", "videoUrl", "orderIndex", "mockQuizzes", "questions", "question", "type", "options", "<PERSON><PERSON><PERSON><PERSON>", "points", "passingScore", "timeLimit", "attempts", "mockCertificates", "studentId", "templateUrl", "certificateUrl", "issuedAt", "verificationCode"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/data/mockCourses.ts"], "sourcesContent": ["import { Course, Video, Quiz, Certificate } from '../types';\n\nexport const mockCourses: Course[] = [\n  {\n    id: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',\n    title: 'أساسيات البرمجة بـ JavaScript',\n    description: 'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',\n    categoryId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',\n    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',\n    price: 299,\n    duration: 25,\n    level: 'beginner',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 15,\n    isActive: true,\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-01')\n  },\n  {\n    id: '41c61139-b980-4f5d-8036-c006b1ed6bc2',\n    title: 'تطوير المواقع بـ React',\n    description: 'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',\n    categoryId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',\n    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n    price: 499,\n    duration: 40,\n    level: 'intermediate',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 12,\n    isActive: true,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    id: '3de6741f-0444-439e-a0f2-5f169c4d1970',\n    title: 'تطوير تطبيقات الهاتف بـ React Native',\n    description: 'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',\n    categoryId: 'b2c3d4e5-f6a7-8901-bcde-f23456789012',\n    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',\n    thumbnailUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',\n    price: 699,\n    duration: 50,\n    level: 'intermediate',\n    videos: [],\n    pdfs: [],\n    quizzes: [],\n    enrolledStudents: 8,\n    isActive: true,\n    createdAt: new Date('2024-02-01'),\n    updatedAt: new Date('2024-02-01')\n  }\n];\n\nexport const mockVideos: Video[] = [\n  {\n    id: 'c0628c50-5bc5-4d9e-adce-63e135037949',\n    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',\n    title: 'مقدمة في JavaScript',\n    videoUrl: 'https://example.com/video1.mp4',\n    duration: 15,\n    orderIndex: 1,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: 'd1739d61-6cd6-5e0f-bede-74e246148a5a',\n    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',\n    title: 'المتغيرات والثوابت',\n    videoUrl: 'https://example.com/video2.mp4',\n    duration: 20,\n    orderIndex: 2,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockQuizzes: Quiz[] = [\n  {\n    id: '5d3d6ffe-45ea-424c-87b5-328e5a191bae',\n    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',\n    title: 'اختبار JavaScript الأساسي',\n    description: 'اختبار لقياس فهمك لأساسيات JavaScript',\n    questions: [\n      {\n        id: 'e2f4a6b8-c9d1-4e3f-a5b7-c8d9e0f1a2b3',\n        question: 'ما هو JavaScript؟',\n        type: 'multiple-choice',\n        options: ['لغة برمجة', 'قاعدة بيانات', 'نظام تشغيل', 'متصفح'],\n        correctAnswer: 0,\n        points: 1\n      }\n    ],\n    passingScore: 70,\n    timeLimit: 30,\n    attempts: 3,\n    isActive: true,\n    createdAt: new Date('2024-01-01')\n  }\n];\n\nexport const mockCertificates: Certificate[] = [\n  {\n    id: 'f3a5b7c9-d1e3-4f5a-b7c9-d1e3f5a7b9c1',\n    studentId: 'b4d6f8a0-c2e4-4f6a-b8c0-e2f4a6b8c0d2',\n    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',\n    templateUrl: '/templates/cert-template.pdf',\n    certificateUrl: '/certificates/cert-001.pdf',\n    issuedAt: new Date('2024-01-20'),\n    verificationCode: 'CERT-2024-001'\n  }\n];\n"], "mappings": "AAEA,MAAO,MAAM,CAAAA,WAAqB,CAAG,CACnC,CACEC,EAAE,CAAE,sCAAsC,CAC1CC,KAAK,CAAE,+BAA+B,CACtCC,WAAW,CAAE,oEAAoE,CACjFC,UAAU,CAAE,sCAAsC,CAClDC,YAAY,CAAE,sCAAsC,CACpDC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,EAAE,CACpBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,sCAAsC,CAC1CC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,4DAA4D,CACzEC,UAAU,CAAE,sCAAsC,CAClDC,YAAY,CAAE,sCAAsC,CACpDC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,cAAc,CACrBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,EAAE,CACpBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,sCAAsC,CAC1CC,KAAK,CAAE,sCAAsC,CAC7CC,WAAW,CAAE,wDAAwD,CACrEC,UAAU,CAAE,sCAAsC,CAClDC,YAAY,CAAE,sCAAsC,CACpDC,YAAY,CAAE,mFAAmF,CACjGC,KAAK,CAAE,GAAG,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,cAAc,CACrBC,MAAM,CAAE,EAAE,CACVC,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,EAAE,CACXC,gBAAgB,CAAE,CAAC,CACnBC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAAC,CACjCC,SAAS,CAAE,GAAI,CAAAD,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,UAAmB,CAAG,CACjC,CACEjB,EAAE,CAAE,sCAAsC,CAC1CkB,QAAQ,CAAE,sCAAsC,CAChDjB,KAAK,CAAE,qBAAqB,CAC5BkB,QAAQ,CAAE,gCAAgC,CAC1CZ,QAAQ,CAAE,EAAE,CACZa,UAAU,CAAE,CAAC,CACbP,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACD,CACEf,EAAE,CAAE,sCAAsC,CAC1CkB,QAAQ,CAAE,sCAAsC,CAChDjB,KAAK,CAAE,oBAAoB,CAC3BkB,QAAQ,CAAE,gCAAgC,CAC1CZ,QAAQ,CAAE,EAAE,CACZa,UAAU,CAAE,CAAC,CACbP,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAM,WAAmB,CAAG,CACjC,CACErB,EAAE,CAAE,sCAAsC,CAC1CkB,QAAQ,CAAE,sCAAsC,CAChDjB,KAAK,CAAE,2BAA2B,CAClCC,WAAW,CAAE,uCAAuC,CACpDoB,SAAS,CAAE,CACT,CACEtB,EAAE,CAAE,sCAAsC,CAC1CuB,QAAQ,CAAE,mBAAmB,CAC7BC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CAAC,WAAW,CAAE,cAAc,CAAE,YAAY,CAAE,OAAO,CAAC,CAC7DC,aAAa,CAAE,CAAC,CAChBC,MAAM,CAAE,CACV,CAAC,CACF,CACDC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,CAAC,CACXjB,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,YAAY,CAClC,CAAC,CACF,CAED,MAAO,MAAM,CAAAgB,gBAA+B,CAAG,CAC7C,CACE/B,EAAE,CAAE,sCAAsC,CAC1CgC,SAAS,CAAE,sCAAsC,CACjDd,QAAQ,CAAE,sCAAsC,CAChDe,WAAW,CAAE,8BAA8B,CAC3CC,cAAc,CAAE,4BAA4B,CAC5CC,QAAQ,CAAE,GAAI,CAAApB,IAAI,CAAC,YAAY,CAAC,CAChCqB,gBAAgB,CAAE,eACpB,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}