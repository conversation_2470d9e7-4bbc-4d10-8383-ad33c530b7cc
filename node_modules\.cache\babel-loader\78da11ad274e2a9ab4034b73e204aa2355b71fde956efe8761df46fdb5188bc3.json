{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import{motion}from'framer-motion';import{PlayIcon,LockClosedIcon,CheckCircleIcon,ClockIcon,BookOpenIcon,StarIcon,EyeIcon,TrophyIcon}from'@heroicons/react/24/outline';import{supabaseService}from'../../services/supabaseService';import{toast}from'react-hot-toast';import LoadingSpinner from'../common/LoadingSpinner';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProgressiveCourseViewer=()=>{var _getVideoProgress,_getVideoProgress2;const{courseId}=useParams();const navigate=useNavigate();const[course,setCourse]=useState(null);const[videos,setVideos]=useState([]);const[videoProgress,setVideoProgress]=useState([]);const[enrollment,setEnrollment]=useState(null);const[selectedVideo,setSelectedVideo]=useState('');const[loading,setLoading]=useState(true);const[currentStudent,setCurrentStudent]=useState(null);useEffect(()=>{// Get current student from localStorage\nconst studentData=localStorage.getItem('currentStudent');if(studentData){setCurrentStudent(JSON.parse(studentData));}else{navigate('/student/login');return;}if(courseId){loadCourseData();}},[courseId,navigate]);const loadCourseData=async()=>{if(!courseId||!currentStudent)return;try{setLoading(true);// Load course details\nconst courseData=await supabaseService.getCourseById(courseId);setCourse(courseData);// Load videos\nconst videosData=await supabaseService.getVideosByCourseId(courseId);const sortedVideos=(videosData===null||videosData===void 0?void 0:videosData.sort((a,b)=>a.order_index-b.order_index))||[];setVideos(sortedVideos);// Load student enrollment\nconst enrollmentData=await loadStudentEnrollment();setEnrollment(enrollmentData);// Load video progress\nconst progressData=await loadVideoProgress();setVideoProgress(progressData);// Set first available video\nif(sortedVideos.length>0){const firstAvailableVideo=getFirstAvailableVideo(sortedVideos,progressData);setSelectedVideo((firstAvailableVideo===null||firstAvailableVideo===void 0?void 0:firstAvailableVideo.id)||sortedVideos[0].id);}}catch(error){console.error('Error loading course data:',error);toast.error('فشل في تحميل بيانات الكورس');}finally{setLoading(false);}};const loadStudentEnrollment=async()=>{if(!courseId||!currentStudent)return null;try{const{data,error}=await supabaseService.supabase.from('student_enrollments').select('*').eq('student_id',currentStudent.id).eq('course_id',courseId).single();if(error&&error.code!=='PGRST116')throw error;return data;}catch(error){console.error('Error loading enrollment:',error);return null;}};const loadVideoProgress=async()=>{if(!currentStudent)return[];try{const{data,error}=await supabaseService.supabase.from('video_progress').select('*').eq('student_id',currentStudent.id);if(error)throw error;return data||[];}catch(error){console.error('Error loading video progress:',error);return[];}};const getFirstAvailableVideo=(videos,progress)=>{// Find the first video that is not completed\nfor(const video of videos){const videoProgress=progress.find(p=>p.video_id===video.id);if(!videoProgress||!videoProgress.completed){return video;}}// If all videos are completed, return the last one\nreturn videos[videos.length-1];};const isVideoUnlocked=(video,index)=>{// First video is always unlocked\nif(index===0)return true;// Free videos are always unlocked\nif(video.is_free)return true;// Check if previous video is completed\nconst previousVideo=videos[index-1];if(!previousVideo)return true;const previousProgress=videoProgress.find(p=>p.video_id===previousVideo.id);return(previousProgress===null||previousProgress===void 0?void 0:previousProgress.completed)||false;};const markVideoAsCompleted=async videoId=>{if(!currentStudent)return;try{// Check if progress record exists\nconst existingProgress=videoProgress.find(p=>p.video_id===videoId);if(existingProgress){var _videos$find;// Update existing record\nconst{error}=await supabaseService.supabase.from('video_progress').update({completed:true,completed_at:new Date().toISOString(),watched_duration:((_videos$find=videos.find(v=>v.id===videoId))===null||_videos$find===void 0?void 0:_videos$find.duration)||0}).eq('id',existingProgress.id);if(error)throw error;}else{var _videos$find2;// Create new progress record\nconst{error}=await supabaseService.supabase.from('video_progress').insert([{student_id:currentStudent.id,video_id:videoId,watched_duration:((_videos$find2=videos.find(v=>v.id===videoId))===null||_videos$find2===void 0?void 0:_videos$find2.duration)||0,completed:true,completed_at:new Date().toISOString()}]);if(error)throw error;}// Reload progress\nconst updatedProgress=await loadVideoProgress();setVideoProgress(updatedProgress);// Update course progress\nawait updateCourseProgress();toast.success('تم إكمال الفيديو بنجاح!');}catch(error){console.error('Error marking video as completed:',error);toast.error('فشل في حفظ التقدم');}};const updateCourseProgress=async()=>{if(!enrollment||!currentStudent)return;try{const completedVideos=videoProgress.filter(p=>p.completed).length;const totalVideos=videos.length;const progressPercentage=totalVideos>0?Math.round(completedVideos/totalVideos*100):0;const{error}=await supabaseService.supabase.from('student_enrollments').update({progress:progressPercentage,completed_at:progressPercentage===100?new Date().toISOString():null}).eq('id',enrollment.id);if(error)throw error;// Update local enrollment state\nsetEnrollment(prev=>prev?{...prev,progress:progressPercentage}:null);}catch(error){console.error('Error updating course progress:',error);}};const handleVideoSelect=(video,index)=>{if(!isVideoUnlocked(video,index)){toast.error('يجب إكمال الفيديو السابق أولاً');return;}setSelectedVideo(video.id);};const getVideoProgress=videoId=>{return videoProgress.find(p=>p.video_id===videoId);};const formatDuration=seconds=>{const hours=Math.floor(seconds/3600);const minutes=Math.floor(seconds%3600/60);const secs=seconds%60;if(hours>0){return`${hours}:${minutes.toString().padStart(2,'0')}:${secs.toString().padStart(2,'0')}`;}return`${minutes}:${secs.toString().padStart(2,'0')}`;};const currentVideo=videos.find(v=>v.id===selectedVideo);const currentVideoIndex=videos.findIndex(v=>v.id===selectedVideo);const completedVideosCount=videoProgress.filter(p=>p.completed).length;const totalVideosCount=videos.length;const overallProgress=totalVideosCount>0?Math.round(completedVideosCount/totalVideosCount*100):0;if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{});}if(!course){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633 \\u063A\\u064A\\u0631 \\u0645\\u0648\\u062C\\u0648\\u062F\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-start justify-between\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900 mb-2\",children:course.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:course.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645 \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-medium text-primary-600\",children:[overallProgress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(motion.div,{className:\"bg-primary-600 h-2 rounded-full\",initial:{width:0},animate:{width:`${overallProgress}%`},transition:{duration:0.5}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mt-2 text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[completedVideosCount,\" \\u0645\\u0646 \",totalVideosCount,\" \\u0641\\u064A\\u062F\\u064A\\u0648 \\u0645\\u0643\\u062A\\u0645\\u0644\"]}),overallProgress===100&&/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center text-green-600\",children:[/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-4 h-4 ml-1\"}),\"\\u0645\\u0628\\u0631\\u0648\\u0643! \\u0623\\u0643\\u0645\\u0644\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"]})]})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm border overflow-hidden\",children:currentVideo?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-video bg-black\",children:/*#__PURE__*/_jsx(\"video\",{controls:true,className:\"w-full h-full\",src:currentVideo.video_url,onEnded:()=>markVideoAsCompleted(currentVideo.id),children:\"\\u0645\\u062A\\u0635\\u0641\\u062D\\u0643 \\u0644\\u0627 \\u064A\\u062F\\u0639\\u0645 \\u062A\\u0634\\u063A\\u064A\\u0644 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"},currentVideo.id)}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-2\",children:currentVideo.title}),currentVideo.description&&/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:currentVideo.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 ml-1\"}),formatDuration(currentVideo.duration)]}),/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4 ml-1\"}),\"\\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \",currentVideoIndex+1]}),currentVideo.is_free&&/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:\"\\u0645\\u062C\\u0627\\u0646\\u064A\"})]})]}),((_getVideoProgress=getVideoProgress(currentVideo.id))===null||_getVideoProgress===void 0?void 0:_getVideoProgress.completed)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-green-600\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-6 h-6 ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:\"\\u0645\\u0643\\u062A\\u0645\\u0644\"})]})]}),!((_getVideoProgress2=getVideoProgress(currentVideo.id))!==null&&_getVideoProgress2!==void 0&&_getVideoProgress2.completed)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>markVideoAsCompleted(currentVideo.id),className:\"w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors\",children:\"\\u062A\\u0645 \\u0625\\u0643\\u0645\\u0627\\u0644 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"})]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"aspect-video flex items-center justify-center bg-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u0644\\u0628\\u062F\\u0621 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-b\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500 mt-1\",children:[completedVideosCount,\" \\u0645\\u0646 \",totalVideosCount,\" \\u0645\\u0643\\u062A\\u0645\\u0644\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"divide-y divide-gray-200 max-h-96 overflow-y-auto\",children:videos.map((video,index)=>{var _getVideoProgress3;const isUnlocked=isVideoUnlocked(video,index);const isCompleted=(_getVideoProgress3=getVideoProgress(video.id))===null||_getVideoProgress3===void 0?void 0:_getVideoProgress3.completed;const isSelected=selectedVideo===video.id;return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:index*0.1},className:`p-4 cursor-pointer transition-colors ${isSelected?'bg-primary-50 border-l-4 border-primary-600':'hover:bg-gray-50'} ${!isUnlocked?'opacity-50':''}`,onClick:()=>handleVideoSelect(video,index),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:isCompleted?/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-5 h-5 text-green-600\"})}):isUnlocked?/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(PlayIcon,{className:\"w-5 h-5 text-primary-600\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(LockClosedIcon,{className:\"w-5 h-5 text-gray-400\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse mb-1\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded\",children:[\"#\",index+1]}),video.is_free&&/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-yellow-500\"})]}),/*#__PURE__*/_jsx(\"h4\",{className:`text-sm font-medium truncate ${isSelected?'text-primary-900':'text-gray-900'}`,children:video.title}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse mt-1 text-xs text-gray-500\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-3 h-3\"}),/*#__PURE__*/_jsx(\"span\",{children:formatDuration(video.duration)}),!isUnlocked&&/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"\\u0645\\u0642\\u0641\\u0644\"})]})]})]})},video.id);})})]})})]})]});};export default ProgressiveCourseViewer;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "PlayIcon", "LockClosedIcon", "CheckCircleIcon", "ClockIcon", "BookOpenIcon", "StarIcon", "EyeIcon", "TrophyIcon", "supabaseService", "toast", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProgressiveCourseViewer", "_getVideoProgress", "_getVideoProgress2", "courseId", "navigate", "course", "setCourse", "videos", "setVideos", "videoProgress", "setVideoProgress", "enrollment", "setEnrollment", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "loading", "setLoading", "currentStudent", "setCurrentStudent", "studentData", "localStorage", "getItem", "JSON", "parse", "loadCourseData", "courseData", "getCourseById", "videosData", "getVideosByCourseId", "sortedVideos", "sort", "a", "b", "order_index", "enrollmentData", "loadStudentEnrollment", "progressData", "loadVideoProgress", "length", "firstAvailableVideo", "getFirstAvailableVideo", "id", "error", "console", "data", "supabase", "from", "select", "eq", "single", "code", "progress", "video", "find", "p", "video_id", "completed", "isVideoUnlocked", "index", "is_free", "previousVideo", "previousProgress", "markVideoAsCompleted", "videoId", "existingProgress", "_videos$find", "update", "completed_at", "Date", "toISOString", "watched_duration", "v", "duration", "_videos$find2", "insert", "student_id", "updatedProgress", "updateCourseProgress", "success", "completedVideos", "filter", "totalVideos", "progressPercentage", "Math", "round", "prev", "handleVideoSelect", "getVideoProgress", "formatDuration", "seconds", "hours", "floor", "minutes", "secs", "toString", "padStart", "currentVideo", "currentVideoIndex", "findIndex", "completedVideosCount", "totalVideosCount", "overallProgress", "className", "children", "title", "description", "div", "initial", "width", "animate", "transition", "controls", "src", "video_url", "onEnded", "onClick", "map", "_getVideoProgress3", "isUnlocked", "isCompleted", "isSelected", "opacity", "x", "delay"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/ProgressiveCourseViewer.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  PlayIcon,\n  LockClosedIcon,\n  CheckCircleIcon,\n  ClockIcon,\n  BookOpenIcon,\n  StarIcon,\n  ArrowLeftIcon,\n  ArrowRightIcon,\n  EyeIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline';\nimport { supabaseService } from '../../services/supabaseService';\nimport { toast } from 'react-hot-toast';\nimport LoadingSpinner from '../common/LoadingSpinner';\n\ninterface Video {\n  id: string;\n  course_id: string;\n  title: string;\n  description?: string;\n  video_url: string;\n  duration: number;\n  order_index: number;\n  is_free: boolean;\n  created_at: string;\n}\n\ninterface Course {\n  id: string;\n  title: string;\n  description: string;\n  thumbnail_url?: string;\n  price: number;\n  duration_hours: number;\n  level: string;\n  is_active: boolean;\n}\n\ninterface VideoProgress {\n  id: string;\n  student_id?: string;\n  user_id?: number;\n  video_id: string;\n  watched_duration: number;\n  total_duration?: number;\n  completed: boolean;\n  completed_at?: string;\n  last_watched_at?: string;\n}\n\ninterface StudentEnrollment {\n  id: string;\n  student_id: string;\n  course_id: string;\n  progress: number;\n  enrolled_at: string;\n  completed_at?: string;\n}\n\nconst ProgressiveCourseViewer: React.FC = () => {\n  const { courseId } = useParams<{ courseId: string }>();\n  const navigate = useNavigate();\n  \n  const [course, setCourse] = useState<Course | null>(null);\n  const [videos, setVideos] = useState<Video[]>([]);\n  const [videoProgress, setVideoProgress] = useState<VideoProgress[]>([]);\n  const [enrollment, setEnrollment] = useState<StudentEnrollment | null>(null);\n  const [selectedVideo, setSelectedVideo] = useState<string>('');\n  const [loading, setLoading] = useState(true);\n  const [currentStudent, setCurrentStudent] = useState<any>(null);\n\n  useEffect(() => {\n    // Get current student from localStorage\n    const studentData = localStorage.getItem('currentStudent');\n    if (studentData) {\n      setCurrentStudent(JSON.parse(studentData));\n    } else {\n      navigate('/student/login');\n      return;\n    }\n\n    if (courseId) {\n      loadCourseData();\n    }\n  }, [courseId, navigate]);\n\n  const loadCourseData = async () => {\n    if (!courseId || !currentStudent) return;\n\n    try {\n      setLoading(true);\n      \n      // Load course details\n      const courseData = await supabaseService.getCourseById(courseId);\n      setCourse(courseData);\n\n      // Load videos\n      const videosData = await supabaseService.getVideosByCourseId(courseId);\n      const sortedVideos = videosData?.sort((a, b) => a.order_index - b.order_index) || [];\n      setVideos(sortedVideos);\n\n      // Load student enrollment\n      const enrollmentData = await loadStudentEnrollment();\n      setEnrollment(enrollmentData);\n\n      // Load video progress\n      const progressData = await loadVideoProgress();\n      setVideoProgress(progressData);\n\n      // Set first available video\n      if (sortedVideos.length > 0) {\n        const firstAvailableVideo = getFirstAvailableVideo(sortedVideos, progressData);\n        setSelectedVideo(firstAvailableVideo?.id || sortedVideos[0].id);\n      }\n\n    } catch (error) {\n      console.error('Error loading course data:', error);\n      toast.error('فشل في تحميل بيانات الكورس');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStudentEnrollment = async () => {\n    if (!courseId || !currentStudent) return null;\n\n    try {\n      const { data, error } = await supabaseService.supabase\n        .from('student_enrollments')\n        .select('*')\n        .eq('student_id', currentStudent.id)\n        .eq('course_id', courseId)\n        .single();\n\n      if (error && error.code !== 'PGRST116') throw error;\n      return data;\n    } catch (error) {\n      console.error('Error loading enrollment:', error);\n      return null;\n    }\n  };\n\n  const loadVideoProgress = async () => {\n    if (!currentStudent) return [];\n\n    try {\n      const { data, error } = await supabaseService.supabase\n        .from('video_progress')\n        .select('*')\n        .eq('student_id', currentStudent.id);\n\n      if (error) throw error;\n      return data || [];\n    } catch (error) {\n      console.error('Error loading video progress:', error);\n      return [];\n    }\n  };\n\n  const getFirstAvailableVideo = (videos: Video[], progress: VideoProgress[]) => {\n    // Find the first video that is not completed\n    for (const video of videos) {\n      const videoProgress = progress.find(p => p.video_id === video.id);\n      if (!videoProgress || !videoProgress.completed) {\n        return video;\n      }\n    }\n    // If all videos are completed, return the last one\n    return videos[videos.length - 1];\n  };\n\n  const isVideoUnlocked = (video: Video, index: number) => {\n    // First video is always unlocked\n    if (index === 0) return true;\n    \n    // Free videos are always unlocked\n    if (video.is_free) return true;\n\n    // Check if previous video is completed\n    const previousVideo = videos[index - 1];\n    if (!previousVideo) return true;\n\n    const previousProgress = videoProgress.find(p => p.video_id === previousVideo.id);\n    return previousProgress?.completed || false;\n  };\n\n  const markVideoAsCompleted = async (videoId: string) => {\n    if (!currentStudent) return;\n\n    try {\n      // Check if progress record exists\n      const existingProgress = videoProgress.find(p => p.video_id === videoId);\n\n      if (existingProgress) {\n        // Update existing record\n        const { error } = await supabaseService.supabase\n          .from('video_progress')\n          .update({\n            completed: true,\n            completed_at: new Date().toISOString(),\n            watched_duration: videos.find(v => v.id === videoId)?.duration || 0\n          })\n          .eq('id', existingProgress.id);\n\n        if (error) throw error;\n      } else {\n        // Create new progress record\n        const { error } = await supabaseService.supabase\n          .from('video_progress')\n          .insert([{\n            student_id: currentStudent.id,\n            video_id: videoId,\n            watched_duration: videos.find(v => v.id === videoId)?.duration || 0,\n            completed: true,\n            completed_at: new Date().toISOString()\n          }]);\n\n        if (error) throw error;\n      }\n\n      // Reload progress\n      const updatedProgress = await loadVideoProgress();\n      setVideoProgress(updatedProgress);\n\n      // Update course progress\n      await updateCourseProgress();\n\n      toast.success('تم إكمال الفيديو بنجاح!');\n    } catch (error) {\n      console.error('Error marking video as completed:', error);\n      toast.error('فشل في حفظ التقدم');\n    }\n  };\n\n  const updateCourseProgress = async () => {\n    if (!enrollment || !currentStudent) return;\n\n    try {\n      const completedVideos = videoProgress.filter(p => p.completed).length;\n      const totalVideos = videos.length;\n      const progressPercentage = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;\n\n      const { error } = await supabaseService.supabase\n        .from('student_enrollments')\n        .update({\n          progress: progressPercentage,\n          completed_at: progressPercentage === 100 ? new Date().toISOString() : null\n        })\n        .eq('id', enrollment.id);\n\n      if (error) throw error;\n\n      // Update local enrollment state\n      setEnrollment(prev => prev ? { ...prev, progress: progressPercentage } : null);\n    } catch (error) {\n      console.error('Error updating course progress:', error);\n    }\n  };\n\n  const handleVideoSelect = (video: Video, index: number) => {\n    if (!isVideoUnlocked(video, index)) {\n      toast.error('يجب إكمال الفيديو السابق أولاً');\n      return;\n    }\n    setSelectedVideo(video.id);\n  };\n\n  const getVideoProgress = (videoId: string) => {\n    return videoProgress.find(p => p.video_id === videoId);\n  };\n\n  const formatDuration = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const currentVideo = videos.find(v => v.id === selectedVideo);\n  const currentVideoIndex = videos.findIndex(v => v.id === selectedVideo);\n  const completedVideosCount = videoProgress.filter(p => p.completed).length;\n  const totalVideosCount = videos.length;\n  const overallProgress = totalVideosCount > 0 ? Math.round((completedVideosCount / totalVideosCount) * 100) : 0;\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  if (!course) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <BookOpenIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">الكورس غير موجود</h3>\n          <p className=\"text-gray-500\">لم يتم العثور على الكورس المطلوب</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Course Header */}\n      <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{course.title}</h1>\n            <p className=\"text-gray-600 mb-4\">{course.description}</p>\n            \n            {/* Progress Bar */}\n            <div className=\"mb-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">التقدم الإجمالي</span>\n                <span className=\"text-sm font-medium text-primary-600\">{overallProgress}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <motion.div\n                  className=\"bg-primary-600 h-2 rounded-full\"\n                  initial={{ width: 0 }}\n                  animate={{ width: `${overallProgress}%` }}\n                  transition={{ duration: 0.5 }}\n                />\n              </div>\n              <div className=\"flex items-center justify-between mt-2 text-sm text-gray-500\">\n                <span>{completedVideosCount} من {totalVideosCount} فيديو مكتمل</span>\n                {overallProgress === 100 && (\n                  <span className=\"flex items-center text-green-600\">\n                    <TrophyIcon className=\"w-4 h-4 ml-1\" />\n                    مبروك! أكملت الكورس\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Video Player */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n            {currentVideo ? (\n              <>\n                {/* Video */}\n                <div className=\"aspect-video bg-black\">\n                  <video\n                    key={currentVideo.id}\n                    controls\n                    className=\"w-full h-full\"\n                    src={currentVideo.video_url}\n                    onEnded={() => markVideoAsCompleted(currentVideo.id)}\n                  >\n                    متصفحك لا يدعم تشغيل الفيديو\n                  </video>\n                </div>\n\n                {/* Video Info */}\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h2 className=\"text-xl font-bold text-gray-900 mb-2\">\n                        {currentVideo.title}\n                      </h2>\n                      {currentVideo.description && (\n                        <p className=\"text-gray-600 mb-4\">{currentVideo.description}</p>\n                      )}\n                      <div className=\"flex items-center space-x-4 space-x-reverse text-sm text-gray-500\">\n                        <span className=\"flex items-center\">\n                          <ClockIcon className=\"w-4 h-4 ml-1\" />\n                          {formatDuration(currentVideo.duration)}\n                        </span>\n                        <span className=\"flex items-center\">\n                          <EyeIcon className=\"w-4 h-4 ml-1\" />\n                          المرحلة {currentVideoIndex + 1}\n                        </span>\n                        {currentVideo.is_free && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                            مجاني\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                    \n                    {getVideoProgress(currentVideo.id)?.completed && (\n                      <div className=\"flex items-center text-green-600\">\n                        <CheckCircleIcon className=\"w-6 h-6 ml-2\" />\n                        <span className=\"text-sm font-medium\">مكتمل</span>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Mark as Complete Button */}\n                  {!getVideoProgress(currentVideo.id)?.completed && (\n                    <button\n                      onClick={() => markVideoAsCompleted(currentVideo.id)}\n                      className=\"w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors\"\n                    >\n                      تم إكمال الفيديو\n                    </button>\n                  )}\n                </div>\n              </>\n            ) : (\n              <div className=\"aspect-video flex items-center justify-center bg-gray-100\">\n                <div className=\"text-center\">\n                  <PlayIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500\">اختر فيديو لبدء المشاهدة</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Videos List */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white rounded-lg shadow-sm border\">\n            <div className=\"p-4 border-b\">\n              <h3 className=\"text-lg font-medium text-gray-900\">قائمة الفيديوهات</h3>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                {completedVideosCount} من {totalVideosCount} مكتمل\n              </p>\n            </div>\n            \n            <div className=\"divide-y divide-gray-200 max-h-96 overflow-y-auto\">\n              {videos.map((video, index) => {\n                const isUnlocked = isVideoUnlocked(video, index);\n                const isCompleted = getVideoProgress(video.id)?.completed;\n                const isSelected = selectedVideo === video.id;\n\n                return (\n                  <motion.div\n                    key={video.id}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className={`p-4 cursor-pointer transition-colors ${\n                      isSelected ? 'bg-primary-50 border-l-4 border-primary-600' : 'hover:bg-gray-50'\n                    } ${!isUnlocked ? 'opacity-50' : ''}`}\n                    onClick={() => handleVideoSelect(video, index)}\n                  >\n                    <div className=\"flex items-start space-x-3 space-x-reverse\">\n                      <div className=\"flex-shrink-0\">\n                        {isCompleted ? (\n                          <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                            <CheckCircleIcon className=\"w-5 h-5 text-green-600\" />\n                          </div>\n                        ) : isUnlocked ? (\n                          <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                            <PlayIcon className=\"w-5 h-5 text-primary-600\" />\n                          </div>\n                        ) : (\n                          <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\">\n                            <LockClosedIcon className=\"w-5 h-5 text-gray-400\" />\n                          </div>\n                        )}\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 space-x-reverse mb-1\">\n                          <span className=\"text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                            #{index + 1}\n                          </span>\n                          {video.is_free && (\n                            <StarIcon className=\"w-4 h-4 text-yellow-500\" />\n                          )}\n                        </div>\n                        <h4 className={`text-sm font-medium truncate ${\n                          isSelected ? 'text-primary-900' : 'text-gray-900'\n                        }`}>\n                          {video.title}\n                        </h4>\n                        <div className=\"flex items-center space-x-2 space-x-reverse mt-1 text-xs text-gray-500\">\n                          <ClockIcon className=\"w-3 h-3\" />\n                          <span>{formatDuration(video.duration)}</span>\n                          {!isUnlocked && (\n                            <span className=\"text-red-500\">مقفل</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProgressiveCourseViewer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,cAAc,CACdC,eAAe,CACfC,SAAS,CACTC,YAAY,CACZC,QAAQ,CAGRC,OAAO,CACPC,UAAU,KACL,6BAA6B,CACpC,OAASC,eAAe,KAAQ,gCAAgC,CAChE,OAASC,KAAK,KAAQ,iBAAiB,CACvC,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA8CtD,KAAM,CAAAC,uBAAiC,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CAAAC,kBAAA,CAC9C,KAAM,CAAEC,QAAS,CAAC,CAAGvB,SAAS,CAAuB,CAAC,CACtD,KAAM,CAAAwB,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACwB,MAAM,CAAEC,SAAS,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CACzD,KAAM,CAAC6B,MAAM,CAAEC,SAAS,CAAC,CAAG9B,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAkB,EAAE,CAAC,CACvE,KAAM,CAACiC,UAAU,CAAEC,aAAa,CAAC,CAAGlC,QAAQ,CAA2B,IAAI,CAAC,CAC5E,KAAM,CAACmC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpC,QAAQ,CAAS,EAAE,CAAC,CAC9D,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuC,cAAc,CAAEC,iBAAiB,CAAC,CAAGxC,QAAQ,CAAM,IAAI,CAAC,CAE/DC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAwC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAC1D,GAAIF,WAAW,CAAE,CACfD,iBAAiB,CAACI,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAAC,CAC5C,CAAC,IAAM,CACLf,QAAQ,CAAC,gBAAgB,CAAC,CAC1B,OACF,CAEA,GAAID,QAAQ,CAAE,CACZqB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACrB,QAAQ,CAAEC,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAoB,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAACrB,QAAQ,EAAI,CAACc,cAAc,CAAE,OAElC,GAAI,CACFD,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAS,UAAU,CAAG,KAAM,CAAAlC,eAAe,CAACmC,aAAa,CAACvB,QAAQ,CAAC,CAChEG,SAAS,CAACmB,UAAU,CAAC,CAErB;AACA,KAAM,CAAAE,UAAU,CAAG,KAAM,CAAApC,eAAe,CAACqC,mBAAmB,CAACzB,QAAQ,CAAC,CACtE,KAAM,CAAA0B,YAAY,CAAG,CAAAF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACE,WAAW,CAAGD,CAAC,CAACC,WAAW,CAAC,GAAI,EAAE,CACpFzB,SAAS,CAACqB,YAAY,CAAC,CAEvB;AACA,KAAM,CAAAK,cAAc,CAAG,KAAM,CAAAC,qBAAqB,CAAC,CAAC,CACpDvB,aAAa,CAACsB,cAAc,CAAC,CAE7B;AACA,KAAM,CAAAE,YAAY,CAAG,KAAM,CAAAC,iBAAiB,CAAC,CAAC,CAC9C3B,gBAAgB,CAAC0B,YAAY,CAAC,CAE9B;AACA,GAAIP,YAAY,CAACS,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAC,mBAAmB,CAAGC,sBAAsB,CAACX,YAAY,CAAEO,YAAY,CAAC,CAC9EtB,gBAAgB,CAAC,CAAAyB,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEE,EAAE,GAAIZ,YAAY,CAAC,CAAC,CAAC,CAACY,EAAE,CAAC,CACjE,CAEF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDlD,KAAK,CAACkD,KAAK,CAAC,4BAA4B,CAAC,CAC3C,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmB,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CAAChC,QAAQ,EAAI,CAACc,cAAc,CAAE,MAAO,KAAI,CAE7C,GAAI,CACF,KAAM,CAAE2B,IAAI,CAAEF,KAAM,CAAC,CAAG,KAAM,CAAAnD,eAAe,CAACsD,QAAQ,CACnDC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE/B,cAAc,CAACwB,EAAE,CAAC,CACnCO,EAAE,CAAC,WAAW,CAAE7C,QAAQ,CAAC,CACzB8C,MAAM,CAAC,CAAC,CAEX,GAAIP,KAAK,EAAIA,KAAK,CAACQ,IAAI,GAAK,UAAU,CAAE,KAAM,CAAAR,KAAK,CACnD,MAAO,CAAAE,IAAI,CACb,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,MAAO,KAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAL,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACpB,cAAc,CAAE,MAAO,EAAE,CAE9B,GAAI,CACF,KAAM,CAAE2B,IAAI,CAAEF,KAAM,CAAC,CAAG,KAAM,CAAAnD,eAAe,CAACsD,QAAQ,CACnDC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE/B,cAAc,CAACwB,EAAE,CAAC,CAEtC,GAAIC,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAE,IAAI,EAAI,EAAE,CACnB,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,EAAE,CACX,CACF,CAAC,CAED,KAAM,CAAAF,sBAAsB,CAAGA,CAACjC,MAAe,CAAE4C,QAAyB,GAAK,CAC7E;AACA,IAAK,KAAM,CAAAC,KAAK,GAAI,CAAA7C,MAAM,CAAE,CAC1B,KAAM,CAAAE,aAAa,CAAG0C,QAAQ,CAACE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAKH,KAAK,CAACX,EAAE,CAAC,CACjE,GAAI,CAAChC,aAAa,EAAI,CAACA,aAAa,CAAC+C,SAAS,CAAE,CAC9C,MAAO,CAAAJ,KAAK,CACd,CACF,CACA;AACA,MAAO,CAAA7C,MAAM,CAACA,MAAM,CAAC+B,MAAM,CAAG,CAAC,CAAC,CAClC,CAAC,CAED,KAAM,CAAAmB,eAAe,CAAGA,CAACL,KAAY,CAAEM,KAAa,GAAK,CACvD;AACA,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAI,CAE5B;AACA,GAAIN,KAAK,CAACO,OAAO,CAAE,MAAO,KAAI,CAE9B;AACA,KAAM,CAAAC,aAAa,CAAGrD,MAAM,CAACmD,KAAK,CAAG,CAAC,CAAC,CACvC,GAAI,CAACE,aAAa,CAAE,MAAO,KAAI,CAE/B,KAAM,CAAAC,gBAAgB,CAAGpD,aAAa,CAAC4C,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAKK,aAAa,CAACnB,EAAE,CAAC,CACjF,MAAO,CAAAoB,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEL,SAAS,GAAI,KAAK,CAC7C,CAAC,CAED,KAAM,CAAAM,oBAAoB,CAAG,KAAO,CAAAC,OAAe,EAAK,CACtD,GAAI,CAAC9C,cAAc,CAAE,OAErB,GAAI,CACF;AACA,KAAM,CAAA+C,gBAAgB,CAAGvD,aAAa,CAAC4C,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAKQ,OAAO,CAAC,CAExE,GAAIC,gBAAgB,CAAE,KAAAC,YAAA,CACpB;AACA,KAAM,CAAEvB,KAAM,CAAC,CAAG,KAAM,CAAAnD,eAAe,CAACsD,QAAQ,CAC7CC,IAAI,CAAC,gBAAgB,CAAC,CACtBoB,MAAM,CAAC,CACNV,SAAS,CAAE,IAAI,CACfW,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtCC,gBAAgB,CAAE,EAAAL,YAAA,CAAA1D,MAAM,CAAC8C,IAAI,CAACkB,CAAC,EAAIA,CAAC,CAAC9B,EAAE,GAAKsB,OAAO,CAAC,UAAAE,YAAA,iBAAlCA,YAAA,CAAoCO,QAAQ,GAAI,CACpE,CAAC,CAAC,CACDxB,EAAE,CAAC,IAAI,CAAEgB,gBAAgB,CAACvB,EAAE,CAAC,CAEhC,GAAIC,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAAC,IAAM,KAAA+B,aAAA,CACL;AACA,KAAM,CAAE/B,KAAM,CAAC,CAAG,KAAM,CAAAnD,eAAe,CAACsD,QAAQ,CAC7CC,IAAI,CAAC,gBAAgB,CAAC,CACtB4B,MAAM,CAAC,CAAC,CACPC,UAAU,CAAE1D,cAAc,CAACwB,EAAE,CAC7Bc,QAAQ,CAAEQ,OAAO,CACjBO,gBAAgB,CAAE,EAAAG,aAAA,CAAAlE,MAAM,CAAC8C,IAAI,CAACkB,CAAC,EAAIA,CAAC,CAAC9B,EAAE,GAAKsB,OAAO,CAAC,UAAAU,aAAA,iBAAlCA,aAAA,CAAoCD,QAAQ,GAAI,CAAC,CACnEhB,SAAS,CAAE,IAAI,CACfW,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACvC,CAAC,CAAC,CAAC,CAEL,GAAI3B,KAAK,CAAE,KAAM,CAAAA,KAAK,CACxB,CAEA;AACA,KAAM,CAAAkC,eAAe,CAAG,KAAM,CAAAvC,iBAAiB,CAAC,CAAC,CACjD3B,gBAAgB,CAACkE,eAAe,CAAC,CAEjC;AACA,KAAM,CAAAC,oBAAoB,CAAC,CAAC,CAE5BrF,KAAK,CAACsF,OAAO,CAAC,yBAAyB,CAAC,CAC1C,CAAE,MAAOpC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDlD,KAAK,CAACkD,KAAK,CAAC,mBAAmB,CAAC,CAClC,CACF,CAAC,CAED,KAAM,CAAAmC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAAClE,UAAU,EAAI,CAACM,cAAc,CAAE,OAEpC,GAAI,CACF,KAAM,CAAA8D,eAAe,CAAGtE,aAAa,CAACuE,MAAM,CAAC1B,CAAC,EAAIA,CAAC,CAACE,SAAS,CAAC,CAAClB,MAAM,CACrE,KAAM,CAAA2C,WAAW,CAAG1E,MAAM,CAAC+B,MAAM,CACjC,KAAM,CAAA4C,kBAAkB,CAAGD,WAAW,CAAG,CAAC,CAAGE,IAAI,CAACC,KAAK,CAAEL,eAAe,CAAGE,WAAW,CAAI,GAAG,CAAC,CAAG,CAAC,CAElG,KAAM,CAAEvC,KAAM,CAAC,CAAG,KAAM,CAAAnD,eAAe,CAACsD,QAAQ,CAC7CC,IAAI,CAAC,qBAAqB,CAAC,CAC3BoB,MAAM,CAAC,CACNf,QAAQ,CAAE+B,kBAAkB,CAC5Bf,YAAY,CAAEe,kBAAkB,GAAK,GAAG,CAAG,GAAI,CAAAd,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAG,IACxE,CAAC,CAAC,CACDrB,EAAE,CAAC,IAAI,CAAErC,UAAU,CAAC8B,EAAE,CAAC,CAE1B,GAAIC,KAAK,CAAE,KAAM,CAAAA,KAAK,CAEtB;AACA9B,aAAa,CAACyE,IAAI,EAAIA,IAAI,CAAG,CAAE,GAAGA,IAAI,CAAElC,QAAQ,CAAE+B,kBAAmB,CAAC,CAAG,IAAI,CAAC,CAChF,CAAE,MAAOxC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAA4C,iBAAiB,CAAGA,CAAClC,KAAY,CAAEM,KAAa,GAAK,CACzD,GAAI,CAACD,eAAe,CAACL,KAAK,CAAEM,KAAK,CAAC,CAAE,CAClClE,KAAK,CAACkD,KAAK,CAAC,gCAAgC,CAAC,CAC7C,OACF,CACA5B,gBAAgB,CAACsC,KAAK,CAACX,EAAE,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA8C,gBAAgB,CAAIxB,OAAe,EAAK,CAC5C,MAAO,CAAAtD,aAAa,CAAC4C,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAKQ,OAAO,CAAC,CACxD,CAAC,CAED,KAAM,CAAAyB,cAAc,CAAIC,OAAe,EAAK,CAC1C,KAAM,CAAAC,KAAK,CAAGP,IAAI,CAACQ,KAAK,CAACF,OAAO,CAAG,IAAI,CAAC,CACxC,KAAM,CAAAG,OAAO,CAAGT,IAAI,CAACQ,KAAK,CAAEF,OAAO,CAAG,IAAI,CAAI,EAAE,CAAC,CACjD,KAAM,CAAAI,IAAI,CAAGJ,OAAO,CAAG,EAAE,CAEzB,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,MAAO,GAAGA,KAAK,IAAIE,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9F,CACA,MAAO,GAAGH,OAAO,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzD,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGzF,MAAM,CAAC8C,IAAI,CAACkB,CAAC,EAAIA,CAAC,CAAC9B,EAAE,GAAK5B,aAAa,CAAC,CAC7D,KAAM,CAAAoF,iBAAiB,CAAG1F,MAAM,CAAC2F,SAAS,CAAC3B,CAAC,EAAIA,CAAC,CAAC9B,EAAE,GAAK5B,aAAa,CAAC,CACvE,KAAM,CAAAsF,oBAAoB,CAAG1F,aAAa,CAACuE,MAAM,CAAC1B,CAAC,EAAIA,CAAC,CAACE,SAAS,CAAC,CAAClB,MAAM,CAC1E,KAAM,CAAA8D,gBAAgB,CAAG7F,MAAM,CAAC+B,MAAM,CACtC,KAAM,CAAA+D,eAAe,CAAGD,gBAAgB,CAAG,CAAC,CAAGjB,IAAI,CAACC,KAAK,CAAEe,oBAAoB,CAAGC,gBAAgB,CAAI,GAAG,CAAC,CAAG,CAAC,CAE9G,GAAIrF,OAAO,CAAE,CACX,mBAAOpB,IAAA,CAACF,cAAc,GAAE,CAAC,CAC3B,CAEA,GAAI,CAACY,MAAM,CAAE,CACX,mBACEV,IAAA,QAAK2G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD1G,KAAA,QAAKyG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5G,IAAA,CAACR,YAAY,EAACmH,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACjE3G,IAAA,OAAI2G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wFAAgB,CAAI,CAAC,cAC5E5G,IAAA,MAAG2G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yKAAgC,CAAG,CAAC,EAC9D,CAAC,CACH,CAAC,CAEV,CAEA,mBACE1G,KAAA,QAAKyG,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB5G,IAAA,QAAK2G,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvD5G,IAAA,QAAK2G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/C1G,KAAA,QAAKyG,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB5G,IAAA,OAAI2G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAElG,MAAM,CAACmG,KAAK,CAAK,CAAC,cACzE7G,IAAA,MAAG2G,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAElG,MAAM,CAACoG,WAAW,CAAI,CAAC,cAG1D5G,KAAA,QAAKyG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1G,KAAA,QAAKyG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD5G,IAAA,SAAM2G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uFAAe,CAAM,CAAC,cAC1E1G,KAAA,SAAMyG,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAEF,eAAe,CAAC,GAAC,EAAM,CAAC,EAC7E,CAAC,cACN1G,IAAA,QAAK2G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD5G,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTJ,SAAS,CAAC,iCAAiC,CAC3CK,OAAO,CAAE,CAAEC,KAAK,CAAE,CAAE,CAAE,CACtBC,OAAO,CAAE,CAAED,KAAK,CAAE,GAAGP,eAAe,GAAI,CAAE,CAC1CS,UAAU,CAAE,CAAEtC,QAAQ,CAAE,GAAI,CAAE,CAC/B,CAAC,CACC,CAAC,cACN3E,KAAA,QAAKyG,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3E1G,KAAA,SAAA0G,QAAA,EAAOJ,oBAAoB,CAAC,gBAAI,CAACC,gBAAgB,CAAC,gEAAY,EAAM,CAAC,CACpEC,eAAe,GAAK,GAAG,eACtBxG,KAAA,SAAMyG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAChD5G,IAAA,CAACL,UAAU,EAACgH,SAAS,CAAC,cAAc,CAAE,CAAC,sGAEzC,EAAM,CACP,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENzG,KAAA,QAAKyG,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD5G,IAAA,QAAK2G,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5G,IAAA,QAAK2G,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEP,YAAY,cACXnG,KAAA,CAAAE,SAAA,EAAAwG,QAAA,eAEE5G,IAAA,QAAK2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC5G,IAAA,UAEEoH,QAAQ,MACRT,SAAS,CAAC,eAAe,CACzBU,GAAG,CAAEhB,YAAY,CAACiB,SAAU,CAC5BC,OAAO,CAAEA,CAAA,GAAMpD,oBAAoB,CAACkC,YAAY,CAACvD,EAAE,CAAE,CAAA8D,QAAA,CACtD,sJAED,EAPOP,YAAY,CAACvD,EAOb,CAAC,CACL,CAAC,cAGN5C,KAAA,QAAKyG,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClB1G,KAAA,QAAKyG,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD1G,KAAA,QAAKyG,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB5G,IAAA,OAAI2G,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CACjDP,YAAY,CAACQ,KAAK,CACjB,CAAC,CACJR,YAAY,CAACS,WAAW,eACvB9G,IAAA,MAAG2G,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEP,YAAY,CAACS,WAAW,CAAI,CAChE,cACD5G,KAAA,QAAKyG,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAChF1G,KAAA,SAAMyG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5G,IAAA,CAACT,SAAS,EAACoH,SAAS,CAAC,cAAc,CAAE,CAAC,CACrCd,cAAc,CAACQ,YAAY,CAACxB,QAAQ,CAAC,EAClC,CAAC,cACP3E,KAAA,SAAMyG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5G,IAAA,CAACN,OAAO,EAACiH,SAAS,CAAC,cAAc,CAAE,CAAC,8CAC5B,CAACL,iBAAiB,CAAG,CAAC,EAC1B,CAAC,CACND,YAAY,CAACrC,OAAO,eACnBhE,IAAA,SAAM2G,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAAC,gCAElH,CAAM,CACP,EACE,CAAC,EACH,CAAC,CAEL,EAAAtG,iBAAA,CAAAsF,gBAAgB,CAACS,YAAY,CAACvD,EAAE,CAAC,UAAAxC,iBAAA,iBAAjCA,iBAAA,CAAmCuD,SAAS,gBAC3C3D,KAAA,QAAKyG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C5G,IAAA,CAACV,eAAe,EAACqH,SAAS,CAAC,cAAc,CAAE,CAAC,cAC5C3G,IAAA,SAAM2G,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gCAAK,CAAM,CAAC,EAC/C,CACN,EACE,CAAC,CAGL,GAAArG,kBAAA,CAACqF,gBAAgB,CAACS,YAAY,CAACvD,EAAE,CAAC,UAAAvC,kBAAA,WAAjCA,kBAAA,CAAmCsD,SAAS,gBAC5C7D,IAAA,WACEwH,OAAO,CAAEA,CAAA,GAAMrD,oBAAoB,CAACkC,YAAY,CAACvD,EAAE,CAAE,CACrD6D,SAAS,CAAC,8FAA8F,CAAAC,QAAA,CACzG,wFAED,CAAQ,CACT,EACE,CAAC,EACN,CAAC,cAEH5G,IAAA,QAAK2G,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE1G,KAAA,QAAKyG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5G,IAAA,CAACZ,QAAQ,EAACuH,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7D3G,IAAA,MAAG2G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mIAAwB,CAAG,CAAC,EACtD,CAAC,CACH,CACN,CACE,CAAC,CACH,CAAC,cAGN5G,IAAA,QAAK2G,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1G,KAAA,QAAKyG,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD1G,KAAA,QAAKyG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5G,IAAA,OAAI2G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,cACvE1G,KAAA,MAAGyG,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACtCJ,oBAAoB,CAAC,gBAAI,CAACC,gBAAgB,CAAC,iCAC9C,EAAG,CAAC,EACD,CAAC,cAENzG,IAAA,QAAK2G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC/DhG,MAAM,CAAC6G,GAAG,CAAC,CAAChE,KAAK,CAAEM,KAAK,GAAK,KAAA2D,kBAAA,CAC5B,KAAM,CAAAC,UAAU,CAAG7D,eAAe,CAACL,KAAK,CAAEM,KAAK,CAAC,CAChD,KAAM,CAAA6D,WAAW,EAAAF,kBAAA,CAAG9B,gBAAgB,CAACnC,KAAK,CAACX,EAAE,CAAC,UAAA4E,kBAAA,iBAA1BA,kBAAA,CAA4B7D,SAAS,CACzD,KAAM,CAAAgE,UAAU,CAAG3G,aAAa,GAAKuC,KAAK,CAACX,EAAE,CAE7C,mBACE9C,IAAA,CAACb,MAAM,CAAC4H,GAAG,EAETC,OAAO,CAAE,CAAEc,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/Bb,OAAO,CAAE,CAAEY,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BZ,UAAU,CAAE,CAAEa,KAAK,CAAEjE,KAAK,CAAG,GAAI,CAAE,CACnC4C,SAAS,CAAE,wCACTkB,UAAU,CAAG,6CAA6C,CAAG,kBAAkB,IAC7E,CAACF,UAAU,CAAG,YAAY,CAAG,EAAE,EAAG,CACtCH,OAAO,CAAEA,CAAA,GAAM7B,iBAAiB,CAAClC,KAAK,CAAEM,KAAK,CAAE,CAAA6C,QAAA,cAE/C1G,KAAA,QAAKyG,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD5G,IAAA,QAAK2G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BgB,WAAW,cACV5H,IAAA,QAAK2G,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjF5G,IAAA,CAACV,eAAe,EAACqH,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACnD,CAAC,CACJgB,UAAU,cACZ3H,IAAA,QAAK2G,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnF5G,IAAA,CAACZ,QAAQ,EAACuH,SAAS,CAAC,0BAA0B,CAAE,CAAC,CAC9C,CAAC,cAEN3G,IAAA,QAAK2G,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF5G,IAAA,CAACX,cAAc,EAACsH,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACjD,CACN,CACE,CAAC,cAENzG,KAAA,QAAKyG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B1G,KAAA,QAAKyG,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/D1G,KAAA,SAAMyG,SAAS,CAAC,iEAAiE,CAAAC,QAAA,EAAC,GAC/E,CAAC7C,KAAK,CAAG,CAAC,EACP,CAAC,CACNN,KAAK,CAACO,OAAO,eACZhE,IAAA,CAACP,QAAQ,EAACkH,SAAS,CAAC,yBAAyB,CAAE,CAChD,EACE,CAAC,cACN3G,IAAA,OAAI2G,SAAS,CAAE,gCACbkB,UAAU,CAAG,kBAAkB,CAAG,eAAe,EAChD,CAAAjB,QAAA,CACAnD,KAAK,CAACoD,KAAK,CACV,CAAC,cACL3G,KAAA,QAAKyG,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eACrF5G,IAAA,CAACT,SAAS,EAACoH,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC3G,IAAA,SAAA4G,QAAA,CAAOf,cAAc,CAACpC,KAAK,CAACoB,QAAQ,CAAC,CAAO,CAAC,CAC5C,CAAC8C,UAAU,eACV3H,IAAA,SAAM2G,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAC1C,EACE,CAAC,EACH,CAAC,EACH,CAAC,EAhDDnD,KAAK,CAACX,EAiDD,CAAC,CAEjB,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}