{"ast": null, "code": "import{useState,useEffect}from'react';const SESSION_KEY='alaa_academy_session';const SESSION_TIMEOUT=24*60*60*1000;// 24 hours in milliseconds\nexport const useSessionPersistence=()=>{const[session,setSession]=useState(null);const[isLoading,setIsLoading]=useState(true);useEffect(()=>{loadSession();// Set up activity tracking\nconst handleActivity=()=>{updateLastActivity();};// Track user activity\nconst events=['mousedown','mousemove','keypress','scroll','touchstart','click'];events.forEach(event=>{document.addEventListener(event,handleActivity,true);});// Check session validity every minute\nconst sessionCheckInterval=setInterval(checkSessionValidity,60000);return()=>{events.forEach(event=>{document.removeEventListener(event,handleActivity,true);});clearInterval(sessionCheckInterval);};},[]);const loadSession=()=>{try{const savedSession=localStorage.getItem(SESSION_KEY);if(savedSession){const sessionData=JSON.parse(savedSession);// Check if session is still valid\nconst now=new Date().getTime();const lastActivity=new Date(sessionData.lastActivity).getTime();if(now-lastActivity<SESSION_TIMEOUT){setSession(sessionData);updateLastActivity();}else{// Session expired\nclearSession();}}}catch(error){console.error('Error loading session:',error);clearSession();}finally{setIsLoading(false);}};const saveSession=(user,userType)=>{const sessionData={user,userType,loginTime:new Date().toISOString(),lastActivity:new Date().toISOString()};try{localStorage.setItem(SESSION_KEY,JSON.stringify(sessionData));setSession(sessionData);// Also save user data separately for backward compatibility\nif(userType==='student'){localStorage.setItem('currentStudent',JSON.stringify(user));}else{localStorage.setItem('currentAdmin',JSON.stringify(user));}}catch(error){console.error('Error saving session:',error);}};const updateLastActivity=()=>{const savedSession=localStorage.getItem(SESSION_KEY);if(savedSession){try{const sessionData=JSON.parse(savedSession);sessionData.lastActivity=new Date().toISOString();localStorage.setItem(SESSION_KEY,JSON.stringify(sessionData));setSession(sessionData);}catch(error){console.error('Error updating last activity:',error);}}};const clearSession=()=>{localStorage.removeItem(SESSION_KEY);localStorage.removeItem('currentStudent');localStorage.removeItem('currentAdmin');setSession(null);};const checkSessionValidity=()=>{const savedSession=localStorage.getItem(SESSION_KEY);if(savedSession){try{const sessionData=JSON.parse(savedSession);const now=new Date().getTime();const lastActivity=new Date(sessionData.lastActivity).getTime();if(now-lastActivity>=SESSION_TIMEOUT){clearSession();// Optionally redirect to login page\nwindow.location.href='/';}}catch(error){console.error('Error checking session validity:',error);clearSession();}}};const extendSession=()=>{updateLastActivity();};const getSessionDuration=()=>{if(!session)return 0;const now=new Date().getTime();const loginTime=new Date(session.loginTime).getTime();return now-loginTime;};const getTimeUntilExpiry=()=>{if(!session)return 0;const now=new Date().getTime();const lastActivity=new Date(session.lastActivity).getTime();const timeRemaining=SESSION_TIMEOUT-(now-lastActivity);return Math.max(0,timeRemaining);};const isSessionValid=()=>{if(!session)return false;const now=new Date().getTime();const lastActivity=new Date(session.lastActivity).getTime();return now-lastActivity<SESSION_TIMEOUT;};return{session,isLoading,saveSession,clearSession,extendSession,updateLastActivity,getSessionDuration,getTimeUntilExpiry,isSessionValid,user:(session===null||session===void 0?void 0:session.user)||null,userType:(session===null||session===void 0?void 0:session.userType)||null};};// Session warning hook for showing expiry warnings\nexport const useSessionWarning=()=>{const{getTimeUntilExpiry,extendSession,clearSession}=useSessionPersistence();const[showWarning,setShowWarning]=useState(false);useEffect(()=>{const checkWarning=()=>{const timeRemaining=getTimeUntilExpiry();const warningThreshold=5*60*1000;// 5 minutes\nif(timeRemaining>0&&timeRemaining<=warningThreshold){setShowWarning(true);}else{setShowWarning(false);}};const warningInterval=setInterval(checkWarning,30000);// Check every 30 seconds\ncheckWarning();// Initial check\nreturn()=>clearInterval(warningInterval);},[getTimeUntilExpiry]);const handleExtendSession=()=>{extendSession();setShowWarning(false);};const handleLogout=()=>{clearSession();setShowWarning(false);};return{showWarning,timeRemaining:getTimeUntilExpiry(),extendSession:handleExtendSession,logout:handleLogout};};", "map": {"version": 3, "names": ["useState", "useEffect", "SESSION_KEY", "SESSION_TIMEOUT", "useSessionPersistence", "session", "setSession", "isLoading", "setIsLoading", "loadSession", "handleActivity", "updateLastActivity", "events", "for<PERSON>ach", "event", "document", "addEventListener", "sessionCheckInterval", "setInterval", "checkSessionValidity", "removeEventListener", "clearInterval", "savedSession", "localStorage", "getItem", "sessionData", "JSON", "parse", "now", "Date", "getTime", "lastActivity", "clearSession", "error", "console", "saveSession", "user", "userType", "loginTime", "toISOString", "setItem", "stringify", "removeItem", "window", "location", "href", "extendSession", "getSessionDuration", "getTimeUntilExpiry", "timeRemaining", "Math", "max", "isSessionValid", "useSessionWarning", "showWarning", "setShowWarning", "checkWarning", "warningThreshold", "warningInterval", "handleExtendSession", "handleLogout", "logout"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/hooks/useSessionPersistence.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Student, Admin } from '../types';\n\ninterface SessionData {\n  user: Student | Admin | null;\n  userType: 'student' | 'admin' | null;\n  loginTime: string;\n  lastActivity: string;\n}\n\nconst SESSION_KEY = 'alaa_academy_session';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n\nexport const useSessionPersistence = () => {\n  const [session, setSession] = useState<SessionData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    loadSession();\n    \n    // Set up activity tracking\n    const handleActivity = () => {\n      updateLastActivity();\n    };\n\n    // Track user activity\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];\n    events.forEach(event => {\n      document.addEventListener(event, handleActivity, true);\n    });\n\n    // Check session validity every minute\n    const sessionCheckInterval = setInterval(checkSessionValidity, 60000);\n\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleActivity, true);\n      });\n      clearInterval(sessionCheckInterval);\n    };\n  }, []);\n\n  const loadSession = () => {\n    try {\n      const savedSession = localStorage.getItem(SESSION_KEY);\n      if (savedSession) {\n        const sessionData: SessionData = JSON.parse(savedSession);\n        \n        // Check if session is still valid\n        const now = new Date().getTime();\n        const lastActivity = new Date(sessionData.lastActivity).getTime();\n        \n        if (now - lastActivity < SESSION_TIMEOUT) {\n          setSession(sessionData);\n          updateLastActivity();\n        } else {\n          // Session expired\n          clearSession();\n        }\n      }\n    } catch (error) {\n      console.error('Error loading session:', error);\n      clearSession();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const saveSession = (user: Student | Admin, userType: 'student' | 'admin') => {\n    const sessionData: SessionData = {\n      user,\n      userType,\n      loginTime: new Date().toISOString(),\n      lastActivity: new Date().toISOString()\n    };\n\n    try {\n      localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));\n      setSession(sessionData);\n\n      // Also save user data separately for backward compatibility\n      if (userType === 'student') {\n        localStorage.setItem('currentStudent', JSON.stringify(user));\n      } else {\n        localStorage.setItem('currentAdmin', JSON.stringify(user));\n      }\n    } catch (error) {\n      console.error('Error saving session:', error);\n    }\n  };\n\n  const updateLastActivity = () => {\n    const savedSession = localStorage.getItem(SESSION_KEY);\n    if (savedSession) {\n      try {\n        const sessionData: SessionData = JSON.parse(savedSession);\n        sessionData.lastActivity = new Date().toISOString();\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));\n        setSession(sessionData);\n      } catch (error) {\n        console.error('Error updating last activity:', error);\n      }\n    }\n  };\n\n  const clearSession = () => {\n    localStorage.removeItem(SESSION_KEY);\n    localStorage.removeItem('currentStudent');\n    localStorage.removeItem('currentAdmin');\n    setSession(null);\n  };\n\n  const checkSessionValidity = () => {\n    const savedSession = localStorage.getItem(SESSION_KEY);\n    if (savedSession) {\n      try {\n        const sessionData: SessionData = JSON.parse(savedSession);\n        const now = new Date().getTime();\n        const lastActivity = new Date(sessionData.lastActivity).getTime();\n        \n        if (now - lastActivity >= SESSION_TIMEOUT) {\n          clearSession();\n          // Optionally redirect to login page\n          window.location.href = '/';\n        }\n      } catch (error) {\n        console.error('Error checking session validity:', error);\n        clearSession();\n      }\n    }\n  };\n\n  const extendSession = () => {\n    updateLastActivity();\n  };\n\n  const getSessionDuration = (): number => {\n    if (!session) return 0;\n    \n    const now = new Date().getTime();\n    const loginTime = new Date(session.loginTime).getTime();\n    return now - loginTime;\n  };\n\n  const getTimeUntilExpiry = (): number => {\n    if (!session) return 0;\n    \n    const now = new Date().getTime();\n    const lastActivity = new Date(session.lastActivity).getTime();\n    const timeRemaining = SESSION_TIMEOUT - (now - lastActivity);\n    return Math.max(0, timeRemaining);\n  };\n\n  const isSessionValid = (): boolean => {\n    if (!session) return false;\n    \n    const now = new Date().getTime();\n    const lastActivity = new Date(session.lastActivity).getTime();\n    return (now - lastActivity) < SESSION_TIMEOUT;\n  };\n\n  return {\n    session,\n    isLoading,\n    saveSession,\n    clearSession,\n    extendSession,\n    updateLastActivity,\n    getSessionDuration,\n    getTimeUntilExpiry,\n    isSessionValid,\n    user: session?.user || null,\n    userType: session?.userType || null\n  };\n};\n\n// Session warning hook for showing expiry warnings\nexport const useSessionWarning = () => {\n  const { getTimeUntilExpiry, extendSession, clearSession } = useSessionPersistence();\n  const [showWarning, setShowWarning] = useState(false);\n\n  useEffect(() => {\n    const checkWarning = () => {\n      const timeRemaining = getTimeUntilExpiry();\n      const warningThreshold = 5 * 60 * 1000; // 5 minutes\n      \n      if (timeRemaining > 0 && timeRemaining <= warningThreshold) {\n        setShowWarning(true);\n      } else {\n        setShowWarning(false);\n      }\n    };\n\n    const warningInterval = setInterval(checkWarning, 30000); // Check every 30 seconds\n    checkWarning(); // Initial check\n\n    return () => clearInterval(warningInterval);\n  }, [getTimeUntilExpiry]);\n\n  const handleExtendSession = () => {\n    extendSession();\n    setShowWarning(false);\n  };\n\n  const handleLogout = () => {\n    clearSession();\n    setShowWarning(false);\n  };\n\n  return {\n    showWarning,\n    timeRemaining: getTimeUntilExpiry(),\n    extendSession: handleExtendSession,\n    logout: handleLogout\n  };\n};\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAU3C,KAAM,CAAAC,WAAW,CAAG,sBAAsB,CAC1C,KAAM,CAAAC,eAAe,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAE;AAE7C,MAAO,MAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CACzC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGN,QAAQ,CAAqB,IAAI,CAAC,CAChE,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGR,QAAQ,CAAC,IAAI,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACdQ,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3BC,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAC,MAAM,CAAG,CAAC,WAAW,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,YAAY,CAAE,OAAO,CAAC,CACtFA,MAAM,CAACC,OAAO,CAACC,KAAK,EAAI,CACtBC,QAAQ,CAACC,gBAAgB,CAACF,KAAK,CAAEJ,cAAc,CAAE,IAAI,CAAC,CACxD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAO,oBAAoB,CAAGC,WAAW,CAACC,oBAAoB,CAAE,KAAK,CAAC,CAErE,MAAO,IAAM,CACXP,MAAM,CAACC,OAAO,CAACC,KAAK,EAAI,CACtBC,QAAQ,CAACK,mBAAmB,CAACN,KAAK,CAAEJ,cAAc,CAAE,IAAI,CAAC,CAC3D,CAAC,CAAC,CACFW,aAAa,CAACJ,oBAAoB,CAAC,CACrC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAR,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CACF,KAAM,CAAAa,YAAY,CAAGC,YAAY,CAACC,OAAO,CAACtB,WAAW,CAAC,CACtD,GAAIoB,YAAY,CAAE,CAChB,KAAM,CAAAG,WAAwB,CAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,CAEzD;AACA,KAAM,CAAAM,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAF,IAAI,CAACJ,WAAW,CAACM,YAAY,CAAC,CAACD,OAAO,CAAC,CAAC,CAEjE,GAAIF,GAAG,CAAGG,YAAY,CAAG5B,eAAe,CAAE,CACxCG,UAAU,CAACmB,WAAW,CAAC,CACvBd,kBAAkB,CAAC,CAAC,CACtB,CAAC,IAAM,CACL;AACAqB,YAAY,CAAC,CAAC,CAChB,CACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CD,YAAY,CAAC,CAAC,CAChB,CAAC,OAAS,CACRxB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA2B,WAAW,CAAGA,CAACC,IAAqB,CAAEC,QAA6B,GAAK,CAC5E,KAAM,CAAAZ,WAAwB,CAAG,CAC/BW,IAAI,CACJC,QAAQ,CACRC,SAAS,CAAE,GAAI,CAAAT,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,CACnCR,YAAY,CAAE,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CACvC,CAAC,CAED,GAAI,CACFhB,YAAY,CAACiB,OAAO,CAACtC,WAAW,CAAEwB,IAAI,CAACe,SAAS,CAAChB,WAAW,CAAC,CAAC,CAC9DnB,UAAU,CAACmB,WAAW,CAAC,CAEvB;AACA,GAAIY,QAAQ,GAAK,SAAS,CAAE,CAC1Bd,YAAY,CAACiB,OAAO,CAAC,gBAAgB,CAAEd,IAAI,CAACe,SAAS,CAACL,IAAI,CAAC,CAAC,CAC9D,CAAC,IAAM,CACLb,YAAY,CAACiB,OAAO,CAAC,cAAc,CAAEd,IAAI,CAACe,SAAS,CAACL,IAAI,CAAC,CAAC,CAC5D,CACF,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAtB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAW,YAAY,CAAGC,YAAY,CAACC,OAAO,CAACtB,WAAW,CAAC,CACtD,GAAIoB,YAAY,CAAE,CAChB,GAAI,CACF,KAAM,CAAAG,WAAwB,CAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,CACzDG,WAAW,CAACM,YAAY,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,CACnDhB,YAAY,CAACiB,OAAO,CAACtC,WAAW,CAAEwB,IAAI,CAACe,SAAS,CAAChB,WAAW,CAAC,CAAC,CAC9DnB,UAAU,CAACmB,WAAW,CAAC,CACzB,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CACF,CAAC,CAED,KAAM,CAAAD,YAAY,CAAGA,CAAA,GAAM,CACzBT,YAAY,CAACmB,UAAU,CAACxC,WAAW,CAAC,CACpCqB,YAAY,CAACmB,UAAU,CAAC,gBAAgB,CAAC,CACzCnB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC,CACvCpC,UAAU,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAAa,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAG,YAAY,CAAGC,YAAY,CAACC,OAAO,CAACtB,WAAW,CAAC,CACtD,GAAIoB,YAAY,CAAE,CAChB,GAAI,CACF,KAAM,CAAAG,WAAwB,CAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,CACzD,KAAM,CAAAM,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAF,IAAI,CAACJ,WAAW,CAACM,YAAY,CAAC,CAACD,OAAO,CAAC,CAAC,CAEjE,GAAIF,GAAG,CAAGG,YAAY,EAAI5B,eAAe,CAAE,CACzC6B,YAAY,CAAC,CAAC,CACd;AACAW,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC5B,CACF,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDD,YAAY,CAAC,CAAC,CAChB,CACF,CACF,CAAC,CAED,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC1BnC,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAED,KAAM,CAAAoC,kBAAkB,CAAGA,CAAA,GAAc,CACvC,GAAI,CAAC1C,OAAO,CAAE,MAAO,EAAC,CAEtB,KAAM,CAAAuB,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAQ,SAAS,CAAG,GAAI,CAAAT,IAAI,CAACxB,OAAO,CAACiC,SAAS,CAAC,CAACR,OAAO,CAAC,CAAC,CACvD,MAAO,CAAAF,GAAG,CAAGU,SAAS,CACxB,CAAC,CAED,KAAM,CAAAU,kBAAkB,CAAGA,CAAA,GAAc,CACvC,GAAI,CAAC3C,OAAO,CAAE,MAAO,EAAC,CAEtB,KAAM,CAAAuB,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAF,IAAI,CAACxB,OAAO,CAAC0B,YAAY,CAAC,CAACD,OAAO,CAAC,CAAC,CAC7D,KAAM,CAAAmB,aAAa,CAAG9C,eAAe,EAAIyB,GAAG,CAAGG,YAAY,CAAC,CAC5D,MAAO,CAAAmB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEF,aAAa,CAAC,CACnC,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGA,CAAA,GAAe,CACpC,GAAI,CAAC/C,OAAO,CAAE,MAAO,MAAK,CAE1B,KAAM,CAAAuB,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAChC,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAF,IAAI,CAACxB,OAAO,CAAC0B,YAAY,CAAC,CAACD,OAAO,CAAC,CAAC,CAC7D,MAAQ,CAAAF,GAAG,CAAGG,YAAY,CAAI5B,eAAe,CAC/C,CAAC,CAED,MAAO,CACLE,OAAO,CACPE,SAAS,CACT4B,WAAW,CACXH,YAAY,CACZc,aAAa,CACbnC,kBAAkB,CAClBoC,kBAAkB,CAClBC,kBAAkB,CAClBI,cAAc,CACdhB,IAAI,CAAE,CAAA/B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE+B,IAAI,GAAI,IAAI,CAC3BC,QAAQ,CAAE,CAAAhC,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEgC,QAAQ,GAAI,IACjC,CAAC,CACH,CAAC,CAED;AACA,MAAO,MAAM,CAAAgB,iBAAiB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAEL,kBAAkB,CAAEF,aAAa,CAAEd,YAAa,CAAC,CAAG5B,qBAAqB,CAAC,CAAC,CACnF,KAAM,CAACkD,WAAW,CAAEC,cAAc,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAErDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuD,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAP,aAAa,CAAGD,kBAAkB,CAAC,CAAC,CAC1C,KAAM,CAAAS,gBAAgB,CAAG,CAAC,CAAG,EAAE,CAAG,IAAI,CAAE;AAExC,GAAIR,aAAa,CAAG,CAAC,EAAIA,aAAa,EAAIQ,gBAAgB,CAAE,CAC1DF,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACLA,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAG,eAAe,CAAGxC,WAAW,CAACsC,YAAY,CAAE,KAAK,CAAC,CAAE;AAC1DA,YAAY,CAAC,CAAC,CAAE;AAEhB,MAAO,IAAMnC,aAAa,CAACqC,eAAe,CAAC,CAC7C,CAAC,CAAE,CAACV,kBAAkB,CAAC,CAAC,CAExB,KAAM,CAAAW,mBAAmB,CAAGA,CAAA,GAAM,CAChCb,aAAa,CAAC,CAAC,CACfS,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB5B,YAAY,CAAC,CAAC,CACduB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,MAAO,CACLD,WAAW,CACXL,aAAa,CAAED,kBAAkB,CAAC,CAAC,CACnCF,aAAa,CAAEa,mBAAmB,CAClCE,MAAM,CAAED,YACV,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}