import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  AcademicCapIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  UserIcon,
  BookOpenIcon,
  CalendarIcon,
  TrophyIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { supabaseService } from '../../services/supabaseService';
import { supabase } from '../../config/supabase';
import { toast } from 'react-hot-toast';

interface Certificate {
  id: string;
  student_id: string;
  course_id: string;
  certificate_url?: string;
  grade: number;
  issued_at: string;
  verification_code: string;
  students: {
    id: string;
    name: string;
    access_code: string;
    email?: string;
  };
  courses: {
    id: string;
    title: string;
    description: string;
    level: string;
  };
}

interface Student {
  id: string;
  name: string;
  access_code: string;
  email?: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  level: string;
}

const AdvancedCertificatesManagement: React.FC = () => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [grade, setGrade] = useState<number>(85);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [certificatesData, studentsData, coursesData] = await Promise.all([
        loadCertificates(),
        supabaseService.getAllStudents(),
        supabaseService.getAllCourses()
      ]);

      setCertificates(certificatesData || []);
      setStudents(studentsData || []);
      setCourses(coursesData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('فشل في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const loadCertificates = async () => {
    try {
      const { data, error } = await supabase
        .from('certificates')
        .select(`
          *,
          students (
            id,
            name,
            access_code,
            email
          ),
          courses (
            id,
            title,
            description,
            level
          )
        `)
        .order('issued_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error loading certificates:', error);
      return [];
    }
  };

  const generateVerificationCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'CERT-';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleIssueCertificate = async () => {
    if (!selectedStudent || !selectedCourse) {
      toast.error('يرجى اختيار الطالب والكورس');
      return;
    }

    if (grade < 0 || grade > 100) {
      toast.error('يجب أن تكون الدرجة بين 0 و 100');
      return;
    }

    try {
      // Check if certificate already exists
      const existingCertificate = certificates.find(
        c => c.student_id === selectedStudent && c.course_id === selectedCourse
      );

      if (existingCertificate) {
        toast.error('الطالب لديه شهادة بالفعل لهذا الكورس');
        return;
      }

      const verificationCode = generateVerificationCode();

      const { data, error } = await supabase
        .from('certificates')
        .insert([{
          student_id: selectedStudent,
          course_id: selectedCourse,
          grade: grade,
          verification_code: verificationCode,
          issued_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      toast.success('تم إصدار الشهادة بنجاح');
      await loadData();
      
      // Reset form
      setSelectedStudent('');
      setSelectedCourse('');
      setGrade(85);
      setShowAddModal(false);
    } catch (error) {
      console.error('Error issuing certificate:', error);
      toast.error('فشل في إصدار الشهادة');
    }
  };

  const handleDeleteCertificate = async (certificateId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الشهادة؟')) return;

    try {
      const { error } = await supabase
        .from('certificates')
        .delete()
        .eq('id', certificateId);

      if (error) throw error;

      toast.success('تم حذف الشهادة بنجاح');
      await loadData();
    } catch (error) {
      console.error('Error deleting certificate:', error);
      toast.error('فشل في حذف الشهادة');
    }
  };

  const generateCertificatePDF = async (certificate: Certificate) => {
    try {
      // This would typically generate a PDF certificate
      // For now, we'll just show a success message
      toast.success('تم إنشاء ملف PDF للشهادة');
      
      // In a real implementation, you would:
      // 1. Generate PDF using a library like jsPDF or PDFKit
      // 2. Upload to storage (Supabase Storage)
      // 3. Update certificate record with PDF URL
      
    } catch (error) {
      console.error('Error generating certificate PDF:', error);
      toast.error('فشل في إنشاء ملف PDF');
    }
  };

  const filteredCertificates = certificates.filter(certificate => {
    const searchLower = searchTerm.toLowerCase();
    return (
      certificate.students?.name?.toLowerCase().includes(searchLower) ||
      certificate.students?.access_code?.toLowerCase().includes(searchLower) ||
      certificate.courses?.title?.toLowerCase().includes(searchLower) ||
      certificate.verification_code?.toLowerCase().includes(searchLower)
    );
  });

  const getGradeColor = (grade: number) => {
    if (grade >= 90) return 'text-green-600 bg-green-100';
    if (grade >= 80) return 'text-blue-600 bg-blue-100';
    if (grade >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getGradeLabel = (grade: number) => {
    if (grade >= 90) return 'ممتاز';
    if (grade >= 80) return 'جيد جداً';
    if (grade >= 70) return 'جيد';
    return 'مقبول';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الشهادات</h1>
          <p className="text-gray-600 mt-1">إصدار وإدارة شهادات إتمام الكورسات</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5 ml-2" />
          إصدار شهادة جديدة
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrophyIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">إجمالي الشهادات</p>
              <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircleIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">شهادات ممتازة</p>
              <p className="text-2xl font-bold text-gray-900">
                {certificates.filter(c => c.grade >= 90).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <UserIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">طلاب حاصلين على شهادات</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(certificates.map(c => c.student_id)).size}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <BookOpenIcon className="w-6 h-6 text-orange-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">كورسات بشهادات</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(certificates.map(c => c.course_id)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="relative">
          <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="البحث عن شهادة أو طالب أو كورس..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Certificates Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطالب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكورس
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدرجة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  رمز التحقق
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإصدار
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCertificates.map((certificate) => (
                <motion.tr
                  key={certificate.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-primary-600" />
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">
                          {certificate.students?.name || 'غير محدد'}
                        </div>
                        <div className="text-sm text-gray-500">
                          كود الوصول: {certificate.students?.access_code}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {certificate.courses?.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      {certificate.courses?.level}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGradeColor(certificate.grade)}`}>
                      {certificate.grade}% - {getGradeLabel(certificate.grade)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                      {certificate.verification_code}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarIcon className="w-4 h-4 ml-1" />
                      {new Date(certificate.issued_at).toLocaleDateString('ar-SA')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => generateCertificatePDF(certificate)}
                        className="text-blue-600 hover:text-blue-900 transition-colors"
                        title="تحميل PDF"
                      >
                        <ArrowDownTrayIcon className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteCertificate(certificate.id)}
                        className="text-red-600 hover:text-red-900 transition-colors"
                        title="حذف"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCertificates.length === 0 && (
          <div className="text-center py-12">
            <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد شهادات</h3>
            <p className="text-gray-500 mb-4">ابدأ بإصدار شهادة جديدة للطلاب</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <PlusIcon className="w-5 h-5 ml-2" />
              إصدار شهادة
            </button>
          </div>
        )}
      </div>

      {/* Issue Certificate Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-md"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">إصدار شهادة جديدة</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اختر الطالب
                </label>
                <select
                  value={selectedStudent}
                  onChange={(e) => setSelectedStudent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">-- اختر طالب --</option>
                  {students.map((student) => (
                    <option key={student.id} value={student.id}>
                      {student.name} ({student.access_code})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اختر الكورس
                </label>
                <select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">-- اختر كورس --</option>
                  {courses.map((course) => (
                    <option key={course.id} value={course.id}>
                      {course.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الدرجة (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={grade}
                  onChange={(e) => setGrade(parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 space-x-reverse mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleIssueCertificate}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                إصدار الشهادة
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default AdvancedCertificatesManagement;
