{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{useNavigate}from'react-router-dom';import{AcademicCapIcon,TrophyIcon,PlayIcon,BookOpenIcon,ChartBarIcon,ClockIcon,StarIcon,FireIcon}from'@heroicons/react/24/outline';import{supabase}from'../../config/supabase';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RealTimeStudentOverview=_ref=>{let{user}=_ref;const navigate=useNavigate();const[stats,setStats]=useState({totalCourses:0,completedCourses:0,inProgressCourses:0,totalVideos:0,completedVideos:0,totalWatchTime:0,certificates:0,averageGrade:0,currentStreak:0,totalQuizzes:0,completedQuizzes:0});const[recentActivity,setRecentActivity]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{if(user){loadStudentStats();loadRecentActivity();}},[user]);const loadStudentStats=async()=>{try{setLoading(true);// Get enrollments\nconst{data:enrollments,error:enrollmentsError}=await supabase.from('student_enrollments').select('*, courses(*)').eq('student_id',user.id);if(enrollmentsError)throw enrollmentsError;// Get video progress\nconst{data:videoProgress,error:videoError}=await supabase.from('video_progress').select('*, videos(*)').eq('student_id',user.id);if(videoError)throw videoError;// Get certificates\nconst{data:certificates,error:certificatesError}=await supabase.from('certificates').select('*').eq('student_id',user.id);if(certificatesError)throw certificatesError;// Get quiz attempts\nconst{data:quizAttempts,error:quizError}=await supabase.from('quiz_attempts').select('*').eq('student_id',user.id);if(quizError&&quizError.code!=='PGRST116')throw quizError;// Calculate stats\nconst totalCourses=(enrollments===null||enrollments===void 0?void 0:enrollments.length)||0;const completedCourses=(enrollments===null||enrollments===void 0?void 0:enrollments.filter(e=>e.progress===100).length)||0;const inProgressCourses=(enrollments===null||enrollments===void 0?void 0:enrollments.filter(e=>e.progress>0&&e.progress<100).length)||0;const completedVideos=(videoProgress===null||videoProgress===void 0?void 0:videoProgress.filter(vp=>vp.completed).length)||0;const totalWatchTime=(videoProgress===null||videoProgress===void 0?void 0:videoProgress.reduce((sum,vp)=>sum+(vp.watched_duration||0),0))||0;const certificatesCount=(certificates===null||certificates===void 0?void 0:certificates.length)||0;const averageGrade=certificatesCount>0?Math.round(certificates.reduce((sum,cert)=>sum+cert.grade,0)/certificatesCount):0;const completedQuizzes=(quizAttempts===null||quizAttempts===void 0?void 0:quizAttempts.filter(qa=>qa.completed).length)||0;const totalQuizzes=new Set(quizAttempts===null||quizAttempts===void 0?void 0:quizAttempts.map(qa=>qa.quiz_id)).size||0;// Calculate current streak (simplified)\nconst currentStreak=calculateStreak(videoProgress||[]);// Get total videos from enrolled courses\nlet totalVideos=0;if(enrollments){for(const enrollment of enrollments){const{data:courseVideos}=await supabase.from('videos').select('id').eq('course_id',enrollment.course_id);totalVideos+=(courseVideos===null||courseVideos===void 0?void 0:courseVideos.length)||0;}}setStats({totalCourses,completedCourses,inProgressCourses,totalVideos,completedVideos,totalWatchTime,certificates:certificatesCount,averageGrade,currentStreak,totalQuizzes,completedQuizzes});}catch(error){console.error('Error loading student stats:',error);toast.error('فشل في تحميل الإحصائيات');}finally{setLoading(false);}};const loadRecentActivity=async()=>{try{const activities=[];// Get recent video completions\nconst{data:recentVideos}=await supabase.from('video_progress').select('*, videos(*)').eq('student_id',user.id).eq('completed',true).order('completed_at',{ascending:false}).limit(5);recentVideos===null||recentVideos===void 0?void 0:recentVideos.forEach(vp=>{if(vp.completed_at){var _vp$videos;activities.push({id:`video-${vp.id}`,type:'video_completed',title:'إكمال فيديو',description:((_vp$videos=vp.videos)===null||_vp$videos===void 0?void 0:_vp$videos.title)||'فيديو غير محدد',date:vp.completed_at,icon:PlayIcon,color:'text-blue-600 bg-blue-100'});}});// Get recent certificates\nconst{data:recentCertificates}=await supabase.from('certificates').select('*, courses(*)').eq('student_id',user.id).order('issued_at',{ascending:false}).limit(3);recentCertificates===null||recentCertificates===void 0?void 0:recentCertificates.forEach(cert=>{var _cert$courses;activities.push({id:`cert-${cert.id}`,type:'certificate_earned',title:'حصول على شهادة',description:((_cert$courses=cert.courses)===null||_cert$courses===void 0?void 0:_cert$courses.title)||'كورس غير محدد',date:cert.issued_at,icon:TrophyIcon,color:'text-yellow-600 bg-yellow-100'});});// Sort by date and take latest 10\nactivities.sort((a,b)=>new Date(b.date).getTime()-new Date(a.date).getTime());setRecentActivity(activities.slice(0,10));}catch(error){console.error('Error loading recent activity:',error);}};const calculateStreak=videoProgress=>{// Simplified streak calculation based on consecutive days with video completions\nconst completedDates=videoProgress.filter(vp=>vp.completed&&vp.completed_at).map(vp=>new Date(vp.completed_at).toDateString()).filter((date,index,array)=>array.indexOf(date)===index).sort((a,b)=>new Date(b).getTime()-new Date(a).getTime());let streak=0;const today=new Date().toDateString();for(let i=0;i<completedDates.length;i++){const currentDate=new Date(completedDates[i]);const expectedDate=new Date();expectedDate.setDate(expectedDate.getDate()-i);if(currentDate.toDateString()===expectedDate.toDateString()){streak++;}else{break;}}return streak;};const formatWatchTime=seconds=>{const hours=Math.floor(seconds/3600);const minutes=Math.floor(seconds%3600/60);if(hours>0){return`${hours} ساعة و ${minutes} دقيقة`;}return`${minutes} دقيقة`;};const getProgressPercentage=(completed,total)=>{return total>0?Math.round(completed/total*100):0;};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl font-bold mb-2\",children:[\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \",user.name,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-primary-100\",children:\"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0631\\u062D\\u0644\\u062A\\u0643 \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629 \\u0648\\u0627\\u062D\\u0642\\u0642 \\u0623\\u0647\\u062F\\u0627\\u0641\\u0643\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse mb-2\",children:[/*#__PURE__*/_jsx(FireIcon,{className:\"w-6 h-6 text-orange-300\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-bold\",children:stats.currentStreak})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-primary-100\",children:\"\\u0623\\u064A\\u0627\\u0645 \\u0645\\u062A\\u062A\\u0627\\u0644\\u064A\\u0629\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:stats.totalCourses}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[stats.completedCourses,\" \\u0645\\u0643\\u062A\\u0645\\u0644 \\u2022 \",stats.inProgressCourses,\" \\u0642\\u064A\\u062F \\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"]})]})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(PlayIcon,{className:\"w-6 h-6 text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:stats.completedVideos}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[\"\\u0645\\u0646 \\u0623\\u0635\\u0644 \",stats.totalVideos,\" \\u0641\\u064A\\u062F\\u064A\\u0648\"]})]})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(ClockIcon,{className:\"w-6 h-6 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg font-bold text-gray-900\",children:formatWatchTime(stats.totalWatchTime)})]})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.4},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-yellow-100 rounded-lg\",children:/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-6 h-6 text-yellow-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:stats.certificates}),stats.averageGrade>0&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[\"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u062F\\u0631\\u062C\\u0627\\u062A: \",stats.averageGrade,\"%\"]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.5},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u062A\\u0642\\u062F\\u0645 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"\\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[getProgressPercentage(stats.completedVideos,stats.totalVideos),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-600 h-2 rounded-full transition-all duration-500\",style:{width:`${getProgressPercentage(stats.completedVideos,stats.totalVideos)}%`}})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"\\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[getProgressPercentage(stats.completedQuizzes,stats.totalQuizzes),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-green-600 h-2 rounded-full transition-all duration-500\",style:{width:`${getProgressPercentage(stats.completedQuizzes,stats.totalQuizzes)}%`}})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[getProgressPercentage(stats.completedCourses,stats.totalCourses),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-purple-600 h-2 rounded-full transition-all duration-500\",style:{width:`${getProgressPercentage(stats.completedCourses,stats.totalCourses)}%`}})})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.6},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0627\\u0644\\u0646\\u0634\\u0627\\u0637 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:recentActivity.length>0?recentActivity.map(activity=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:`p-2 rounded-lg ${activity.color}`,children:/*#__PURE__*/_jsx(activity.icon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:activity.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 truncate\",children:activity.description})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-400\",children:new Date(activity.date).toLocaleDateString('ar-SA')})]},activity.id)):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(ChartBarIcon,{className:\"w-8 h-8 text-gray-400 mx-auto mb-2\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u0644\\u0627 \\u064A\\u0648\\u062C\\u062F \\u0646\\u0634\\u0627\\u0637 \\u062D\\u062F\\u064A\\u062B\"})]})})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.7},className:\"bg-white rounded-lg p-6 shadow-sm border\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/courses'),className:\"flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors\",children:[/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-6 h-6 text-blue-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-blue-900\",children:\"\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-blue-600\",children:\"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/certificates'),className:\"flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors\",children:[/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-6 h-6 text-yellow-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-yellow-900\",children:\"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-yellow-600\",children:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/student/profile'),className:\"flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-green-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-green-900\",children:\"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-green-600\",children:\"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-4 bg-purple-50 rounded-lg\",children:[/*#__PURE__*/_jsx(StarIcon,{className:\"w-6 h-6 text-purple-600 ml-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-purple-900\",children:\"\\u0627\\u0644\\u062A\\u0642\\u064A\\u064A\\u0645\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-purple-600\",children:stats.averageGrade>0?`${stats.averageGrade}%`:'لا يوجد'})]})]})]})]})]});};export default RealTimeStudentOverview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "AcademicCapIcon", "TrophyIcon", "PlayIcon", "BookOpenIcon", "ChartBarIcon", "ClockIcon", "StarIcon", "FireIcon", "supabase", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "RealTimeStudentOverview", "_ref", "user", "navigate", "stats", "setStats", "totalCourses", "completedCourses", "inProgressCourses", "totalVideos", "completedVideos", "totalWatchTime", "certificates", "averageGrade", "currentStreak", "totalQuizzes", "completedQuizzes", "recentActivity", "setRecentActivity", "loading", "setLoading", "loadStudentStats", "loadRecentActivity", "data", "enrollments", "error", "enrollmentsError", "from", "select", "eq", "id", "videoProgress", "videoError", "certificatesError", "quizAttempts", "quizError", "code", "length", "filter", "e", "progress", "vp", "completed", "reduce", "sum", "watched_duration", "certificatesCount", "Math", "round", "cert", "grade", "qa", "Set", "map", "quiz_id", "size", "calculateStreak", "enrollment", "courseVideos", "course_id", "console", "activities", "recentVideos", "order", "ascending", "limit", "for<PERSON>ach", "completed_at", "_vp$videos", "push", "type", "title", "description", "videos", "date", "icon", "color", "recentCertificates", "_cert$courses", "courses", "issued_at", "sort", "a", "b", "Date", "getTime", "slice", "completedDates", "toDateString", "index", "array", "indexOf", "streak", "today", "i", "currentDate", "expectedDate", "setDate", "getDate", "formatWatchTime", "seconds", "hours", "floor", "minutes", "getProgressPercentage", "total", "className", "children", "div", "initial", "opacity", "y", "animate", "name", "transition", "delay", "style", "width", "activity", "toLocaleDateString", "onClick"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/RealTimeStudentOverview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AcademicCapIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  TrophyIcon,\n  PlayIcon,\n  BookOpenIcon,\n  ChartBarIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  StarIcon,\n  CalendarIcon,\n  FireIcon\n} from '@heroicons/react/24/outline';\nimport { Student } from '../../types';\nimport { supabaseService } from '../../services/supabaseService';\nimport { supabase } from '../../config/supabase';\nimport { toast } from 'react-hot-toast';\n\ninterface StudentStats {\n  totalCourses: number;\n  completedCourses: number;\n  inProgressCourses: number;\n  totalVideos: number;\n  completedVideos: number;\n  totalWatchTime: number;\n  certificates: number;\n  averageGrade: number;\n  currentStreak: number;\n  totalQuizzes: number;\n  completedQuizzes: number;\n}\n\ninterface RecentActivity {\n  id: string;\n  type: 'video_completed' | 'quiz_completed' | 'course_completed' | 'certificate_earned';\n  title: string;\n  description: string;\n  date: string;\n  icon: React.ComponentType<any>;\n  color: string;\n}\n\ninterface Props {\n  user: Student;\n}\n\nconst RealTimeStudentOverview: React.FC<Props> = ({ user }) => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState<StudentStats>({\n    totalCourses: 0,\n    completedCourses: 0,\n    inProgressCourses: 0,\n    totalVideos: 0,\n    completedVideos: 0,\n    totalWatchTime: 0,\n    certificates: 0,\n    averageGrade: 0,\n    currentStreak: 0,\n    totalQuizzes: 0,\n    completedQuizzes: 0\n  });\n  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      loadStudentStats();\n      loadRecentActivity();\n    }\n  }, [user]);\n\n  const loadStudentStats = async () => {\n    try {\n      setLoading(true);\n\n      // Get enrollments\n      const { data: enrollments, error: enrollmentsError } = await supabase\n        .from('student_enrollments')\n        .select('*, courses(*)')\n        .eq('student_id', user.id);\n\n      if (enrollmentsError) throw enrollmentsError;\n\n      // Get video progress\n      const { data: videoProgress, error: videoError } = await supabase\n        .from('video_progress')\n        .select('*, videos(*)')\n        .eq('student_id', user.id);\n\n      if (videoError) throw videoError;\n\n      // Get certificates\n      const { data: certificates, error: certificatesError } = await supabase\n        .from('certificates')\n        .select('*')\n        .eq('student_id', user.id);\n\n      if (certificatesError) throw certificatesError;\n\n      // Get quiz attempts\n      const { data: quizAttempts, error: quizError } = await supabase\n        .from('quiz_attempts')\n        .select('*')\n        .eq('student_id', user.id);\n\n      if (quizError && quizError.code !== 'PGRST116') throw quizError;\n\n      // Calculate stats\n      const totalCourses = enrollments?.length || 0;\n      const completedCourses = enrollments?.filter(e => e.progress === 100).length || 0;\n      const inProgressCourses = enrollments?.filter(e => e.progress > 0 && e.progress < 100).length || 0;\n      \n      const completedVideos = videoProgress?.filter(vp => vp.completed).length || 0;\n      const totalWatchTime = videoProgress?.reduce((sum, vp) => sum + (vp.watched_duration || 0), 0) || 0;\n      \n      const certificatesCount = certificates?.length || 0;\n      const averageGrade = certificatesCount > 0 \n        ? Math.round(certificates.reduce((sum, cert) => sum + cert.grade, 0) / certificatesCount)\n        : 0;\n\n      const completedQuizzes = quizAttempts?.filter(qa => qa.completed).length || 0;\n      const totalQuizzes = new Set(quizAttempts?.map(qa => qa.quiz_id)).size || 0;\n\n      // Calculate current streak (simplified)\n      const currentStreak = calculateStreak(videoProgress || []);\n\n      // Get total videos from enrolled courses\n      let totalVideos = 0;\n      if (enrollments) {\n        for (const enrollment of enrollments) {\n          const { data: courseVideos } = await supabase\n            .from('videos')\n            .select('id')\n            .eq('course_id', enrollment.course_id);\n          totalVideos += courseVideos?.length || 0;\n        }\n      }\n\n      setStats({\n        totalCourses,\n        completedCourses,\n        inProgressCourses,\n        totalVideos,\n        completedVideos,\n        totalWatchTime,\n        certificates: certificatesCount,\n        averageGrade,\n        currentStreak,\n        totalQuizzes,\n        completedQuizzes\n      });\n\n    } catch (error) {\n      console.error('Error loading student stats:', error);\n      toast.error('فشل في تحميل الإحصائيات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadRecentActivity = async () => {\n    try {\n      const activities: RecentActivity[] = [];\n\n      // Get recent video completions\n      const { data: recentVideos } = await supabase\n        .from('video_progress')\n        .select('*, videos(*)')\n        .eq('student_id', user.id)\n        .eq('completed', true)\n        .order('completed_at', { ascending: false })\n        .limit(5);\n\n      recentVideos?.forEach(vp => {\n        if (vp.completed_at) {\n          activities.push({\n            id: `video-${vp.id}`,\n            type: 'video_completed',\n            title: 'إكمال فيديو',\n            description: vp.videos?.title || 'فيديو غير محدد',\n            date: vp.completed_at,\n            icon: PlayIcon,\n            color: 'text-blue-600 bg-blue-100'\n          });\n        }\n      });\n\n      // Get recent certificates\n      const { data: recentCertificates } = await supabase\n        .from('certificates')\n        .select('*, courses(*)')\n        .eq('student_id', user.id)\n        .order('issued_at', { ascending: false })\n        .limit(3);\n\n      recentCertificates?.forEach(cert => {\n        activities.push({\n          id: `cert-${cert.id}`,\n          type: 'certificate_earned',\n          title: 'حصول على شهادة',\n          description: cert.courses?.title || 'كورس غير محدد',\n          date: cert.issued_at,\n          icon: TrophyIcon,\n          color: 'text-yellow-600 bg-yellow-100'\n        });\n      });\n\n      // Sort by date and take latest 10\n      activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());\n      setRecentActivity(activities.slice(0, 10));\n\n    } catch (error) {\n      console.error('Error loading recent activity:', error);\n    }\n  };\n\n  const calculateStreak = (videoProgress: any[]) => {\n    // Simplified streak calculation based on consecutive days with video completions\n    const completedDates = videoProgress\n      .filter(vp => vp.completed && vp.completed_at)\n      .map(vp => new Date(vp.completed_at).toDateString())\n      .filter((date, index, array) => array.indexOf(date) === index)\n      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());\n\n    let streak = 0;\n    const today = new Date().toDateString();\n    \n    for (let i = 0; i < completedDates.length; i++) {\n      const currentDate = new Date(completedDates[i]);\n      const expectedDate = new Date();\n      expectedDate.setDate(expectedDate.getDate() - i);\n      \n      if (currentDate.toDateString() === expectedDate.toDateString()) {\n        streak++;\n      } else {\n        break;\n      }\n    }\n\n    return streak;\n  };\n\n  const formatWatchTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours} ساعة و ${minutes} دقيقة`;\n    }\n    return `${minutes} دقيقة`;\n  };\n\n  const getProgressPercentage = (completed: number, total: number) => {\n    return total > 0 ? Math.round((completed / total) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold mb-2\">مرحباً، {user.name}!</h1>\n            <p className=\"text-primary-100\">\n              استمر في رحلتك التعليمية واحقق أهدافك\n            </p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-2\">\n              <FireIcon className=\"w-6 h-6 text-orange-300\" />\n              <span className=\"text-lg font-bold\">{stats.currentStreak}</span>\n            </div>\n            <p className=\"text-sm text-primary-100\">أيام متتالية</p>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <BookOpenIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">الكورسات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.totalCourses}</p>\n              <p className=\"text-xs text-gray-500\">\n                {stats.completedCourses} مكتمل • {stats.inProgressCourses} قيد التقدم\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <PlayIcon className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">الفيديوهات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.completedVideos}</p>\n              <p className=\"text-xs text-gray-500\">\n                من أصل {stats.totalVideos} فيديو\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <ClockIcon className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">وقت المشاهدة</p>\n              <p className=\"text-lg font-bold text-gray-900\">\n                {formatWatchTime(stats.totalWatchTime)}\n              </p>\n            </div>\n          </div>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-yellow-100 rounded-lg\">\n              <TrophyIcon className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">الشهادات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.certificates}</p>\n              {stats.averageGrade > 0 && (\n                <p className=\"text-xs text-gray-500\">\n                  متوسط الدرجات: {stats.averageGrade}%\n                </p>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Progress Overview */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Course Progress */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">تقدم الكورسات</h3>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">الفيديوهات المكتملة</span>\n                <span className=\"text-sm text-gray-500\">\n                  {getProgressPercentage(stats.completedVideos, stats.totalVideos)}%\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div\n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${getProgressPercentage(stats.completedVideos, stats.totalVideos)}%` }}\n                />\n              </div>\n            </div>\n\n            <div>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">الاختبارات المكتملة</span>\n                <span className=\"text-sm text-gray-500\">\n                  {getProgressPercentage(stats.completedQuizzes, stats.totalQuizzes)}%\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div\n                  className=\"bg-green-600 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${getProgressPercentage(stats.completedQuizzes, stats.totalQuizzes)}%` }}\n                />\n              </div>\n            </div>\n\n            <div>\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">الكورسات المكتملة</span>\n                <span className=\"text-sm text-gray-500\">\n                  {getProgressPercentage(stats.completedCourses, stats.totalCourses)}%\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div\n                  className=\"bg-purple-600 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${getProgressPercentage(stats.completedCourses, stats.totalCourses)}%` }}\n                />\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Recent Activity */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm border\"\n        >\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">النشاط الأخير</h3>\n          \n          <div className=\"space-y-3\">\n            {recentActivity.length > 0 ? (\n              recentActivity.map((activity) => (\n                <div key={activity.id} className=\"flex items-center space-x-3 space-x-reverse\">\n                  <div className={`p-2 rounded-lg ${activity.color}`}>\n                    <activity.icon className=\"w-4 h-4\" />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">\n                      {activity.title}\n                    </p>\n                    <p className=\"text-xs text-gray-500 truncate\">\n                      {activity.description}\n                    </p>\n                  </div>\n                  <div className=\"text-xs text-gray-400\">\n                    {new Date(activity.date).toLocaleDateString('ar-SA')}\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center py-8\">\n                <ChartBarIcon className=\"w-8 h-8 text-gray-400 mx-auto mb-2\" />\n                <p className=\"text-sm text-gray-500\">لا يوجد نشاط حديث</p>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.7 }}\n        className=\"bg-white rounded-lg p-6 shadow-sm border\"\n      >\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">إجراءات سريعة</h3>\n        \n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <button\n            onClick={() => navigate('/student/courses')}\n            className=\"flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors\"\n          >\n            <BookOpenIcon className=\"w-6 h-6 text-blue-600 ml-3\" />\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-blue-900\">كورساتي</p>\n              <p className=\"text-xs text-blue-600\">عرض جميع الكورسات</p>\n            </div>\n          </button>\n\n          <button\n            onClick={() => navigate('/student/certificates')}\n            className=\"flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors\"\n          >\n            <TrophyIcon className=\"w-6 h-6 text-yellow-600 ml-3\" />\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-yellow-900\">شهاداتي</p>\n              <p className=\"text-xs text-yellow-600\">عرض الشهادات</p>\n            </div>\n          </button>\n\n          <button\n            onClick={() => navigate('/student/profile')}\n            className=\"flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors\"\n          >\n            <AcademicCapIcon className=\"w-6 h-6 text-green-600 ml-3\" />\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-green-900\">الملف الشخصي</p>\n              <p className=\"text-xs text-green-600\">تحديث البيانات</p>\n            </div>\n          </button>\n\n          <div className=\"flex items-center p-4 bg-purple-50 rounded-lg\">\n            <StarIcon className=\"w-6 h-6 text-purple-600 ml-3\" />\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-purple-900\">التقييم</p>\n              <p className=\"text-xs text-purple-600\">\n                {stats.averageGrade > 0 ? `${stats.averageGrade}%` : 'لا يوجد'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default RealTimeStudentOverview;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,eAAe,CAGfC,UAAU,CACVC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,SAAS,CAETC,QAAQ,CAERC,QAAQ,KACH,6BAA6B,CAGpC,OAASC,QAAQ,KAAQ,uBAAuB,CAChD,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA8BxC,KAAM,CAAAC,uBAAwC,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CACxD,KAAM,CAAAE,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACmB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAe,CAC/CwB,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CAAC,CACnBC,iBAAiB,CAAE,CAAC,CACpBC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAE,CAAC,CAClBC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CACpB,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpC,QAAQ,CAAmB,EAAE,CAAC,CAC1E,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd,GAAImB,IAAI,CAAE,CACRmB,gBAAgB,CAAC,CAAC,CAClBC,kBAAkB,CAAC,CAAC,CACtB,CACF,CAAC,CAAE,CAACpB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAAmB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACFD,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAEG,IAAI,CAAEC,WAAW,CAAEC,KAAK,CAAEC,gBAAiB,CAAC,CAAG,KAAM,CAAAhC,QAAQ,CAClEiC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,eAAe,CAAC,CACvBC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CAE5B,GAAIJ,gBAAgB,CAAE,KAAM,CAAAA,gBAAgB,CAE5C;AACA,KAAM,CAAEH,IAAI,CAAEQ,aAAa,CAAEN,KAAK,CAAEO,UAAW,CAAC,CAAG,KAAM,CAAAtC,QAAQ,CAC9DiC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,cAAc,CAAC,CACtBC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CAE5B,GAAIE,UAAU,CAAE,KAAM,CAAAA,UAAU,CAEhC;AACA,KAAM,CAAET,IAAI,CAAEX,YAAY,CAAEa,KAAK,CAAEQ,iBAAkB,CAAC,CAAG,KAAM,CAAAvC,QAAQ,CACpEiC,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CAE5B,GAAIG,iBAAiB,CAAE,KAAM,CAAAA,iBAAiB,CAE9C;AACA,KAAM,CAAEV,IAAI,CAAEW,YAAY,CAAET,KAAK,CAAEU,SAAU,CAAC,CAAG,KAAM,CAAAzC,QAAQ,CAC5DiC,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CAE5B,GAAIK,SAAS,EAAIA,SAAS,CAACC,IAAI,GAAK,UAAU,CAAE,KAAM,CAAAD,SAAS,CAE/D;AACA,KAAM,CAAA7B,YAAY,CAAG,CAAAkB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEa,MAAM,GAAI,CAAC,CAC7C,KAAM,CAAA9B,gBAAgB,CAAG,CAAAiB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEc,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,GAAK,GAAG,CAAC,CAACH,MAAM,GAAI,CAAC,CACjF,KAAM,CAAA7B,iBAAiB,CAAG,CAAAgB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEc,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,CAAG,CAAC,EAAID,CAAC,CAACC,QAAQ,CAAG,GAAG,CAAC,CAACH,MAAM,GAAI,CAAC,CAElG,KAAM,CAAA3B,eAAe,CAAG,CAAAqB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEO,MAAM,CAACG,EAAE,EAAIA,EAAE,CAACC,SAAS,CAAC,CAACL,MAAM,GAAI,CAAC,CAC7E,KAAM,CAAA1B,cAAc,CAAG,CAAAoB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEY,MAAM,CAAC,CAACC,GAAG,CAAEH,EAAE,GAAKG,GAAG,EAAIH,EAAE,CAACI,gBAAgB,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAEnG,KAAM,CAAAC,iBAAiB,CAAG,CAAAlC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEyB,MAAM,GAAI,CAAC,CACnD,KAAM,CAAAxB,YAAY,CAAGiC,iBAAiB,CAAG,CAAC,CACtCC,IAAI,CAACC,KAAK,CAACpC,YAAY,CAAC+B,MAAM,CAAC,CAACC,GAAG,CAAEK,IAAI,GAAKL,GAAG,CAAGK,IAAI,CAACC,KAAK,CAAE,CAAC,CAAC,CAAGJ,iBAAiB,CAAC,CACvF,CAAC,CAEL,KAAM,CAAA9B,gBAAgB,CAAG,CAAAkB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEI,MAAM,CAACa,EAAE,EAAIA,EAAE,CAACT,SAAS,CAAC,CAACL,MAAM,GAAI,CAAC,CAC7E,KAAM,CAAAtB,YAAY,CAAG,GAAI,CAAAqC,GAAG,CAAClB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEmB,GAAG,CAACF,EAAE,EAAIA,EAAE,CAACG,OAAO,CAAC,CAAC,CAACC,IAAI,EAAI,CAAC,CAE3E;AACA,KAAM,CAAAzC,aAAa,CAAG0C,eAAe,CAACzB,aAAa,EAAI,EAAE,CAAC,CAE1D;AACA,GAAI,CAAAtB,WAAW,CAAG,CAAC,CACnB,GAAIe,WAAW,CAAE,CACf,IAAK,KAAM,CAAAiC,UAAU,GAAI,CAAAjC,WAAW,CAAE,CACpC,KAAM,CAAED,IAAI,CAAEmC,YAAa,CAAC,CAAG,KAAM,CAAAhE,QAAQ,CAC1CiC,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,WAAW,CAAE4B,UAAU,CAACE,SAAS,CAAC,CACxClD,WAAW,EAAI,CAAAiD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAErB,MAAM,GAAI,CAAC,CAC1C,CACF,CAEAhC,QAAQ,CAAC,CACPC,YAAY,CACZC,gBAAgB,CAChBC,iBAAiB,CACjBC,WAAW,CACXC,eAAe,CACfC,cAAc,CACdC,YAAY,CAAEkC,iBAAiB,CAC/BjC,YAAY,CACZC,aAAa,CACbC,YAAY,CACZC,gBACF,CAAC,CAAC,CAEJ,CAAE,MAAOS,KAAK,CAAE,CACdmC,OAAO,CAACnC,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD9B,KAAK,CAAC8B,KAAK,CAAC,yBAAyB,CAAC,CACxC,CAAC,OAAS,CACRL,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAAuC,UAA4B,CAAG,EAAE,CAEvC;AACA,KAAM,CAAEtC,IAAI,CAAEuC,YAAa,CAAC,CAAG,KAAM,CAAApE,QAAQ,CAC1CiC,IAAI,CAAC,gBAAgB,CAAC,CACtBC,MAAM,CAAC,cAAc,CAAC,CACtBC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CACzBD,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,CACrBkC,KAAK,CAAC,cAAc,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAC3CC,KAAK,CAAC,CAAC,CAAC,CAEXH,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEI,OAAO,CAACzB,EAAE,EAAI,CAC1B,GAAIA,EAAE,CAAC0B,YAAY,CAAE,KAAAC,UAAA,CACnBP,UAAU,CAACQ,IAAI,CAAC,CACdvC,EAAE,CAAE,SAASW,EAAE,CAACX,EAAE,EAAE,CACpBwC,IAAI,CAAE,iBAAiB,CACvBC,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,EAAAJ,UAAA,CAAA3B,EAAE,CAACgC,MAAM,UAAAL,UAAA,iBAATA,UAAA,CAAWG,KAAK,GAAI,gBAAgB,CACjDG,IAAI,CAAEjC,EAAE,CAAC0B,YAAY,CACrBQ,IAAI,CAAEvF,QAAQ,CACdwF,KAAK,CAAE,2BACT,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAErD,IAAI,CAAEsD,kBAAmB,CAAC,CAAG,KAAM,CAAAnF,QAAQ,CAChDiC,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC,eAAe,CAAC,CACvBC,EAAE,CAAC,YAAY,CAAE3B,IAAI,CAAC4B,EAAE,CAAC,CACzBiC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACxCC,KAAK,CAAC,CAAC,CAAC,CAEXY,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAEX,OAAO,CAACjB,IAAI,EAAI,KAAA6B,aAAA,CAClCjB,UAAU,CAACQ,IAAI,CAAC,CACdvC,EAAE,CAAE,QAAQmB,IAAI,CAACnB,EAAE,EAAE,CACrBwC,IAAI,CAAE,oBAAoB,CAC1BC,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,EAAAM,aAAA,CAAA7B,IAAI,CAAC8B,OAAO,UAAAD,aAAA,iBAAZA,aAAA,CAAcP,KAAK,GAAI,eAAe,CACnDG,IAAI,CAAEzB,IAAI,CAAC+B,SAAS,CACpBL,IAAI,CAAExF,UAAU,CAChByF,KAAK,CAAE,+BACT,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACAf,UAAU,CAACoB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAC,IAAI,CAACD,CAAC,CAACT,IAAI,CAAC,CAACW,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACF,CAAC,CAACR,IAAI,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAClFnE,iBAAiB,CAAC2C,UAAU,CAACyB,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,CAE5C,CAAE,MAAO7D,KAAK,CAAE,CACdmC,OAAO,CAACnC,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAA+B,eAAe,CAAIzB,aAAoB,EAAK,CAChD;AACA,KAAM,CAAAwD,cAAc,CAAGxD,aAAa,CACjCO,MAAM,CAACG,EAAE,EAAIA,EAAE,CAACC,SAAS,EAAID,EAAE,CAAC0B,YAAY,CAAC,CAC7Cd,GAAG,CAACZ,EAAE,EAAI,GAAI,CAAA2C,IAAI,CAAC3C,EAAE,CAAC0B,YAAY,CAAC,CAACqB,YAAY,CAAC,CAAC,CAAC,CACnDlD,MAAM,CAAC,CAACoC,IAAI,CAAEe,KAAK,CAAEC,KAAK,GAAKA,KAAK,CAACC,OAAO,CAACjB,IAAI,CAAC,GAAKe,KAAK,CAAC,CAC7DR,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAC,IAAI,CAACD,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACF,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAEhE,GAAI,CAAAO,MAAM,CAAG,CAAC,CACd,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAT,IAAI,CAAC,CAAC,CAACI,YAAY,CAAC,CAAC,CAEvC,IAAK,GAAI,CAAAM,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGP,cAAc,CAAClD,MAAM,CAAEyD,CAAC,EAAE,CAAE,CAC9C,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAX,IAAI,CAACG,cAAc,CAACO,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAAE,YAAY,CAAG,GAAI,CAAAZ,IAAI,CAAC,CAAC,CAC/BY,YAAY,CAACC,OAAO,CAACD,YAAY,CAACE,OAAO,CAAC,CAAC,CAAGJ,CAAC,CAAC,CAEhD,GAAIC,WAAW,CAACP,YAAY,CAAC,CAAC,GAAKQ,YAAY,CAACR,YAAY,CAAC,CAAC,CAAE,CAC9DI,MAAM,EAAE,CACV,CAAC,IAAM,CACL,MACF,CACF,CAEA,MAAO,CAAAA,MAAM,CACf,CAAC,CAED,KAAM,CAAAO,eAAe,CAAIC,OAAe,EAAK,CAC3C,KAAM,CAAAC,KAAK,CAAGtD,IAAI,CAACuD,KAAK,CAACF,OAAO,CAAG,IAAI,CAAC,CACxC,KAAM,CAAAG,OAAO,CAAGxD,IAAI,CAACuD,KAAK,CAAEF,OAAO,CAAG,IAAI,CAAI,EAAE,CAAC,CAEjD,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,MAAO,GAAGA,KAAK,WAAWE,OAAO,QAAQ,CAC3C,CACA,MAAO,GAAGA,OAAO,QAAQ,CAC3B,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAC9D,SAAiB,CAAE+D,KAAa,GAAK,CAClE,MAAO,CAAAA,KAAK,CAAG,CAAC,CAAG1D,IAAI,CAACC,KAAK,CAAEN,SAAS,CAAG+D,KAAK,CAAI,GAAG,CAAC,CAAG,CAAC,CAC9D,CAAC,CAED,GAAItF,OAAO,CAAE,CACX,mBACEtB,IAAA,QAAK6G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD9G,IAAA,QAAK6G,SAAS,CAAC,mEAAmE,CAAM,CAAC,CACtF,CAAC,CAEV,CAEA,mBACE3G,KAAA,QAAK2G,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB9G,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BL,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cAEtF5G,KAAA,QAAK2G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5G,KAAA,QAAA4G,QAAA,eACE5G,KAAA,OAAI2G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,6CAAQ,CAACzG,IAAI,CAAC+G,IAAI,CAAC,GAAC,EAAI,CAAC,cACjEpH,IAAA,MAAG6G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,uMAEhC,CAAG,CAAC,EACD,CAAC,cACN5G,KAAA,QAAK2G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB5G,KAAA,QAAK2G,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/D9G,IAAA,CAACJ,QAAQ,EAACiH,SAAS,CAAC,yBAAyB,CAAE,CAAC,cAChD7G,IAAA,SAAM6G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAEvG,KAAK,CAACU,aAAa,CAAO,CAAC,EAC7D,CAAC,cACNjB,IAAA,MAAG6G,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,EACrD,CAAC,EACH,CAAC,CACI,CAAC,cAGb5G,KAAA,QAAK2G,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE9G,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cAEpD5G,KAAA,QAAK2G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9G,IAAA,QAAK6G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzC9G,IAAA,CAACR,YAAY,EAACqH,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC/C,CAAC,cACN3G,KAAA,QAAK2G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kDAAQ,CAAG,CAAC,cACjD9G,IAAA,MAAG6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEvG,KAAK,CAACE,YAAY,CAAI,CAAC,cACxEP,KAAA,MAAG2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACjCvG,KAAK,CAACG,gBAAgB,CAAC,yCAAS,CAACH,KAAK,CAACI,iBAAiB,CAAC,0DAC5D,EAAG,CAAC,EACD,CAAC,EACH,CAAC,CACI,CAAC,cAEbX,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cAEpD5G,KAAA,QAAK2G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9G,IAAA,QAAK6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9G,IAAA,CAACT,QAAQ,EAACsH,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC5C,CAAC,cACN3G,KAAA,QAAK2G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8DAAU,CAAG,CAAC,cACnD9G,IAAA,MAAG6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEvG,KAAK,CAACM,eAAe,CAAI,CAAC,cAC3EX,KAAA,MAAG2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,kCAC5B,CAACvG,KAAK,CAACK,WAAW,CAAC,iCAC5B,EAAG,CAAC,EACD,CAAC,EACH,CAAC,CACI,CAAC,cAEbZ,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cAEpD5G,KAAA,QAAK2G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9G,IAAA,QAAK6G,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C9G,IAAA,CAACN,SAAS,EAACmH,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC9C,CAAC,cACN3G,KAAA,QAAK2G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,cACrD9G,IAAA,MAAG6G,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC3CR,eAAe,CAAC/F,KAAK,CAACO,cAAc,CAAC,CACrC,CAAC,EACD,CAAC,EACH,CAAC,CACI,CAAC,cAEbd,IAAA,CAACb,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cAEpD5G,KAAA,QAAK2G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9G,IAAA,QAAK6G,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C9G,IAAA,CAACV,UAAU,EAACuH,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC/C,CAAC,cACN3G,KAAA,QAAK2G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kDAAQ,CAAG,CAAC,cACjD9G,IAAA,MAAG6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEvG,KAAK,CAACQ,YAAY,CAAI,CAAC,CACvER,KAAK,CAACS,YAAY,CAAG,CAAC,eACrBd,KAAA,MAAG2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,6EACpB,CAACvG,KAAK,CAACS,YAAY,CAAC,GACrC,EAAG,CACJ,EACE,CAAC,EACH,CAAC,CACI,CAAC,EACV,CAAC,cAGNd,KAAA,QAAK2G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD5G,KAAA,CAACf,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpD9G,IAAA,OAAI6G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEzE5G,KAAA,QAAK2G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB5G,KAAA,QAAA4G,QAAA,eACE5G,KAAA,QAAK2G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9G,IAAA,SAAM6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,+GAAmB,CAAM,CAAC,cAC9E5G,KAAA,SAAM2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACpCH,qBAAqB,CAACpG,KAAK,CAACM,eAAe,CAAEN,KAAK,CAACK,WAAW,CAAC,CAAC,GACnE,EAAM,CAAC,EACJ,CAAC,cACNZ,IAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD9G,IAAA,QACE6G,SAAS,CAAC,0DAA0D,CACpEU,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGb,qBAAqB,CAACpG,KAAK,CAACM,eAAe,CAAEN,KAAK,CAACK,WAAW,CAAC,GAAI,CAAE,CACzF,CAAC,CACC,CAAC,EACH,CAAC,cAENV,KAAA,QAAA4G,QAAA,eACE5G,KAAA,QAAK2G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9G,IAAA,SAAM6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,+GAAmB,CAAM,CAAC,cAC9E5G,KAAA,SAAM2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACpCH,qBAAqB,CAACpG,KAAK,CAACY,gBAAgB,CAAEZ,KAAK,CAACW,YAAY,CAAC,CAAC,GACrE,EAAM,CAAC,EACJ,CAAC,cACNlB,IAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD9G,IAAA,QACE6G,SAAS,CAAC,2DAA2D,CACrEU,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGb,qBAAqB,CAACpG,KAAK,CAACY,gBAAgB,CAAEZ,KAAK,CAACW,YAAY,CAAC,GAAI,CAAE,CAC3F,CAAC,CACC,CAAC,EACH,CAAC,cAENhB,KAAA,QAAA4G,QAAA,eACE5G,KAAA,QAAK2G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9G,IAAA,SAAM6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,mGAAiB,CAAM,CAAC,cAC5E5G,KAAA,SAAM2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACpCH,qBAAqB,CAACpG,KAAK,CAACG,gBAAgB,CAAEH,KAAK,CAACE,YAAY,CAAC,CAAC,GACrE,EAAM,CAAC,EACJ,CAAC,cACNT,IAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD9G,IAAA,QACE6G,SAAS,CAAC,4DAA4D,CACtEU,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGb,qBAAqB,CAACpG,KAAK,CAACG,gBAAgB,CAAEH,KAAK,CAACE,YAAY,CAAC,GAAI,CAAE,CAC3F,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACI,CAAC,cAGbP,KAAA,CAACf,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpD9G,IAAA,OAAI6G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEzE9G,IAAA,QAAK6G,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB1F,cAAc,CAACoB,MAAM,CAAG,CAAC,CACxBpB,cAAc,CAACoC,GAAG,CAAEiE,QAAQ,eAC1BvH,KAAA,QAAuB2G,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC5E9G,IAAA,QAAK6G,SAAS,CAAE,kBAAkBY,QAAQ,CAAC1C,KAAK,EAAG,CAAA+B,QAAA,cACjD9G,IAAA,CAACyH,QAAQ,CAAC3C,IAAI,EAAC+B,SAAS,CAAC,SAAS,CAAE,CAAC,CAClC,CAAC,cACN3G,KAAA,QAAK2G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B9G,IAAA,MAAG6G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtDW,QAAQ,CAAC/C,KAAK,CACd,CAAC,cACJ1E,IAAA,MAAG6G,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC1CW,QAAQ,CAAC9C,WAAW,CACpB,CAAC,EACD,CAAC,cACN3E,IAAA,QAAK6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnC,GAAI,CAAAvB,IAAI,CAACkC,QAAQ,CAAC5C,IAAI,CAAC,CAAC6C,kBAAkB,CAAC,OAAO,CAAC,CACjD,CAAC,GAdED,QAAQ,CAACxF,EAed,CACN,CAAC,cAEF/B,KAAA,QAAK2G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9G,IAAA,CAACP,YAAY,EAACoH,SAAS,CAAC,oCAAoC,CAAE,CAAC,cAC/D7G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yFAAiB,CAAG,CAAC,EACvD,CACN,CACE,CAAC,EACI,CAAC,EACV,CAAC,cAGN5G,KAAA,CAACf,MAAM,CAAC4H,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAEpD9G,IAAA,OAAI6G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEzE5G,KAAA,QAAK2G,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE5G,KAAA,WACEyH,OAAO,CAAEA,CAAA,GAAMrH,QAAQ,CAAC,kBAAkB,CAAE,CAC5CuG,SAAS,CAAC,iFAAiF,CAAAC,QAAA,eAE3F9G,IAAA,CAACR,YAAY,EAACqH,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACvD3G,KAAA,QAAK2G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9G,IAAA,MAAG6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,4CAAO,CAAG,CAAC,cAC5D9G,IAAA,MAAG6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8FAAiB,CAAG,CAAC,EACvD,CAAC,EACA,CAAC,cAET5G,KAAA,WACEyH,OAAO,CAAEA,CAAA,GAAMrH,QAAQ,CAAC,uBAAuB,CAAE,CACjDuG,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAE/F9G,IAAA,CAACV,UAAU,EAACuH,SAAS,CAAC,8BAA8B,CAAE,CAAC,cACvD3G,KAAA,QAAK2G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9G,IAAA,MAAG6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,4CAAO,CAAG,CAAC,cAC9D9G,IAAA,MAAG6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,EACpD,CAAC,EACA,CAAC,cAET5G,KAAA,WACEyH,OAAO,CAAEA,CAAA,GAAMrH,QAAQ,CAAC,kBAAkB,CAAE,CAC5CuG,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eAE7F9G,IAAA,CAACX,eAAe,EAACwH,SAAS,CAAC,6BAA6B,CAAE,CAAC,cAC3D3G,KAAA,QAAK2G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9G,IAAA,MAAG6G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,cAClE9G,IAAA,MAAG6G,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,iFAAc,CAAG,CAAC,EACrD,CAAC,EACA,CAAC,cAET5G,KAAA,QAAK2G,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5D9G,IAAA,CAACL,QAAQ,EAACkH,SAAS,CAAC,8BAA8B,CAAE,CAAC,cACrD3G,KAAA,QAAK2G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9G,IAAA,MAAG6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,4CAAO,CAAG,CAAC,cAC9D9G,IAAA,MAAG6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACnCvG,KAAK,CAACS,YAAY,CAAG,CAAC,CAAG,GAAGT,KAAK,CAACS,YAAY,GAAG,CAAG,SAAS,CAC7D,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,EACI,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}