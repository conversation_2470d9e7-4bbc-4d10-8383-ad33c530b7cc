import { db } from '../config/firebase';
import { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { Course, Student, Quiz, Certificate, Category } from '../types';
import { supabaseService } from './supabaseService';



class DataService {
  // Courses
  async getCourses(): Promise<Course[]> {
    try {
      const supabaseCourses = await supabaseService.getAllCourses();
      if (supabaseCourses && supabaseCourses.length > 0) {
        // Transform Supabase data to match our Course type
        return supabaseCourses.map(course => ({
          id: course.id,
          title: course.title,
          description: course.description,
          categoryId: course.category_id || '',
          instructorId: course.instructor_id || '',
          thumbnailUrl: course.thumbnail_url || '',
          price: course.price || 0,
          duration: course.duration_hours || 0,
          level: course.level || 'beginner',
          isActive: course.is_active,
          videos: course.videos?.map((video: any) => ({
            id: video.id,
            title: video.title,
            url: video.video_url,
            duration: video.duration || 0,
            order: video.order_index || 0
          })) || [],
          pdfs: [], // Will be implemented later
          quizzes: course.quizzes?.map((quiz: any) => quiz.id) || [],
          enrolledStudents: 0, // Will be calculated separately
          createdAt: new Date(course.created_at),
          updatedAt: new Date(course.updated_at || course.created_at)
        }));
      }

      return [];
    } catch (error) {
      console.error('Error fetching courses:', error);
      return [];
    }
  }

  async getCourse(id: string): Promise<Course | null> {
    try {
      // Try to get from Supabase first
      const supabaseCourse = await supabaseService.getCourseById(id);
      if (supabaseCourse) {
        // Transform Supabase data to match our Course type
        return {
          id: supabaseCourse.id,
          title: supabaseCourse.title,
          description: supabaseCourse.description,
          categoryId: supabaseCourse.category_id || '',
          instructorId: supabaseCourse.instructor_id || '',
          thumbnailUrl: supabaseCourse.thumbnail_url || '',
          price: supabaseCourse.price || 0,
          duration: supabaseCourse.duration_hours || 0,
          level: supabaseCourse.level || 'beginner',
          isActive: supabaseCourse.is_active,
          videos: supabaseCourse.videos?.map((video: any) => ({
            id: video.id,
            title: video.title,
            url: video.video_url,
            duration: video.duration || 0,
            order: video.order_index || 0
          })) || [],
          pdfs: [], // Will be implemented later
          quizzes: supabaseCourse.quizzes?.map((quiz: any) => quiz.id) || [],
          enrolledStudents: 0, // Will be calculated separately
          createdAt: new Date(supabaseCourse.created_at),
          updatedAt: new Date(supabaseCourse.updated_at || supabaseCourse.created_at)
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching course:', error);
      return null;
    }
  }

  async addCourse(course: Omit<Course, 'id'>): Promise<string> {
    try {
      // Create course in Supabase
      const newCourse = await supabaseService.createCourse({
        title: course.title,
        description: course.description,
        categoryId: course.categoryId,
        instructorId: course.instructorId,
        thumbnailUrl: course.thumbnailUrl,
        price: course.price,
        durationHours: course.duration,
        level: course.level
      });

      return newCourse.id;
    } catch (error) {
      console.error('Error adding course:', error);
      throw error;
    }
  }

  async updateCourse(id: string, course: Partial<Course>): Promise<void> {
    try {
      // Update course in Supabase
      await supabaseService.updateCourse(id, {
        title: course.title,
        description: course.description,
        categoryId: course.categoryId,
        thumbnailUrl: course.thumbnailUrl,
        price: course.price,
        durationHours: course.duration,
        level: course.level,
        isActive: course.isActive
      });
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      // Delete course from Supabase
      await supabaseService.deleteCourse(id);
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  }

  // Students
  async getStudents(): Promise<Student[]> {
    try {
      const supabaseStudents = await supabaseService.getAllStudents();
      return supabaseStudents || [];
    } catch (error) {
      console.error('Error fetching students:', error);
      return [];
    }
  }

  async getStudent(id: string): Promise<Student | null> {
    try {
      const student = await supabaseService.getStudentById(id);
      return student;
    } catch (error) {
      console.error('Error fetching student:', error);
      return null;
    }
  }

  // Quizzes
  async getQuizzes(): Promise<Quiz[]> {
    try {
      const supabaseQuizzes = await supabaseService.getAllQuizzes();
      return supabaseQuizzes || [];
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      return [];
    }
  }

  async getQuiz(id: string): Promise<Quiz | null> {
    try {
      const quiz = await supabaseService.getQuizById(id);
      return quiz;
    } catch (error) {
      console.error('Error fetching quiz:', error);
      return null;
    }
  }

  // Certificates
  async getCertificates(): Promise<Certificate[]> {
    try {
      const supabaseCertificates = await supabaseService.getAllCertificates();
      return supabaseCertificates || [];
    } catch (error) {
      console.error('Error fetching certificates:', error);
      return [];
    }
  }

  async getStudentCertificates(studentId: string): Promise<Certificate[]> {
    try {
      const certificates = await supabaseService.getStudentCertificates(studentId);
      return certificates || [];
    } catch (error) {
      console.error('Error fetching student certificates:', error);
      return [];
    }
  }

  // Categories
  async getCategories(): Promise<Category[]> {
    try {
      // Try to get from Supabase first
      const supabaseCategories = await supabaseService.getAllCategories();
      if (supabaseCategories && supabaseCategories.length > 0) {
        // Transform Supabase data to match our Category type
        return supabaseCategories.map(category => ({
          id: category.id,
          name: category.name,
          description: category.description || '',
          isActive: category.is_active,
          createdAt: new Date(category.created_at),
          updatedAt: new Date(category.updated_at || category.created_at)
        }));
      }

      // Fallback to mock data
      return mockCategories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Fallback to mock data
      return mockCategories;
    }
  }

  async getCategory(id: string): Promise<Category | null> {
    try {
      const category = mockCategories.find(c => c.id === id);
      return category || null;
    } catch (error) {
      console.error('Error fetching category:', error);
      return null;
    }
  }

  async createCategory(category: Omit<Category, 'id' | 'createdAt'>): Promise<string> {
    try {
      // Create category in Supabase
      const newCategory = await supabaseService.createCategory({
        name: category.name,
        description: category.description
      });

      return newCategory.id;
    } catch (error) {
      console.error('Error adding category:', error);
      throw error;
    }
  }

  async updateCategory(id: string, category: Partial<Category>): Promise<void> {
    try {
      // Update category in Supabase
      await supabaseService.updateCategory(id, {
        name: category.name,
        description: category.description,
        isActive: category.isActive
      });
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      // Delete category from Supabase
      await supabaseService.deleteCategory(id);
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }

  // Analytics
  async getAnalytics() {
    try {
      const [students, courses, quizzes, certificates] = await Promise.all([
        this.getStudents(),
        this.getCourses(),
        this.getQuizzes(),
        this.getCertificates()
      ]);

      return {
        totalStudents: students.length,
        totalCourses: courses.length,
        totalQuizzes: quizzes.length,
        totalCertificates: certificates.length,
        revenue: courses.reduce((sum, course) => sum + (course.price || 0), 0),
        enrollments: 0 // Will be calculated from enrollments table
      };
    } catch (error) {
      console.error('Error fetching analytics:', error);
      return {
        totalStudents: 0,
        totalCourses: 0,
        totalQuizzes: 0,
        totalCertificates: 0,
        revenue: 0,
        enrollments: 0
      };
    }
  }
}

export const dataService = new DataService();
