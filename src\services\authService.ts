import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { User, Student, Admin } from '../types';
import { supabaseService } from './supabaseService';
import { defaultAdmin, adminCredentials } from '../data/defaultAdmin';

class AuthService {
  // Admin login
  async loginAdmin(email: string, password: string): Promise<Admin> {
    try {
      // Try Supabase first
      let admin = null;
      try {
        admin = await supabaseService.getAdminByEmail(email);
      } catch (supabaseError) {
        console.warn('Supabase admin login failed, trying fallback:', supabaseError);
      }

      if (admin) {
        // Verify password (simplified for frontend)
        const isPasswordValid = password === 'Admin@123456';
        if (!isPasswordValid) {
          throw new Error('كلمة المرور غير صحيحة');
        }

        return {
          id: admin.id,
          email: admin.email,
          role: 'admin',
          name: admin.name,
          avatar: admin.avatar_url,
          permissions: admin.permissions || [],
          createdAt: new Date(admin.created_at)
        };
      }

      // Fallback to default admin
      if (adminCredentials.email === email && adminCredentials.password === password) {
        return {
          id: 'admin-001',
          email: adminCredentials.email,
          role: 'admin',
          name: defaultAdmin.name,
          avatar: defaultAdmin.avatar_url,
          permissions: defaultAdmin.permissions,
          createdAt: new Date()
        };
      }

      throw new Error('بيانات تسجيل الدخول غير صحيحة');
    } catch (error: any) {
      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');
    }
  }

  // Student login with access code
  async loginStudent(accessCode: string): Promise<Student> {
    try {
      // Try Supabase first
      let studentData = null;
      try {
        studentData = await supabaseService.getStudentByAccessCode(accessCode);
      } catch (supabaseError) {
        console.warn('Supabase student login failed, trying fallback:', supabaseError);
      }

      if (studentData) {
        if (!studentData.is_active) {
          throw new Error('الحساب غير مفعل');
        }

        // Transform Supabase data to match our Student type
        const enrolledCourses = studentData.student_enrollments?.map((enrollment: any) => enrollment.course_id) || [];
        const completedCourses = studentData.student_enrollments?.filter((enrollment: any) => enrollment.completed_at).map((enrollment: any) => enrollment.course_id) || [];
        const certificates = studentData.certificates?.map((cert: any) => cert.id) || [];

        return {
          id: studentData.id,
          email: studentData.email || '',
          role: 'student',
          name: studentData.name || '',
          avatar: studentData.avatar_url || '',
          accessCode: studentData.access_code,
          enrolledCourses,
          completedCourses,
          certificates,
          createdAt: new Date(studentData.created_at)
        };
      }

      throw new Error('كود الدخول غير صحيح');
    } catch (error: any) {
      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');
    }
  }

  // Generate access code for student
  generateAccessCode(): string {
    return Math.floor(1000000 + Math.random() * 9000000).toString();
  }

  // Create student account
  async createStudent(studentData: {
    name: string;
    email?: string;
    enrolledCourses?: string[];
  }): Promise<string> {
    try {
      const accessCode = this.generateAccessCode();

      // Create student in Supabase
      const newStudent = await supabaseService.createStudent({
        accessCode: accessCode,
        name: studentData.name,
        email: studentData.email
      });

      // Enroll student in courses if provided
      if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {
        for (const courseId of studentData.enrolledCourses) {
          await supabaseService.enrollStudent(newStudent.id, courseId);
        }
      }

      return accessCode;
    } catch (error: any) {
      // If access code already exists, try again
      if (error.message.includes('duplicate key')) {
        return this.createStudent(studentData);
      }
      throw new Error('فشل في إنشاء حساب الطالب');
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error: any) {
      throw new Error('فشل في تسجيل الخروج');
    }
  }

  // Get current user
  getCurrentUser(): Promise<FirebaseUser | null> {
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe();
        resolve(user);
      });
    });
  }

  // Auth state listener
  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'المستخدم غير موجود';
      case 'auth/wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'auth/invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'auth/user-disabled':
        return 'الحساب معطل';
      case 'auth/too-many-requests':
        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';
      default:
        return 'حدث خطأ في تسجيل الدخول';
    }
  }
}

export const authService = new AuthService();
