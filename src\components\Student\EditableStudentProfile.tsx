import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  EnvelopeIcon,
  IdentificationIcon,
  CalendarIcon,
  CameraIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import { Student } from '../../types';
import { supabaseService } from '../../services/supabaseService';
import { supabase } from '../../config/supabase';
import { toast } from 'react-hot-toast';

interface Props {
  user: Student;
}

interface ProfileFormData {
  name: string;
  email: string;
  avatar_url: string;
}

const EditableStudentProfile: React.FC<Props> = ({ user }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    name: user.name || '',
    email: user.email || '',
    avatar_url: (user as any).avatar_url || ''
  });
  const [showAccessCode, setShowAccessCode] = useState(false);
  const [studentStats, setStudentStats] = useState({
    totalCourses: 0,
    completedCourses: 0,
    certificates: 0,
    joinDate: (user as any).created_at || user.createdAt?.toISOString() || new Date().toISOString()
  });

  useEffect(() => {
    loadStudentStats();
  }, [user]);

  const loadStudentStats = async () => {
    try {
      // Get enrollments
      const { data: enrollments } = await supabase
        .from('student_enrollments')
        .select('*')
        .eq('student_id', user.id);

      // Get certificates
      const { data: certificates } = await supabase
        .from('certificates')
        .select('*')
        .eq('student_id', user.id);

      const totalCourses = enrollments?.length || 0;
      const completedCourses = enrollments?.filter(e => e.progress === 100).length || 0;
      const certificatesCount = certificates?.length || 0;

      setStudentStats({
        totalCourses,
        completedCourses,
        certificates: certificatesCount,
        joinDate: (user as any).created_at || user.createdAt?.toISOString() || new Date().toISOString()
      });
    } catch (error) {
      console.error('Error loading student stats:', error);
    }
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error('الاسم مطلوب');
      return;
    }

    if (formData.email && !isValidEmail(formData.email)) {
      toast.error('البريد الإلكتروني غير صحيح');
      return;
    }

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('students')
        .update({
          name: formData.name.trim(),
          email: formData.email.trim() || null,
          avatar_url: formData.avatar_url.trim() || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      // Update localStorage
      const currentStudent = JSON.parse(localStorage.getItem('currentStudent') || '{}');
      const updatedStudent = { ...currentStudent, ...data };
      localStorage.setItem('currentStudent', JSON.stringify(updatedStudent));

      toast.success('تم تحديث الملف الشخصي بنجاح');
      setIsEditing(false);

      // Refresh page to reflect changes
      window.location.reload();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('فشل في تحديث الملف الشخصي');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user.name || '',
      email: user.email || '',
      avatar_url: (user as any).avatar_url || ''
    });
    setIsEditing(false);
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const copyAccessCode = () => {
    navigator.clipboard.writeText((user as any).access_code || user.accessCode);
    toast.success('تم نسخ كود الوصول');
  };

  const getAvatarUrl = () => {
    if (formData.avatar_url) {
      return formData.avatar_url;
    }
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name)}&background=3B82F6&color=fff&size=200`;
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg p-6 shadow-sm border"
      >
        <div className="flex items-start justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900">الملف الشخصي</h1>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <PencilIcon className="w-4 h-4 ml-2" />
              تعديل
            </button>
          ) : (
            <div className="flex space-x-2 space-x-reverse">
              <button
                onClick={handleSave}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                <CheckIcon className="w-4 h-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ'}
              </button>
              <button
                onClick={handleCancel}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <XMarkIcon className="w-4 h-4 ml-2" />
                إلغاء
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Avatar Section */}
          <div className="lg:col-span-1">
            <div className="text-center">
              <div className="relative inline-block">
                <img
                  src={getAvatarUrl()}
                  alt={formData.name}
                  className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-white shadow-lg"
                />
                {isEditing && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                    <CameraIcon className="w-8 h-8 text-white" />
                  </div>
                )}
              </div>
              
              {isEditing && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رابط الصورة الشخصية
                  </label>
                  <input
                    type="url"
                    value={formData.avatar_url}
                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}
                    placeholder="https://example.com/avatar.jpg"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Profile Information */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم الكامل
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="أدخل اسمك الكامل"
                  />
                ) : (
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <UserIcon className="w-5 h-5 text-gray-400 ml-3" />
                    <span className="text-gray-900">{formData.name || 'غير محدد'}</span>
                  </div>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                ) : (
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <EnvelopeIcon className="w-5 h-5 text-gray-400 ml-3" />
                    <span className="text-gray-900">{formData.email || 'غير محدد'}</span>
                  </div>
                )}
              </div>

              {/* Access Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كود الوصول
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <IdentificationIcon className="w-5 h-5 text-gray-400 ml-3" />
                  <span className="text-gray-900 font-mono flex-1">
                    {showAccessCode ? ((user as any).access_code || user.accessCode) : '••••••••'}
                  </span>
                  <button
                    onClick={() => setShowAccessCode(!showAccessCode)}
                    className="p-1 text-gray-400 hover:text-gray-600 ml-2"
                  >
                    {showAccessCode ? (
                      <EyeSlashIcon className="w-4 h-4" />
                    ) : (
                      <EyeIcon className="w-4 h-4" />
                    )}
                  </button>
                  <button
                    onClick={copyAccessCode}
                    className="px-2 py-1 text-xs bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                  >
                    نسخ
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  استخدم هذا الكود لتسجيل الدخول
                </p>
              </div>

              {/* Join Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الانضمام
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <CalendarIcon className="w-5 h-5 text-gray-400 ml-3" />
                  <span className="text-gray-900">
                    {new Date(studentStats.joinDate).toLocaleDateString('ar-SA', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-lg p-6 shadow-sm border"
      >
        <h2 className="text-lg font-medium text-gray-900 mb-4">إحصائياتي</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {studentStats.totalCourses}
            </div>
            <div className="text-sm text-blue-800">إجمالي الكورسات</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {studentStats.completedCourses}
            </div>
            <div className="text-sm text-green-800">كورسات مكتملة</div>
          </div>

          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600 mb-1">
              {studentStats.certificates}
            </div>
            <div className="text-sm text-yellow-800">شهادات حاصل عليها</div>
          </div>
        </div>
      </motion.div>

      {/* Account Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-lg p-6 shadow-sm border"
      >
        <h2 className="text-lg font-medium text-gray-900 mb-4">إعدادات الحساب</h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="text-sm font-medium text-gray-900">حالة الحساب</h3>
              <p className="text-xs text-gray-500">حسابك نشط ويمكنك الوصول لجميع الكورسات</p>
            </div>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              نشط
            </span>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="text-sm font-medium text-gray-900">الإشعارات</h3>
              <p className="text-xs text-gray-500">تلقي إشعارات حول التحديثات والكورسات الجديدة</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EditableStudentProfile;
