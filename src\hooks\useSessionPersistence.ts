import { useState, useEffect } from 'react';
import { Student, Admin } from '../types';

interface SessionData {
  user: Student | Admin | null;
  userType: 'student' | 'admin' | null;
  loginTime: string;
  lastActivity: string;
}

const SESSION_KEY = 'alaa_academy_session';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export const useSessionPersistence = () => {
  const [session, setSession] = useState<SessionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSession();
    
    // Set up activity tracking
    const handleActivity = () => {
      updateLastActivity();
    };

    // Track user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Check session validity every minute
    const sessionCheckInterval = setInterval(checkSessionValidity, 60000);

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearInterval(sessionCheckInterval);
    };
  }, []);

  const loadSession = () => {
    try {
      const savedSession = localStorage.getItem(SESSION_KEY);
      if (savedSession) {
        const sessionData: SessionData = JSON.parse(savedSession);
        
        // Check if session is still valid
        const now = new Date().getTime();
        const lastActivity = new Date(sessionData.lastActivity).getTime();
        
        if (now - lastActivity < SESSION_TIMEOUT) {
          setSession(sessionData);
          updateLastActivity();
        } else {
          // Session expired
          clearSession();
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
      clearSession();
    } finally {
      setIsLoading(false);
    }
  };

  const saveSession = (user: Student | Admin, userType: 'student' | 'admin') => {
    const sessionData: SessionData = {
      user,
      userType,
      loginTime: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };

    try {
      localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
      setSession(sessionData);

      // Also save user data separately for backward compatibility
      if (userType === 'student') {
        localStorage.setItem('currentStudent', JSON.stringify(user));
      } else {
        localStorage.setItem('currentAdmin', JSON.stringify(user));
      }
    } catch (error) {
      console.error('Error saving session:', error);
    }
  };

  const updateLastActivity = () => {
    const savedSession = localStorage.getItem(SESSION_KEY);
    if (savedSession) {
      try {
        const sessionData: SessionData = JSON.parse(savedSession);
        sessionData.lastActivity = new Date().toISOString();
        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
        setSession(sessionData);
      } catch (error) {
        console.error('Error updating last activity:', error);
      }
    }
  };

  const clearSession = () => {
    localStorage.removeItem(SESSION_KEY);
    localStorage.removeItem('currentStudent');
    localStorage.removeItem('currentAdmin');
    setSession(null);
  };

  const checkSessionValidity = () => {
    const savedSession = localStorage.getItem(SESSION_KEY);
    if (savedSession) {
      try {
        const sessionData: SessionData = JSON.parse(savedSession);
        const now = new Date().getTime();
        const lastActivity = new Date(sessionData.lastActivity).getTime();
        
        if (now - lastActivity >= SESSION_TIMEOUT) {
          clearSession();
          // Optionally redirect to login page
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Error checking session validity:', error);
        clearSession();
      }
    }
  };

  const extendSession = () => {
    updateLastActivity();
  };

  const getSessionDuration = (): number => {
    if (!session) return 0;
    
    const now = new Date().getTime();
    const loginTime = new Date(session.loginTime).getTime();
    return now - loginTime;
  };

  const getTimeUntilExpiry = (): number => {
    if (!session) return 0;
    
    const now = new Date().getTime();
    const lastActivity = new Date(session.lastActivity).getTime();
    const timeRemaining = SESSION_TIMEOUT - (now - lastActivity);
    return Math.max(0, timeRemaining);
  };

  const isSessionValid = (): boolean => {
    if (!session) return false;
    
    const now = new Date().getTime();
    const lastActivity = new Date(session.lastActivity).getTime();
    return (now - lastActivity) < SESSION_TIMEOUT;
  };

  return {
    session,
    isLoading,
    saveSession,
    clearSession,
    extendSession,
    updateLastActivity,
    getSessionDuration,
    getTimeUntilExpiry,
    isSessionValid,
    user: session?.user || null,
    userType: session?.userType || null
  };
};

// Session warning hook for showing expiry warnings
export const useSessionWarning = () => {
  const { getTimeUntilExpiry, extendSession, clearSession } = useSessionPersistence();
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    const checkWarning = () => {
      const timeRemaining = getTimeUntilExpiry();
      const warningThreshold = 5 * 60 * 1000; // 5 minutes
      
      if (timeRemaining > 0 && timeRemaining <= warningThreshold) {
        setShowWarning(true);
      } else {
        setShowWarning(false);
      }
    };

    const warningInterval = setInterval(checkWarning, 30000); // Check every 30 seconds
    checkWarning(); // Initial check

    return () => clearInterval(warningInterval);
  }, [getTimeUntilExpiry]);

  const handleExtendSession = () => {
    extendSession();
    setShowWarning(false);
  };

  const handleLogout = () => {
    clearSession();
    setShowWarning(false);
  };

  return {
    showWarning,
    timeRemaining: getTimeUntilExpiry(),
    extendSession: handleExtendSession,
    logout: handleLogout
  };
};
