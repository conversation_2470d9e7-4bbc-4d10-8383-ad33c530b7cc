@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import tablet responsive styles */
@import './styles/tablet-responsive.css';

/* RTL Support */
* {
  direction: rtl;
}

body {
  font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;
  direction: rtl;
  text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Custom Button Styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Card Styles */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
}

.card-hover {
  @apply card hover:shadow-md transition-shadow duration-200;
}

/* Form Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Video Player Styles */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe,
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

/* Quiz Styles */
.quiz-option {
  @apply p-4 border-2 border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-300;
}

.quiz-option.selected {
  @apply border-primary-500 bg-primary-50;
}

.quiz-option.correct {
  @apply border-green-500 bg-green-50;
}

.quiz-option.incorrect {
  @apply border-red-500 bg-red-50;
}

/* Certificate Styles */
.certificate-container {
  @apply bg-white border-4 border-primary-600 rounded-lg p-8 text-center shadow-lg;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-menu {
    @apply fixed inset-0 z-50 bg-white;
  }

  .mobile-menu-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }

  /* Mobile-specific adjustments */
  .mobile-padding {
    @apply px-4 py-2;
  }

  .mobile-text {
    @apply text-sm;
  }

  .mobile-button {
    @apply py-3 px-4 text-base;
  }

  .mobile-card {
    @apply p-4 mx-2;
  }

  .mobile-grid {
    @apply grid-cols-1 gap-4;
  }

  .mobile-hidden {
    @apply hidden;
  }

  .mobile-full {
    @apply w-full;
  }
}

/* Tablet Responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid-cols-2 gap-6;
  }

  .tablet-padding {
    @apply px-6 py-4;
  }

  .tablet-text {
    @apply text-base;
  }

  .tablet-card {
    @apply p-6;
  }

  /* Touch-friendly buttons for tablets */
  .tablet-button {
    @apply py-3 px-6 text-base min-h-[44px] touch-manipulation;
  }

  /* Improved touch targets */
  .tablet-touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Better spacing for tablet layouts */
  .tablet-spacing {
    @apply space-y-4 space-x-4;
  }

  /* Tablet-specific sidebar width */
  .tablet-sidebar {
    @apply w-72;
  }

  /* Tablet modal adjustments */
  .tablet-modal {
    @apply max-w-2xl mx-4;
  }
}

/* Desktop Responsive */
@media (min-width: 1025px) {
  .desktop-grid {
    @apply grid-cols-3 gap-8;
  }

  .desktop-padding {
    @apply px-8 py-6;
  }

  .desktop-text {
    @apply text-lg;
  }

  .desktop-card {
    @apply p-8;
  }
}

/* Container Responsive Classes */
.container-responsive {
  @apply w-full mx-auto px-4;
  max-width: 1200px;
}

@media (min-width: 640px) {
  .container-responsive {
    @apply px-6;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    @apply px-8;
  }
}

/* Responsive Typography */
.text-responsive-xs {
  @apply text-xs sm:text-sm md:text-base;
}

.text-responsive-sm {
  @apply text-sm sm:text-base md:text-lg;
}

.text-responsive-base {
  @apply text-base sm:text-lg md:text-xl;
}

.text-responsive-lg {
  @apply text-lg sm:text-xl md:text-2xl;
}

.text-responsive-xl {
  @apply text-xl sm:text-2xl md:text-3xl;
}

.text-responsive-2xl {
  @apply text-2xl sm:text-3xl md:text-4xl;
}

/* Responsive Spacing */
.spacing-responsive-sm {
  @apply space-y-2 sm:space-y-3 md:space-y-4;
}

.spacing-responsive-md {
  @apply space-y-4 sm:space-y-6 md:space-y-8;
}

.spacing-responsive-lg {
  @apply space-y-6 sm:space-y-8 md:space-y-12;
}

/* Responsive Grid */
.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8;
}

.grid-responsive-2 {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6;
}

.grid-responsive-3 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
}

/* Responsive Flex */
.flex-responsive {
  @apply flex flex-col sm:flex-row items-center gap-4 sm:gap-6;
}

.flex-responsive-reverse {
  @apply flex flex-col-reverse sm:flex-row items-center gap-4 sm:gap-6;
}

/* Touch Optimizations */
.touch-friendly {
  @apply touch-manipulation;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.touch-button {
  @apply min-h-[44px] min-w-[44px] touch-manipulation;
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.2);
}

.touch-card {
  @apply touch-manipulation;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.05);
}

/* Improved scrolling for touch devices */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Better focus states for touch devices */
.focus-visible:focus-visible {
  @apply ring-2 ring-primary-500 ring-offset-2;
}

/* Prevent text selection on interactive elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }

  .dark-mode .card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark-mode .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}
