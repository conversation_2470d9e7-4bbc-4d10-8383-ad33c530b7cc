import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  TrophyIcon,
  PlayIcon,
  BookOpenIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  StarIcon,
  CalendarIcon,
  FireIcon
} from '@heroicons/react/24/outline';
import { Student } from '../../types';
import { supabaseService } from '../../services/supabaseService';
import { toast } from 'react-hot-toast';

interface StudentStats {
  totalCourses: number;
  completedCourses: number;
  inProgressCourses: number;
  totalVideos: number;
  completedVideos: number;
  totalWatchTime: number;
  certificates: number;
  averageGrade: number;
  currentStreak: number;
  totalQuizzes: number;
  completedQuizzes: number;
}

interface RecentActivity {
  id: string;
  type: 'video_completed' | 'quiz_completed' | 'course_completed' | 'certificate_earned';
  title: string;
  description: string;
  date: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface Props {
  user: Student;
}

const RealTimeStudentOverview: React.FC<Props> = ({ user }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<StudentStats>({
    totalCourses: 0,
    completedCourses: 0,
    inProgressCourses: 0,
    totalVideos: 0,
    completedVideos: 0,
    totalWatchTime: 0,
    certificates: 0,
    averageGrade: 0,
    currentStreak: 0,
    totalQuizzes: 0,
    completedQuizzes: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadStudentStats();
      loadRecentActivity();
    }
  }, [user]);

  const loadStudentStats = async () => {
    try {
      setLoading(true);

      // Get enrollments
      const { data: enrollments, error: enrollmentsError } = await supabaseService.supabase
        .from('student_enrollments')
        .select('*, courses(*)')
        .eq('student_id', user.id);

      if (enrollmentsError) throw enrollmentsError;

      // Get video progress
      const { data: videoProgress, error: videoError } = await supabaseService.supabase
        .from('video_progress')
        .select('*, videos(*)')
        .eq('student_id', user.id);

      if (videoError) throw videoError;

      // Get certificates
      const { data: certificates, error: certificatesError } = await supabaseService.supabase
        .from('certificates')
        .select('*')
        .eq('student_id', user.id);

      if (certificatesError) throw certificatesError;

      // Get quiz attempts
      const { data: quizAttempts, error: quizError } = await supabaseService.supabase
        .from('quiz_attempts')
        .select('*')
        .eq('student_id', user.id);

      if (quizError && quizError.code !== 'PGRST116') throw quizError;

      // Calculate stats
      const totalCourses = enrollments?.length || 0;
      const completedCourses = enrollments?.filter(e => e.progress === 100).length || 0;
      const inProgressCourses = enrollments?.filter(e => e.progress > 0 && e.progress < 100).length || 0;
      
      const completedVideos = videoProgress?.filter(vp => vp.completed).length || 0;
      const totalWatchTime = videoProgress?.reduce((sum, vp) => sum + (vp.watched_duration || 0), 0) || 0;
      
      const certificatesCount = certificates?.length || 0;
      const averageGrade = certificatesCount > 0 
        ? Math.round(certificates.reduce((sum, cert) => sum + cert.grade, 0) / certificatesCount)
        : 0;

      const completedQuizzes = quizAttempts?.filter(qa => qa.completed).length || 0;
      const totalQuizzes = new Set(quizAttempts?.map(qa => qa.quiz_id)).size || 0;

      // Calculate current streak (simplified)
      const currentStreak = calculateStreak(videoProgress || []);

      // Get total videos from enrolled courses
      let totalVideos = 0;
      if (enrollments) {
        for (const enrollment of enrollments) {
          const { data: courseVideos } = await supabaseService.supabase
            .from('videos')
            .select('id')
            .eq('course_id', enrollment.course_id);
          totalVideos += courseVideos?.length || 0;
        }
      }

      setStats({
        totalCourses,
        completedCourses,
        inProgressCourses,
        totalVideos,
        completedVideos,
        totalWatchTime,
        certificates: certificatesCount,
        averageGrade,
        currentStreak,
        totalQuizzes,
        completedQuizzes
      });

    } catch (error) {
      console.error('Error loading student stats:', error);
      toast.error('فشل في تحميل الإحصائيات');
    } finally {
      setLoading(false);
    }
  };

  const loadRecentActivity = async () => {
    try {
      const activities: RecentActivity[] = [];

      // Get recent video completions
      const { data: recentVideos } = await supabaseService.supabase
        .from('video_progress')
        .select('*, videos(*)')
        .eq('student_id', user.id)
        .eq('completed', true)
        .order('completed_at', { ascending: false })
        .limit(5);

      recentVideos?.forEach(vp => {
        if (vp.completed_at) {
          activities.push({
            id: `video-${vp.id}`,
            type: 'video_completed',
            title: 'إكمال فيديو',
            description: vp.videos?.title || 'فيديو غير محدد',
            date: vp.completed_at,
            icon: PlayIcon,
            color: 'text-blue-600 bg-blue-100'
          });
        }
      });

      // Get recent certificates
      const { data: recentCertificates } = await supabaseService.supabase
        .from('certificates')
        .select('*, courses(*)')
        .eq('student_id', user.id)
        .order('issued_at', { ascending: false })
        .limit(3);

      recentCertificates?.forEach(cert => {
        activities.push({
          id: `cert-${cert.id}`,
          type: 'certificate_earned',
          title: 'حصول على شهادة',
          description: cert.courses?.title || 'كورس غير محدد',
          date: cert.issued_at,
          icon: TrophyIcon,
          color: 'text-yellow-600 bg-yellow-100'
        });
      });

      // Sort by date and take latest 10
      activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      setRecentActivity(activities.slice(0, 10));

    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  };

  const calculateStreak = (videoProgress: any[]) => {
    // Simplified streak calculation based on consecutive days with video completions
    const completedDates = videoProgress
      .filter(vp => vp.completed && vp.completed_at)
      .map(vp => new Date(vp.completed_at).toDateString())
      .filter((date, index, array) => array.indexOf(date) === index)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toDateString();
    
    for (let i = 0; i < completedDates.length; i++) {
      const currentDate = new Date(completedDates[i]);
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() - i);
      
      if (currentDate.toDateString() === expectedDate.toDateString()) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  };

  const formatWatchTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours} ساعة و ${minutes} دقيقة`;
    }
    return `${minutes} دقيقة`;
  };

  const getProgressPercentage = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">مرحباً، {user.name}!</h1>
            <p className="text-primary-100">
              استمر في رحلتك التعليمية واحقق أهدافك
            </p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2 space-x-reverse mb-2">
              <FireIcon className="w-6 h-6 text-orange-300" />
              <span className="text-lg font-bold">{stats.currentStreak}</span>
            </div>
            <p className="text-sm text-primary-100">أيام متتالية</p>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BookOpenIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الكورسات</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
              <p className="text-xs text-gray-500">
                {stats.completedCourses} مكتمل • {stats.inProgressCourses} قيد التقدم
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <PlayIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الفيديوهات</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedVideos}</p>
              <p className="text-xs text-gray-500">
                من أصل {stats.totalVideos} فيديو
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ClockIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">وقت المشاهدة</p>
              <p className="text-lg font-bold text-gray-900">
                {formatWatchTime(stats.totalWatchTime)}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrophyIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الشهادات</p>
              <p className="text-2xl font-bold text-gray-900">{stats.certificates}</p>
              {stats.averageGrade > 0 && (
                <p className="text-xs text-gray-500">
                  متوسط الدرجات: {stats.averageGrade}%
                </p>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Course Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">تقدم الكورسات</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">الفيديوهات المكتملة</span>
                <span className="text-sm text-gray-500">
                  {getProgressPercentage(stats.completedVideos, stats.totalVideos)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getProgressPercentage(stats.completedVideos, stats.totalVideos)}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">الاختبارات المكتملة</span>
                <span className="text-sm text-gray-500">
                  {getProgressPercentage(stats.completedQuizzes, stats.totalQuizzes)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getProgressPercentage(stats.completedQuizzes, stats.totalQuizzes)}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">الكورسات المكتملة</span>
                <span className="text-sm text-gray-500">
                  {getProgressPercentage(stats.completedCourses, stats.totalCourses)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getProgressPercentage(stats.completedCourses, stats.totalCourses)}%` }}
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">النشاط الأخير</h3>
          
          <div className="space-y-3">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-2 rounded-lg ${activity.color}`}>
                    <activity.icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.title}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {activity.description}
                    </p>
                  </div>
                  <div className="text-xs text-gray-400">
                    {new Date(activity.date).toLocaleDateString('ar-SA')}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <ChartBarIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">لا يوجد نشاط حديث</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white rounded-lg p-6 shadow-sm border"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => navigate('/student/courses')}
            className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <BookOpenIcon className="w-6 h-6 text-blue-600 ml-3" />
            <div className="text-right">
              <p className="text-sm font-medium text-blue-900">كورساتي</p>
              <p className="text-xs text-blue-600">عرض جميع الكورسات</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/student/certificates')}
            className="flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
          >
            <TrophyIcon className="w-6 h-6 text-yellow-600 ml-3" />
            <div className="text-right">
              <p className="text-sm font-medium text-yellow-900">شهاداتي</p>
              <p className="text-xs text-yellow-600">عرض الشهادات</p>
            </div>
          </button>

          <button
            onClick={() => navigate('/student/profile')}
            className="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <AcademicCapIcon className="w-6 h-6 text-green-600 ml-3" />
            <div className="text-right">
              <p className="text-sm font-medium text-green-900">الملف الشخصي</p>
              <p className="text-xs text-green-600">تحديث البيانات</p>
            </div>
          </button>

          <div className="flex items-center p-4 bg-purple-50 rounded-lg">
            <StarIcon className="w-6 h-6 text-purple-600 ml-3" />
            <div className="text-right">
              <p className="text-sm font-medium text-purple-900">التقييم</p>
              <p className="text-xs text-purple-600">
                {stats.averageGrade > 0 ? `${stats.averageGrade}%` : 'لا يوجد'}
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default RealTimeStudentOverview;
