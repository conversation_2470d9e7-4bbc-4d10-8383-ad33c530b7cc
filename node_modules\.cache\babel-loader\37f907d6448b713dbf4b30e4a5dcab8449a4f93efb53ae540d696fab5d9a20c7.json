{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{toast}from'react-hot-toast';import{UserIcon,KeyIcon,EyeIcon,EyeSlashIcon,AcademicCapIcon,ShieldCheckIcon}from'@heroicons/react/24/outline';// Services\nimport{authService}from'../services/authService';// Hooks\nimport{useSessionPersistence}from'../hooks/useSessionPersistence';// Components\nimport AIAssistant from'../components/AIAssistant/AIAssistant';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=_ref=>{let{onLogin}=_ref;const{saveSession}=useSessionPersistence();const[loginType,setLoginType]=useState('student');const[loading,setLoading]=useState(false);const[showPassword,setShowPassword]=useState(false);// Admin login form\nconst[adminForm,setAdminForm]=useState({email:'',password:''});// Student login form\nconst[studentForm,setStudentForm]=useState({accessCode:''});const handleAdminLogin=async e=>{e.preventDefault();if(!adminForm.email||!adminForm.password){toast.error('يرجى ملء جميع الحقول');return;}setLoading(true);try{const user=await authService.loginAdmin(adminForm.email,adminForm.password);saveSession(user,'admin');toast.success(`مرحباً ${user.name||'المدير'}`);onLogin(user);}catch(error){toast.error(error.message);}finally{setLoading(false);}};const handleStudentLogin=async e=>{e.preventDefault();if(!studentForm.accessCode||studentForm.accessCode.length!==7){toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');return;}setLoading(true);try{const user=await authService.loginStudent(studentForm.accessCode);saveSession(user,'student');toast.success(`مرحباً ${user.name||'الطالب'}`);onLogin(user);}catch(error){toast.error(error.message);}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen gradient-primary flex items-center justify-center p-4 sm:p-6 lg:p-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full max-w-md lg:max-w-lg xl:max-w-xl\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:\"text-center mb-6 sm:mb-8 lg:mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mx-auto mb-3 sm:mb-4 lg:mb-6 flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-primary-600\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 font-display\",children:\"\\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 mb-1 text-sm sm:text-base lg:text-lg font-body\",children:\"\\u0644\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629 \\u0648\\u0627\\u0644\\u062A\\u0639\\u0644\\u0645 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0639\\u0644\\u064A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-200 text-xs sm:text-sm lg:text-base font-medium\",children:\"\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0641\\u0631\\u064A\\u0642 ALaa Abd Elhamied 2025\"})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"bg-white rounded-xl shadow-xl p-4 sm:p-6 lg:p-8 mb-4 sm:mb-6 lg:mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex rounded-lg bg-gray-100 p-1 mb-4 sm:mb-6\",children:[/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:()=>setLoginType('student'),className:`\n                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center\n                ${loginType==='student'?'bg-primary-600 text-white shadow-sm':'text-gray-600 hover:text-gray-800'}\n              `,children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden xs:inline\",children:\"\\u062F\\u062E\\u0648\\u0644 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"span\",{className:\"xs:hidden\",children:\"\\u0637\\u0627\\u0644\\u0628\"})]}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:()=>setLoginType('admin'),className:`\n                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center\n                ${loginType==='admin'?'bg-primary-600 text-white shadow-sm':'text-gray-600 hover:text-gray-800'}\n              `,children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden xs:inline\",children:\"\\u062F\\u062E\\u0648\\u0644 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"}),/*#__PURE__*/_jsx(\"span\",{className:\"xs:hidden\",children:\"\\u0645\\u062F\\u064A\\u0631\"})]})]}),loginType==='student'&&/*#__PURE__*/_jsxs(motion.form,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},onSubmit:handleStudentLogin,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 (7 \\u0623\\u0631\\u0642\\u0627\\u0645)\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(KeyIcon,{className:\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:studentForm.accessCode,onChange:e=>{const value=e.target.value.replace(/\\D/g,'').slice(0,7);setStudentForm({accessCode:value});},placeholder:\"1234567\",className:\"form-input pr-10 text-center text-lg font-mono tracking-wider\",maxLength:7,required:true})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"\\u064A\\u0645\\u0643\\u0646\\u0643 \\u0627\\u0644\\u062D\\u0635\\u0648\\u0644 \\u0639\\u0644\\u0649 \\u0643\\u0648\\u062F \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading||studentForm.accessCode.length!==7,className:\"w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm sm:text-base\",children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"})]}):'دخول'})]}),loginType==='admin'&&/*#__PURE__*/_jsxs(motion.form,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},onSubmit:handleAdminLogin,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:adminForm.email,onChange:e=>setAdminForm({...adminForm,email:e.target.value}),placeholder:\"<EMAIL>\",className:\"form-input pr-10\",required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(KeyIcon,{className:\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:showPassword?'text':'password',value:adminForm.password,onChange:e=>setAdminForm({...adminForm,password:e.target.value}),placeholder:\"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",className:\"form-input pr-10 pl-10\",required:true}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowPassword(!showPassword),className:\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",children:showPassword?/*#__PURE__*/_jsx(EyeSlashIcon,{className:\"w-5 h-5\"}):/*#__PURE__*/_jsx(EyeIcon,{className:\"w-5 h-5\"})})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading||!adminForm.email||!adminForm.password,className:\"w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm sm:text-base\",children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644...\"})]}):'دخول'})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:0.3},className:\"text-center text-blue-100 text-xs sm:text-sm lg:text-base space-y-1 sm:space-y-2 px-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium\",children:\"\\xA9 2025 \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied. \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-200 text-xs sm:text-sm\",children:\"\\u062A\\u0645 \\u0627\\u0644\\u062A\\u0635\\u0645\\u064A\\u0645 \\u0648\\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0628\\u0648\\u0627\\u0633\\u0637\\u0629 \\u0641\\u0631\\u064A\\u0642 ALaa Abd Elhamied 2025\"})]})]}),/*#__PURE__*/_jsx(AIAssistant,{context:\"login\"})]});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "toast", "UserIcon", "KeyIcon", "EyeIcon", "EyeSlashIcon", "AcademicCapIcon", "ShieldCheckIcon", "authService", "useSessionPersistence", "AIAssistant", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "_ref", "onLogin", "saveSession", "loginType", "setLoginType", "loading", "setLoading", "showPassword", "setShowPassword", "adminForm", "setAdminForm", "email", "password", "studentForm", "setStudentForm", "accessCode", "handleAdminLogin", "e", "preventDefault", "error", "user", "loginAdmin", "success", "name", "message", "handleStudentLogin", "length", "loginStudent", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "delay", "type", "onClick", "form", "x", "onSubmit", "value", "onChange", "target", "replace", "slice", "placeholder", "max<PERSON><PERSON><PERSON>", "required", "disabled", "context"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'react-hot-toast';\nimport { \n  UserIcon, \n  KeyIcon, \n  EyeIcon, \n  EyeSlashIcon,\n  AcademicCapIcon,\n  ShieldCheckIcon\n} from '@heroicons/react/24/outline';\n\n// Services\nimport { authService } from '../services/authService';\n\n// Hooks\nimport { useSessionPersistence } from '../hooks/useSessionPersistence';\n\n// Components\nimport AIAssistant from '../components/AIAssistant/AIAssistant';\n\n// Types\nimport { User } from '../types';\n\ninterface LoginPageProps {\n  onLogin: (user: User) => void;\n}\n\nconst LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {\n  const { saveSession } = useSessionPersistence();\n  const [loginType, setLoginType] = useState<'admin' | 'student'>('student');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  \n  // Admin login form\n  const [adminForm, setAdminForm] = useState({\n    email: '',\n    password: ''\n  });\n  \n  // Student login form\n  const [studentForm, setStudentForm] = useState({\n    accessCode: ''\n  });\n\n  const handleAdminLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!adminForm.email || !adminForm.password) {\n      toast.error('يرجى ملء جميع الحقول');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const user = await authService.loginAdmin(adminForm.email, adminForm.password);\n      saveSession(user, 'admin');\n      toast.success(`مرحباً ${user.name || 'المدير'}`);\n      onLogin(user);\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStudentLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!studentForm.accessCode || studentForm.accessCode.length !== 7) {\n      toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const user = await authService.loginStudent(studentForm.accessCode);\n      saveSession(user, 'student');\n      toast.success(`مرحباً ${user.name || 'الطالب'}`);\n      onLogin(user);\n    } catch (error: any) {\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen gradient-primary flex items-center justify-center p-4 sm:p-6 lg:p-8\">\n      <div className=\"w-full max-w-md lg:max-w-lg xl:max-w-xl\">\n        {/* Logo and Title */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-6 sm:mb-8 lg:mb-10\"\n        >\n          <div className=\"bg-white rounded-full w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mx-auto mb-3 sm:mb-4 lg:mb-6 flex items-center justify-center shadow-lg\">\n            <AcademicCapIcon className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-primary-600\" />\n          </div>\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 font-display\">\n            منصة ALaa Abd Hamied\n          </h1>\n          <p className=\"text-blue-100 mb-1 text-sm sm:text-base lg:text-lg font-body\">\n            للكورسات الإلكترونية والتعلم التفاعلي\n          </p>\n          <p className=\"text-blue-200 text-xs sm:text-sm lg:text-base font-medium\">\n            تطوير فريق ALaa Abd Elhamied 2025\n          </p>\n        </motion.div>\n\n        {/* Login Type Selector */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n          className=\"bg-white rounded-xl shadow-xl p-4 sm:p-6 lg:p-8 mb-4 sm:mb-6 lg:mb-8\"\n        >\n          <div className=\"flex rounded-lg bg-gray-100 p-1 mb-4 sm:mb-6\">\n            <button\n              type=\"button\"\n              onClick={() => setLoginType('student')}\n              className={`\n                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center\n                ${loginType === 'student'\n                  ? 'bg-primary-600 text-white shadow-sm'\n                  : 'text-gray-600 hover:text-gray-800'\n                }\n              `}\n            >\n              <UserIcon className=\"w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2\" />\n              <span className=\"hidden xs:inline\">دخول الطالب</span>\n              <span className=\"xs:hidden\">طالب</span>\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setLoginType('admin')}\n              className={`\n                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center\n                ${loginType === 'admin'\n                  ? 'bg-primary-600 text-white shadow-sm'\n                  : 'text-gray-600 hover:text-gray-800'\n                }\n              `}\n            >\n              <ShieldCheckIcon className=\"w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2\" />\n              <span className=\"hidden xs:inline\">دخول المدير</span>\n              <span className=\"xs:hidden\">مدير</span>\n            </button>\n          </div>\n\n          {/* Student Login Form */}\n          {loginType === 'student' && (\n            <motion.form \n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              onSubmit={handleStudentLogin}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"form-label\">\n                  كود الدخول (7 أرقام)\n                </label>\n                <div className=\"relative\">\n                  <KeyIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    value={studentForm.accessCode}\n                    onChange={(e) => {\n                      const value = e.target.value.replace(/\\D/g, '').slice(0, 7);\n                      setStudentForm({ accessCode: value });\n                    }}\n                    placeholder=\"1234567\"\n                    className=\"form-input pr-10 text-center text-lg font-mono tracking-wider\"\n                    maxLength={7}\n                    required\n                  />\n                </div>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  يمكنك الحصول على كود الدخول من المدير\n                </p>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={loading || studentForm.accessCode.length !== 7}\n                className=\"w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\" />\n                    <span className=\"text-sm sm:text-base\">جاري تسجيل الدخول...</span>\n                  </div>\n                ) : (\n                  'دخول'\n                )}\n              </button>\n            </motion.form>\n          )}\n\n          {/* Admin Login Form */}\n          {loginType === 'admin' && (\n            <motion.form \n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              onSubmit={handleAdminLogin}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"form-label\">\n                  البريد الإلكتروني\n                </label>\n                <div className=\"relative\">\n                  <UserIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type=\"email\"\n                    value={adminForm.email}\n                    onChange={(e) => setAdminForm({ ...adminForm, email: e.target.value })}\n                    placeholder=\"<EMAIL>\"\n                    className=\"form-input pr-10\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"form-label\">\n                  كلمة المرور\n                </label>\n                <div className=\"relative\">\n                  <KeyIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    value={adminForm.password}\n                    onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}\n                    placeholder=\"••••••••\"\n                    className=\"form-input pr-10 pl-10\"\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"w-5 h-5\" />\n                    ) : (\n                      <EyeIcon className=\"w-5 h-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={loading || !adminForm.email || !adminForm.password}\n                className=\"w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\" />\n                    <span className=\"text-sm sm:text-base\">جاري تسجيل الدخول...</span>\n                  </div>\n                ) : (\n                  'دخول'\n                )}\n              </button>\n            </motion.form>\n          )}\n        </motion.div>\n\n        {/* Footer */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className=\"text-center text-blue-100 text-xs sm:text-sm lg:text-base space-y-1 sm:space-y-2 px-4\"\n        >\n          <p className=\"font-medium\">© 2025 منصة ALaa Abd Hamied. جميع الحقوق محفوظة.</p>\n          <p className=\"text-blue-200 text-xs sm:text-sm\">\n            تم التصميم والتطوير بواسطة فريق ALaa Abd Elhamied 2025\n          </p>\n        </motion.div>\n      </div>\n\n      {/* AI Assistant */}\n      <AIAssistant context=\"login\" />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OACEC,QAAQ,CACRC,OAAO,CACPC,OAAO,CACPC,YAAY,CACZC,eAAe,CACfC,eAAe,KACV,6BAA6B,CAEpC;AACA,OAASC,WAAW,KAAQ,yBAAyB,CAErD;AACA,OAASC,qBAAqB,KAAQ,gCAAgC,CAEtE;AACA,MAAO,CAAAC,WAAW,KAAM,uCAAuC,CAE/D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CACtD,KAAM,CAAEE,WAAY,CAAC,CAAGT,qBAAqB,CAAC,CAAC,CAC/C,KAAM,CAACU,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAsB,SAAS,CAAC,CAC1E,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,CACzC4B,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG/B,QAAQ,CAAC,CAC7CgC,UAAU,CAAE,EACd,CAAC,CAAC,CAEF,KAAM,CAAAC,gBAAgB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACrDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACT,SAAS,CAACE,KAAK,EAAI,CAACF,SAAS,CAACG,QAAQ,CAAE,CAC3C3B,KAAK,CAACkC,KAAK,CAAC,sBAAsB,CAAC,CACnC,OACF,CAEAb,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAc,IAAI,CAAG,KAAM,CAAA5B,WAAW,CAAC6B,UAAU,CAACZ,SAAS,CAACE,KAAK,CAAEF,SAAS,CAACG,QAAQ,CAAC,CAC9EV,WAAW,CAACkB,IAAI,CAAE,OAAO,CAAC,CAC1BnC,KAAK,CAACqC,OAAO,CAAC,UAAUF,IAAI,CAACG,IAAI,EAAI,QAAQ,EAAE,CAAC,CAChDtB,OAAO,CAACmB,IAAI,CAAC,CACf,CAAE,MAAOD,KAAU,CAAE,CACnBlC,KAAK,CAACkC,KAAK,CAACA,KAAK,CAACK,OAAO,CAAC,CAC5B,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmB,kBAAkB,CAAG,KAAO,CAAAR,CAAkB,EAAK,CACvDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACL,WAAW,CAACE,UAAU,EAAIF,WAAW,CAACE,UAAU,CAACW,MAAM,GAAK,CAAC,CAAE,CAClEzC,KAAK,CAACkC,KAAK,CAAC,0CAA0C,CAAC,CACvD,OACF,CAEAb,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAc,IAAI,CAAG,KAAM,CAAA5B,WAAW,CAACmC,YAAY,CAACd,WAAW,CAACE,UAAU,CAAC,CACnEb,WAAW,CAACkB,IAAI,CAAE,SAAS,CAAC,CAC5BnC,KAAK,CAACqC,OAAO,CAAC,UAAUF,IAAI,CAACG,IAAI,EAAI,QAAQ,EAAE,CAAC,CAChDtB,OAAO,CAACmB,IAAI,CAAC,CACf,CAAE,MAAOD,KAAU,CAAE,CACnBlC,KAAK,CAACkC,KAAK,CAACA,KAAK,CAACK,OAAO,CAAC,CAC5B,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACER,KAAA,QAAK8B,SAAS,CAAC,kFAAkF,CAAAC,QAAA,eAC/F/B,KAAA,QAAK8B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtD/B,KAAA,CAACd,MAAM,CAAC8C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BL,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7CjC,IAAA,QAAKgC,SAAS,CAAC,yIAAyI,CAAAC,QAAA,cACtJjC,IAAA,CAACN,eAAe,EAACsC,SAAS,CAAC,0DAA0D,CAAE,CAAC,CACrF,CAAC,cACNhC,IAAA,OAAIgC,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CAAC,0CAExF,CAAI,CAAC,cACLjC,IAAA,MAAGgC,SAAS,CAAC,8DAA8D,CAAAC,QAAA,CAAC,iNAE5E,CAAG,CAAC,cACJjC,IAAA,MAAGgC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,gFAEzE,CAAG,CAAC,EACM,CAAC,cAGb/B,KAAA,CAACd,MAAM,CAAC8C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BR,SAAS,CAAC,sEAAsE,CAAAC,QAAA,eAEhF/B,KAAA,QAAK8B,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAC3D/B,KAAA,WACEuC,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMlC,YAAY,CAAC,SAAS,CAAE,CACvCwB,SAAS,CAAE;AACzB;AACA,kBAAkBzB,SAAS,GAAK,SAAS,CACrB,qCAAqC,CACrC,mCAAmC;AACvD,eACgB,CAAA0B,QAAA,eAEFjC,IAAA,CAACV,QAAQ,EAAC0C,SAAS,CAAC,iDAAiD,CAAE,CAAC,cACxEhC,IAAA,SAAMgC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,+DAAW,CAAM,CAAC,cACrDjC,IAAA,SAAMgC,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,EACjC,CAAC,cACT/B,KAAA,WACEuC,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAMlC,YAAY,CAAC,OAAO,CAAE,CACrCwB,SAAS,CAAE;AACzB;AACA,kBAAkBzB,SAAS,GAAK,OAAO,CACnB,qCAAqC,CACrC,mCAAmC;AACvD,eACgB,CAAA0B,QAAA,eAEFjC,IAAA,CAACL,eAAe,EAACqC,SAAS,CAAC,iDAAiD,CAAE,CAAC,cAC/EhC,IAAA,SAAMgC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,+DAAW,CAAM,CAAC,cACrDjC,IAAA,SAAMgC,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,EACjC,CAAC,EACN,CAAC,CAGL1B,SAAS,GAAK,SAAS,eACtBL,KAAA,CAACd,MAAM,CAACuD,IAAI,EACVR,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEQ,CAAC,CAAE,EAAG,CAAE,CAC/BN,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEQ,CAAC,CAAE,CAAE,CAAE,CAC9BC,QAAQ,CAAEhB,kBAAmB,CAC7BG,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErB/B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,UAAOgC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,4FAE9B,CAAO,CAAC,cACR/B,KAAA,QAAK8B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjC,IAAA,CAACT,OAAO,EAACyC,SAAS,CAAC,2EAA2E,CAAE,CAAC,cACjGhC,IAAA,UACEyC,IAAI,CAAC,MAAM,CACXK,KAAK,CAAE7B,WAAW,CAACE,UAAW,CAC9B4B,QAAQ,CAAG1B,CAAC,EAAK,CACf,KAAM,CAAAyB,KAAK,CAAGzB,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAACG,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAC3DhC,cAAc,CAAC,CAAEC,UAAU,CAAE2B,KAAM,CAAC,CAAC,CACvC,CAAE,CACFK,WAAW,CAAC,SAAS,CACrBnB,SAAS,CAAC,+DAA+D,CACzEoB,SAAS,CAAE,CAAE,CACbC,QAAQ,MACT,CAAC,EACC,CAAC,cACNrD,IAAA,MAAGgC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kMAE1C,CAAG,CAAC,EACD,CAAC,cAENjC,IAAA,WACEyC,IAAI,CAAC,QAAQ,CACba,QAAQ,CAAE7C,OAAO,EAAIQ,WAAW,CAACE,UAAU,CAACW,MAAM,GAAK,CAAE,CACzDE,SAAS,CAAC,6HAA6H,CAAAC,QAAA,CAEtIxB,OAAO,cACNP,KAAA,QAAK8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CjC,IAAA,QAAKgC,SAAS,CAAC,iGAAiG,CAAE,CAAC,cACnHhC,IAAA,SAAMgC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,iGAAoB,CAAM,CAAC,EAC/D,CAAC,CAEN,MACD,CACK,CAAC,EACE,CACd,CAGA1B,SAAS,GAAK,OAAO,eACpBL,KAAA,CAACd,MAAM,CAACuD,IAAI,EACVR,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEQ,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCN,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEQ,CAAC,CAAE,CAAE,CAAE,CAC9BC,QAAQ,CAAEzB,gBAAiB,CAC3BY,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErB/B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,UAAOgC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,mGAE9B,CAAO,CAAC,cACR/B,KAAA,QAAK8B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjC,IAAA,CAACV,QAAQ,EAAC0C,SAAS,CAAC,2EAA2E,CAAE,CAAC,cAClGhC,IAAA,UACEyC,IAAI,CAAC,OAAO,CACZK,KAAK,CAAEjC,SAAS,CAACE,KAAM,CACvBgC,QAAQ,CAAG1B,CAAC,EAAKP,YAAY,CAAC,CAAE,GAAGD,SAAS,CAAEE,KAAK,CAAEM,CAAC,CAAC2B,MAAM,CAACF,KAAM,CAAC,CAAE,CACvEK,WAAW,CAAC,mBAAmB,CAC/BnB,SAAS,CAAC,kBAAkB,CAC5BqB,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,cAENnD,KAAA,QAAA+B,QAAA,eACEjC,IAAA,UAAOgC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,+DAE9B,CAAO,CAAC,cACR/B,KAAA,QAAK8B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjC,IAAA,CAACT,OAAO,EAACyC,SAAS,CAAC,2EAA2E,CAAE,CAAC,cACjGhC,IAAA,UACEyC,IAAI,CAAE9B,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCmC,KAAK,CAAEjC,SAAS,CAACG,QAAS,CAC1B+B,QAAQ,CAAG1B,CAAC,EAAKP,YAAY,CAAC,CAAE,GAAGD,SAAS,CAAEG,QAAQ,CAAEK,CAAC,CAAC2B,MAAM,CAACF,KAAM,CAAC,CAAE,CAC1EK,WAAW,CAAC,kDAAU,CACtBnB,SAAS,CAAC,wBAAwB,CAClCqB,QAAQ,MACT,CAAC,cACFrD,IAAA,WACEyC,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAEA,CAAA,GAAM9B,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CqB,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CAE/FtB,YAAY,cACXX,IAAA,CAACP,YAAY,EAACuC,SAAS,CAAC,SAAS,CAAE,CAAC,cAEpChC,IAAA,CAACR,OAAO,EAACwC,SAAS,CAAC,SAAS,CAAE,CAC/B,CACK,CAAC,EACN,CAAC,EACH,CAAC,cAENhC,IAAA,WACEyC,IAAI,CAAC,QAAQ,CACba,QAAQ,CAAE7C,OAAO,EAAI,CAACI,SAAS,CAACE,KAAK,EAAI,CAACF,SAAS,CAACG,QAAS,CAC7DgB,SAAS,CAAC,6HAA6H,CAAAC,QAAA,CAEtIxB,OAAO,cACNP,KAAA,QAAK8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CjC,IAAA,QAAKgC,SAAS,CAAC,iGAAiG,CAAE,CAAC,cACnHhC,IAAA,SAAMgC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,iGAAoB,CAAM,CAAC,EAC/D,CAAC,CAEN,MACD,CACK,CAAC,EACE,CACd,EACS,CAAC,cAGb/B,KAAA,CAACd,MAAM,CAAC8C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BR,SAAS,CAAC,uFAAuF,CAAAC,QAAA,eAEjGjC,IAAA,MAAGgC,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,yJAAgD,CAAG,CAAC,cAC/EjC,IAAA,MAAGgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,+LAEhD,CAAG,CAAC,EACM,CAAC,EACV,CAAC,cAGNjC,IAAA,CAACF,WAAW,EAACyD,OAAO,CAAC,OAAO,CAAE,CAAC,EAC5B,CAAC,CAEV,CAAC,CAED,cAAe,CAAApD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}