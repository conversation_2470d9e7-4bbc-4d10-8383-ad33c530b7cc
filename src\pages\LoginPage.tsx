import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { 
  UserIcon, 
  KeyIcon, 
  EyeIcon, 
  EyeSlashIcon,
  AcademicCapIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

// Services
import { authService } from '../services/authService';

// Hooks
import { useSessionPersistence } from '../hooks/useSessionPersistence';

// Components
import AIAssistant from '../components/AIAssistant/AIAssistant';

// Types
import { User } from '../types';

interface LoginPageProps {
  onLogin: (user: User) => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const { saveSession } = useSessionPersistence();
  const [loginType, setLoginType] = useState<'admin' | 'student'>('student');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    accessCode: ''
  });

  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!adminForm.email || !adminForm.password) {
      toast.error('يرجى ملء جميع الحقول');
      return;
    }

    setLoading(true);
    try {
      const user = await authService.loginAdmin(adminForm.email, adminForm.password);
      saveSession(user, 'admin');
      toast.success(`مرحباً ${user.name || 'المدير'}`);
      onLogin(user);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStudentLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!studentForm.accessCode || studentForm.accessCode.length !== 7) {
      toast.error('يرجى إدخال كود دخول صحيح مكون من 7 أرقام');
      return;
    }

    setLoading(true);
    try {
      const user = await authService.loginStudent(studentForm.accessCode);
      saveSession(user, 'student');
      toast.success(`مرحباً ${user.name || 'الطالب'}`);
      onLogin(user);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen gradient-primary flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-md lg:max-w-lg xl:max-w-xl">
        {/* Logo and Title */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-6 sm:mb-8 lg:mb-10"
        >
          <div className="bg-white rounded-full w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mx-auto mb-3 sm:mb-4 lg:mb-6 flex items-center justify-center shadow-lg">
            <AcademicCapIcon className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-primary-600" />
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 font-display">
            منصة ALaa Abd Hamied
          </h1>
          <p className="text-blue-100 mb-1 text-sm sm:text-base lg:text-lg font-body">
            للكورسات الإلكترونية والتعلم التفاعلي
          </p>
          <p className="text-blue-200 text-xs sm:text-sm lg:text-base font-medium">
            تطوير فريق ALaa Abd Elhamied 2025
          </p>
        </motion.div>

        {/* Login Type Selector */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-xl p-4 sm:p-6 lg:p-8 mb-4 sm:mb-6 lg:mb-8"
        >
          <div className="flex rounded-lg bg-gray-100 p-1 mb-4 sm:mb-6">
            <button
              type="button"
              onClick={() => setLoginType('student')}
              className={`
                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center
                ${loginType === 'student'
                  ? 'bg-primary-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <UserIcon className="w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2" />
              <span className="hidden xs:inline">دخول الطالب</span>
              <span className="xs:hidden">طالب</span>
            </button>
            <button
              type="button"
              onClick={() => setLoginType('admin')}
              className={`
                flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-all duration-200 flex items-center justify-center
                ${loginType === 'admin'
                  ? 'bg-primary-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <ShieldCheckIcon className="w-3 h-3 sm:w-4 sm:h-4 inline-block ml-1 sm:ml-2" />
              <span className="hidden xs:inline">دخول المدير</span>
              <span className="xs:hidden">مدير</span>
            </button>
          </div>

          {/* Student Login Form */}
          {loginType === 'student' && (
            <motion.form 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              onSubmit={handleStudentLogin}
              className="space-y-4"
            >
              <div>
                <label className="form-label">
                  كود الدخول (7 أرقام)
                </label>
                <div className="relative">
                  <KeyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={studentForm.accessCode}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 7);
                      setStudentForm({ accessCode: value });
                    }}
                    placeholder="1234567"
                    className="form-input pr-10 text-center text-lg font-mono tracking-wider"
                    maxLength={7}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  يمكنك الحصول على كود الدخول من المدير
                </p>
              </div>

              <button
                type="submit"
                disabled={loading || studentForm.accessCode.length !== 7}
                className="w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2" />
                    <span className="text-sm sm:text-base">جاري تسجيل الدخول...</span>
                  </div>
                ) : (
                  'دخول'
                )}
              </button>
            </motion.form>
          )}

          {/* Admin Login Form */}
          {loginType === 'admin' && (
            <motion.form 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              onSubmit={handleAdminLogin}
              className="space-y-4"
            >
              <div>
                <label className="form-label">
                  البريد الإلكتروني
                </label>
                <div className="relative">
                  <UserIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="email"
                    value={adminForm.email}
                    onChange={(e) => setAdminForm({ ...adminForm, email: e.target.value })}
                    placeholder="<EMAIL>"
                    className="form-input pr-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="form-label">
                  كلمة المرور
                </label>
                <div className="relative">
                  <KeyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={adminForm.password}
                    onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}
                    placeholder="••••••••"
                    className="form-input pr-10 pl-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="w-5 h-5" />
                    ) : (
                      <EyeIcon className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading || !adminForm.email || !adminForm.password}
                className="w-full btn-primary py-3 sm:py-4 text-base sm:text-lg lg:text-xl disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin ml-2" />
                    <span className="text-sm sm:text-base">جاري تسجيل الدخول...</span>
                  </div>
                ) : (
                  'دخول'
                )}
              </button>
            </motion.form>
          )}
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-center text-blue-100 text-xs sm:text-sm lg:text-base space-y-1 sm:space-y-2 px-4"
        >
          <p className="font-medium">© 2025 منصة ALaa Abd Hamied. جميع الحقوق محفوظة.</p>
          <p className="text-blue-200 text-xs sm:text-sm">
            تم التصميم والتطوير بواسطة فريق ALaa Abd Elhamied 2025
          </p>
        </motion.div>
      </div>

      {/* AI Assistant */}
      <AIAssistant context="login" />
    </div>
  );
};

export default LoginPage;
