{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{jsx as _jsx}from\"react/jsx-runtime\";const ResponsiveCard=_ref=>{let{children,className='',padding='md',shadow='md',rounded='lg',border=true,hover=false,animated=false,animationDelay=0,onClick,gradient=false}=_ref;const baseClasses='bg-white transition-all duration-200';const paddingClasses={none:'',sm:'p-3 sm:p-4',md:'p-4 sm:p-6',lg:'p-6 sm:p-8',xl:'p-8 sm:p-10'};const shadowClasses={none:'',sm:'shadow-sm',md:'shadow-md',lg:'shadow-lg',xl:'shadow-xl'};const roundedClasses={none:'',sm:'rounded-sm',md:'rounded-md',lg:'rounded-lg',xl:'rounded-xl',full:'rounded-full'};const borderClass=border?'border border-gray-200':'';const hoverClass=hover?'hover:shadow-lg hover:scale-105 cursor-pointer touch-manipulation':'';const gradientClass=gradient?'bg-gradient-to-br from-white to-gray-50':'';const clickableClass=onClick?'cursor-pointer touch-manipulation':'';const cardClasses=`\n    ${baseClasses}\n    ${paddingClasses[padding]}\n    ${shadowClasses[shadow]}\n    ${roundedClasses[rounded]}\n    ${borderClass}\n    ${hoverClass}\n    ${gradientClass}\n    ${clickableClass}\n    ${className}\n  `.trim();const cardContent=/*#__PURE__*/_jsx(\"div\",{className:cardClasses,onClick:onClick,children:children});if(animated){return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:animationDelay,duration:0.6},whileHover:hover?{y:-5}:{},children:cardContent});}return cardContent;};export default ResponsiveCard;", "map": {"version": 3, "names": ["React", "motion", "jsx", "_jsx", "ResponsiveCard", "_ref", "children", "className", "padding", "shadow", "rounded", "border", "hover", "animated", "animationDelay", "onClick", "gradient", "baseClasses", "paddingClasses", "none", "sm", "md", "lg", "xl", "shadowClasses", "roundedClasses", "full", "borderClass", "hoverClass", "gradientClass", "clickableClass", "cardClasses", "trim", "cardContent", "div", "initial", "opacity", "y", "animate", "transition", "delay", "duration", "whileHover"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/ResponsiveCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ResponsiveCardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';\n  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';\n  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\n  border?: boolean;\n  hover?: boolean;\n  animated?: boolean;\n  animationDelay?: number;\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst ResponsiveCard: React.FC<ResponsiveCardProps> = ({\n  children,\n  className = '',\n  padding = 'md',\n  shadow = 'md',\n  rounded = 'lg',\n  border = true,\n  hover = false,\n  animated = false,\n  animationDelay = 0,\n  onClick,\n  gradient = false\n}) => {\n  const baseClasses = 'bg-white transition-all duration-200';\n\n  const paddingClasses = {\n    none: '',\n    sm: 'p-3 sm:p-4',\n    md: 'p-4 sm:p-6',\n    lg: 'p-6 sm:p-8',\n    xl: 'p-8 sm:p-10'\n  };\n\n  const shadowClasses = {\n    none: '',\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg',\n    xl: 'shadow-xl'\n  };\n\n  const roundedClasses = {\n    none: '',\n    sm: 'rounded-sm',\n    md: 'rounded-md',\n    lg: 'rounded-lg',\n    xl: 'rounded-xl',\n    full: 'rounded-full'\n  };\n\n  const borderClass = border ? 'border border-gray-200' : '';\n  const hoverClass = hover ? 'hover:shadow-lg hover:scale-105 cursor-pointer touch-manipulation' : '';\n  const gradientClass = gradient ? 'bg-gradient-to-br from-white to-gray-50' : '';\n  const clickableClass = onClick ? 'cursor-pointer touch-manipulation' : '';\n\n  const cardClasses = `\n    ${baseClasses}\n    ${paddingClasses[padding]}\n    ${shadowClasses[shadow]}\n    ${roundedClasses[rounded]}\n    ${borderClass}\n    ${hoverClass}\n    ${gradientClass}\n    ${clickableClass}\n    ${className}\n  `.trim();\n\n  const cardContent = (\n    <div className={cardClasses} onClick={onClick}>\n      {children}\n    </div>\n  );\n\n  if (animated) {\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: animationDelay, duration: 0.6 }}\n        whileHover={hover ? { y: -5 } : {}}\n      >\n        {cardContent}\n      </motion.div>\n    );\n  }\n\n  return cardContent;\n};\n\nexport default ResponsiveCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAgBvC,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAYhD,IAZiD,CACrDC,QAAQ,CACRC,SAAS,CAAG,EAAE,CACdC,OAAO,CAAG,IAAI,CACdC,MAAM,CAAG,IAAI,CACbC,OAAO,CAAG,IAAI,CACdC,MAAM,CAAG,IAAI,CACbC,KAAK,CAAG,KAAK,CACbC,QAAQ,CAAG,KAAK,CAChBC,cAAc,CAAG,CAAC,CAClBC,OAAO,CACPC,QAAQ,CAAG,KACb,CAAC,CAAAX,IAAA,CACC,KAAM,CAAAY,WAAW,CAAG,sCAAsC,CAE1D,KAAM,CAAAC,cAAc,CAAG,CACrBC,IAAI,CAAE,EAAE,CACRC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,aACN,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,CACpBL,IAAI,CAAE,EAAE,CACRC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WACN,CAAC,CAED,KAAM,CAAAE,cAAc,CAAG,CACrBN,IAAI,CAAE,EAAE,CACRC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,YAAY,CAChBG,IAAI,CAAE,cACR,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGhB,MAAM,CAAG,wBAAwB,CAAG,EAAE,CAC1D,KAAM,CAAAiB,UAAU,CAAGhB,KAAK,CAAG,mEAAmE,CAAG,EAAE,CACnG,KAAM,CAAAiB,aAAa,CAAGb,QAAQ,CAAG,yCAAyC,CAAG,EAAE,CAC/E,KAAM,CAAAc,cAAc,CAAGf,OAAO,CAAG,mCAAmC,CAAG,EAAE,CAEzE,KAAM,CAAAgB,WAAW,CAAG;AACtB,MAAMd,WAAW;AACjB,MAAMC,cAAc,CAACV,OAAO,CAAC;AAC7B,MAAMgB,aAAa,CAACf,MAAM,CAAC;AAC3B,MAAMgB,cAAc,CAACf,OAAO,CAAC;AAC7B,MAAMiB,WAAW;AACjB,MAAMC,UAAU;AAChB,MAAMC,aAAa;AACnB,MAAMC,cAAc;AACpB,MAAMvB,SAAS;AACf,GAAG,CAACyB,IAAI,CAAC,CAAC,CAER,KAAM,CAAAC,WAAW,cACf9B,IAAA,QAAKI,SAAS,CAAEwB,WAAY,CAAChB,OAAO,CAAEA,OAAQ,CAAAT,QAAA,CAC3CA,QAAQ,CACN,CACN,CAED,GAAIO,QAAQ,CAAE,CACZ,mBACEV,IAAA,CAACF,MAAM,CAACiC,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE1B,cAAc,CAAE2B,QAAQ,CAAE,GAAI,CAAE,CACrDC,UAAU,CAAE9B,KAAK,CAAG,CAAEyB,CAAC,CAAE,CAAC,CAAE,CAAC,CAAG,CAAC,CAAE,CAAA/B,QAAA,CAElC2B,WAAW,CACF,CAAC,CAEjB,CAEA,MAAO,CAAAA,WAAW,CACpB,CAAC,CAED,cAAe,CAAA7B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}