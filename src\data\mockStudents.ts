import { Student } from '../types';

export const mockStudents: Student[] = [
  {
    id: 'b4d6f8a0-c2e4-4f6a-b8c0-e2f4a6b8c0d2',
    email: '<EMAIL>',
    name: 'أحم<PERSON> محمد علي',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    accessCode: '1234567',
    enrolledCourses: ['138ec959-163c-4ce9-9ad3-2b0597bdcfea', '41c61139-b980-4f5d-8036-c006b1ed6bc2'],
    completedCourses: ['138ec959-163c-4ce9-9ad3-2b0597bdcfea'],
    certificates: ['f3a5b7c9-d1e3-4f5a-b7c9-d1e3f5a7b9c1'],
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'c5e7f9b1-d3f5-5a7c-c9e1-f3f5a7b9c1d3',
    email: '<EMAIL>',
    name: 'فاطمة أحمد حسن',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    accessCode: '9876543',
    enrolledCourses: ['41c61139-b980-4f5d-8036-c006b1ed6bc2'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-01-20')
  },
  {
    id: 'd6f8a0c2-e4f6-6b8d-d0f2-f4f6a8b0c2e4',
    email: '<EMAIL>',
    name: 'محمد علي سالم',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    accessCode: '5555555',
    enrolledCourses: ['138ec959-163c-4ce9-9ad3-2b0597bdcfea'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-02-01')
  },
  {
    id: 'e7f9a1c3-f5a7-7c9e-e1f3-f5f7a9c1e3f5',
    email: '<EMAIL>',
    name: 'سارة خالد محمود',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    accessCode: '1111111',
    enrolledCourses: ['138ec959-163c-4ce9-9ad3-2b0597bdcfea', '41c61139-b980-4f5d-8036-c006b1ed6bc2'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-02-05')
  },
  {
    id: 'f8a0b2d4-a6b8-8d0f-f2a4-a6a8b0d2f4a6',
    email: '<EMAIL>',
    name: 'عبدالله يوسف أحمد',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    accessCode: '7777777',
    enrolledCourses: ['3de6741f-0444-439e-a0f2-5f169c4d1970'],
    completedCourses: [],
    certificates: [],
    createdAt: new Date('2024-02-10')
  }
];

// بيانات تسجيل دخول الطلاب للاختبار
export const studentCredentials = [
  { email: '<EMAIL>', password: 'Student@123', accessCode: '1234567' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '9876543' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '5555555' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '1111111' },
  { email: '<EMAIL>', password: 'Student@123', accessCode: '7777777' }
];
