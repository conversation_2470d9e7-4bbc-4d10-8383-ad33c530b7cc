{"ast": null, "code": "import{signOut,onAuthStateChanged}from'firebase/auth';import{auth}from'../config/firebase';import{supabaseService}from'./supabaseService';import{defaultAdmin,adminCredentials}from'../data/defaultAdmin';class AuthService{// Admin login\nasync loginAdmin(email,password){try{// Try Supabase first\nlet admin=null;try{admin=await supabaseService.getAdminByEmail(email);}catch(supabaseError){console.warn('Supabase admin login failed, trying fallback:',supabaseError);}if(admin){// Verify password (simplified for frontend)\nconst isPasswordValid=password==='Admin@123456';if(!isPasswordValid){throw new Error('كلمة المرور غير صحيحة');}return{id:admin.id,email:admin.email,role:'admin',name:admin.name,avatar:admin.avatar_url,permissions:admin.permissions||[],createdAt:new Date(admin.created_at)};}// Fallback to default admin\nif(adminCredentials.email===email&&adminCredentials.password===password){return{id:'admin-001',email:adminCredentials.email,role:'admin',name:defaultAdmin.name,avatar:defaultAdmin.avatar_url,permissions:defaultAdmin.permissions,createdAt:new Date()};}throw new Error('بيانات تسجيل الدخول غير صحيحة');}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Student login with access code\nasync loginStudent(accessCode){try{// Try Supabase first\nlet studentData=null;try{studentData=await supabaseService.getStudentByAccessCode(accessCode);}catch(supabaseError){console.warn('Supabase student login failed, trying fallback:',supabaseError);}if(studentData){var _studentData$student_,_studentData$student_2,_studentData$certific;if(!studentData.is_active){throw new Error('الحساب غير مفعل');}// Transform Supabase data to match our Student type\nconst enrolledCourses=((_studentData$student_=studentData.student_enrollments)===null||_studentData$student_===void 0?void 0:_studentData$student_.map(enrollment=>enrollment.course_id))||[];const completedCourses=((_studentData$student_2=studentData.student_enrollments)===null||_studentData$student_2===void 0?void 0:_studentData$student_2.filter(enrollment=>enrollment.completed_at).map(enrollment=>enrollment.course_id))||[];const certificates=((_studentData$certific=studentData.certificates)===null||_studentData$certific===void 0?void 0:_studentData$certific.map(cert=>cert.id))||[];return{id:studentData.id,email:studentData.email||'',role:'student',name:studentData.name||'',avatar:studentData.avatar_url||'',accessCode:studentData.access_code,enrolledCourses,completedCourses,certificates,createdAt:new Date(studentData.created_at)};}throw new Error('كود الدخول غير صحيح');}catch(error){throw new Error(error.message||'حدث خطأ في تسجيل الدخول');}}// Generate access code for student\ngenerateAccessCode(){return Math.floor(1000000+Math.random()*9000000).toString();}// Create student account\nasync createStudent(studentData){try{const accessCode=this.generateAccessCode();// Create student in Supabase\nconst newStudent=await supabaseService.createStudent({accessCode:accessCode,name:studentData.name,email:studentData.email});// Enroll student in courses if provided\nif(studentData.enrolledCourses&&studentData.enrolledCourses.length>0){for(const courseId of studentData.enrolledCourses){await supabaseService.enrollStudent(newStudent.id,courseId);}}return accessCode;}catch(error){// If access code already exists, try again\nif(error.message.includes('duplicate key')){return this.createStudent(studentData);}throw new Error('فشل في إنشاء حساب الطالب');}}// Logout\nasync logout(){try{await signOut(auth);}catch(error){throw new Error('فشل في تسجيل الخروج');}}// Get current user\ngetCurrentUser(){return new Promise(resolve=>{const unsubscribe=onAuthStateChanged(auth,user=>{unsubscribe();resolve(user);});});}// Auth state listener\nonAuthStateChange(callback){return onAuthStateChanged(auth,callback);}getErrorMessage(errorCode){switch(errorCode){case'auth/user-not-found':return'المستخدم غير موجود';case'auth/wrong-password':return'كلمة المرور غير صحيحة';case'auth/invalid-email':return'البريد الإلكتروني غير صحيح';case'auth/user-disabled':return'الحساب معطل';case'auth/too-many-requests':return'محاولات كثيرة، حاول مرة أخرى لاحقاً';default:return'حدث خطأ في تسجيل الدخول';}}}export const authService=new AuthService();", "map": {"version": 3, "names": ["signOut", "onAuthStateChanged", "auth", "supabaseService", "defaultAdmin", "adminCredentials", "AuthService", "loginAdmin", "email", "password", "admin", "getAdminByEmail", "supabaseError", "console", "warn", "isPasswordValid", "Error", "id", "role", "name", "avatar", "avatar_url", "permissions", "createdAt", "Date", "created_at", "error", "message", "loginStudent", "accessCode", "studentData", "getStudentByAccessCode", "_studentData$student_", "_studentData$student_2", "_studentData$certific", "is_active", "enrolledCourses", "student_enrollments", "map", "enrollment", "course_id", "completedCourses", "filter", "completed_at", "certificates", "cert", "access_code", "generateAccessCode", "Math", "floor", "random", "toString", "createStudent", "newStudent", "length", "courseId", "enrollStudent", "includes", "logout", "getCurrentUser", "Promise", "resolve", "unsubscribe", "user", "onAuthStateChange", "callback", "getErrorMessage", "errorCode", "authService"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/services/authService.ts"], "sourcesContent": ["import {\n  signInWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  User as FirebaseUser\n} from 'firebase/auth';\nimport { auth } from '../config/firebase';\nimport { User, Student, Admin } from '../types';\nimport { supabaseService } from './supabaseService';\nimport { defaultAdmin, adminCredentials } from '../data/defaultAdmin';\n\nclass AuthService {\n  // Admin login\n  async loginAdmin(email: string, password: string): Promise<Admin> {\n    try {\n      // Try Supabase first\n      let admin = null;\n      try {\n        admin = await supabaseService.getAdminByEmail(email);\n      } catch (supabaseError) {\n        console.warn('Supabase admin login failed, trying fallback:', supabaseError);\n      }\n\n      if (admin) {\n        // Verify password (simplified for frontend)\n        const isPasswordValid = password === 'Admin@123456';\n        if (!isPasswordValid) {\n          throw new Error('كلمة المرور غير صحيحة');\n        }\n\n        return {\n          id: admin.id,\n          email: admin.email,\n          role: 'admin',\n          name: admin.name,\n          avatar: admin.avatar_url,\n          permissions: admin.permissions || [],\n          createdAt: new Date(admin.created_at)\n        };\n      }\n\n      // Fallback to default admin\n      if (adminCredentials.email === email && adminCredentials.password === password) {\n        return {\n          id: 'admin-001',\n          email: adminCredentials.email,\n          role: 'admin',\n          name: defaultAdmin.name,\n          avatar: defaultAdmin.avatar_url,\n          permissions: defaultAdmin.permissions,\n          createdAt: new Date()\n        };\n      }\n\n      throw new Error('بيانات تسجيل الدخول غير صحيحة');\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Student login with access code\n  async loginStudent(accessCode: string): Promise<Student> {\n    try {\n      // Try Supabase first\n      let studentData = null;\n      try {\n        studentData = await supabaseService.getStudentByAccessCode(accessCode);\n      } catch (supabaseError) {\n        console.warn('Supabase student login failed, trying fallback:', supabaseError);\n      }\n\n      if (studentData) {\n        if (!studentData.is_active) {\n          throw new Error('الحساب غير مفعل');\n        }\n\n        // Transform Supabase data to match our Student type\n        const enrolledCourses = studentData.student_enrollments?.map((enrollment: any) => enrollment.course_id) || [];\n        const completedCourses = studentData.student_enrollments?.filter((enrollment: any) => enrollment.completed_at).map((enrollment: any) => enrollment.course_id) || [];\n        const certificates = studentData.certificates?.map((cert: any) => cert.id) || [];\n\n        return {\n          id: studentData.id,\n          email: studentData.email || '',\n          role: 'student',\n          name: studentData.name || '',\n          avatar: studentData.avatar_url || '',\n          accessCode: studentData.access_code,\n          enrolledCourses,\n          completedCourses,\n          certificates,\n          createdAt: new Date(studentData.created_at)\n        };\n      }\n\n      throw new Error('كود الدخول غير صحيح');\n    } catch (error: any) {\n      throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');\n    }\n  }\n\n  // Generate access code for student\n  generateAccessCode(): string {\n    return Math.floor(1000000 + Math.random() * 9000000).toString();\n  }\n\n  // Create student account\n  async createStudent(studentData: {\n    name: string;\n    email?: string;\n    enrolledCourses?: string[];\n  }): Promise<string> {\n    try {\n      const accessCode = this.generateAccessCode();\n\n      // Create student in Supabase\n      const newStudent = await supabaseService.createStudent({\n        accessCode: accessCode,\n        name: studentData.name,\n        email: studentData.email\n      });\n\n      // Enroll student in courses if provided\n      if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {\n        for (const courseId of studentData.enrolledCourses) {\n          await supabaseService.enrollStudent(newStudent.id, courseId);\n        }\n      }\n\n      return accessCode;\n    } catch (error: any) {\n      // If access code already exists, try again\n      if (error.message.includes('duplicate key')) {\n        return this.createStudent(studentData);\n      }\n      throw new Error('فشل في إنشاء حساب الطالب');\n    }\n  }\n\n  // Logout\n  async logout(): Promise<void> {\n    try {\n      await signOut(auth);\n    } catch (error: any) {\n      throw new Error('فشل في تسجيل الخروج');\n    }\n  }\n\n  // Get current user\n  getCurrentUser(): Promise<FirebaseUser | null> {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (user) => {\n        unsubscribe();\n        resolve(user);\n      });\n    });\n  }\n\n  // Auth state listener\n  onAuthStateChange(callback: (user: FirebaseUser | null) => void) {\n    return onAuthStateChanged(auth, callback);\n  }\n\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/user-not-found':\n        return 'المستخدم غير موجود';\n      case 'auth/wrong-password':\n        return 'كلمة المرور غير صحيحة';\n      case 'auth/invalid-email':\n        return 'البريد الإلكتروني غير صحيح';\n      case 'auth/user-disabled':\n        return 'الحساب معطل';\n      case 'auth/too-many-requests':\n        return 'محاولات كثيرة، حاول مرة أخرى لاحقاً';\n      default:\n        return 'حدث خطأ في تسجيل الدخول';\n    }\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA,OAEEA,OAAO,CACPC,kBAAkB,KAEb,eAAe,CACtB,OAASC,IAAI,KAAQ,oBAAoB,CAEzC,OAASC,eAAe,KAAQ,mBAAmB,CACnD,OAASC,YAAY,CAAEC,gBAAgB,KAAQ,sBAAsB,CAErE,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAa,CAAEC,QAAgB,CAAkB,CAChE,GAAI,CACF;AACA,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChB,GAAI,CACFA,KAAK,CAAG,KAAM,CAAAP,eAAe,CAACQ,eAAe,CAACH,KAAK,CAAC,CACtD,CAAE,MAAOI,aAAa,CAAE,CACtBC,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAEF,aAAa,CAAC,CAC9E,CAEA,GAAIF,KAAK,CAAE,CACT;AACA,KAAM,CAAAK,eAAe,CAAGN,QAAQ,GAAK,cAAc,CACnD,GAAI,CAACM,eAAe,CAAE,CACpB,KAAM,IAAI,CAAAC,KAAK,CAAC,uBAAuB,CAAC,CAC1C,CAEA,MAAO,CACLC,EAAE,CAAEP,KAAK,CAACO,EAAE,CACZT,KAAK,CAAEE,KAAK,CAACF,KAAK,CAClBU,IAAI,CAAE,OAAO,CACbC,IAAI,CAAET,KAAK,CAACS,IAAI,CAChBC,MAAM,CAAEV,KAAK,CAACW,UAAU,CACxBC,WAAW,CAAEZ,KAAK,CAACY,WAAW,EAAI,EAAE,CACpCC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACd,KAAK,CAACe,UAAU,CACtC,CAAC,CACH,CAEA;AACA,GAAIpB,gBAAgB,CAACG,KAAK,GAAKA,KAAK,EAAIH,gBAAgB,CAACI,QAAQ,GAAKA,QAAQ,CAAE,CAC9E,MAAO,CACLQ,EAAE,CAAE,WAAW,CACfT,KAAK,CAAEH,gBAAgB,CAACG,KAAK,CAC7BU,IAAI,CAAE,OAAO,CACbC,IAAI,CAAEf,YAAY,CAACe,IAAI,CACvBC,MAAM,CAAEhB,YAAY,CAACiB,UAAU,CAC/BC,WAAW,CAAElB,YAAY,CAACkB,WAAW,CACrCC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACH,CAEA,KAAM,IAAI,CAAAR,KAAK,CAAC,+BAA+B,CAAC,CAClD,CAAE,MAAOU,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAV,KAAK,CAACU,KAAK,CAACC,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACA,KAAM,CAAAC,YAAYA,CAACC,UAAkB,CAAoB,CACvD,GAAI,CACF;AACA,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,GAAI,CACFA,WAAW,CAAG,KAAM,CAAA3B,eAAe,CAAC4B,sBAAsB,CAACF,UAAU,CAAC,CACxE,CAAE,MAAOjB,aAAa,CAAE,CACtBC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAEF,aAAa,CAAC,CAChF,CAEA,GAAIkB,WAAW,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CACf,GAAI,CAACJ,WAAW,CAACK,SAAS,CAAE,CAC1B,KAAM,IAAI,CAAAnB,KAAK,CAAC,iBAAiB,CAAC,CACpC,CAEA;AACA,KAAM,CAAAoB,eAAe,CAAG,EAAAJ,qBAAA,CAAAF,WAAW,CAACO,mBAAmB,UAAAL,qBAAA,iBAA/BA,qBAAA,CAAiCM,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CAC7G,KAAM,CAAAC,gBAAgB,CAAG,EAAAR,sBAAA,CAAAH,WAAW,CAACO,mBAAmB,UAAAJ,sBAAA,iBAA/BA,sBAAA,CAAiCS,MAAM,CAAEH,UAAe,EAAKA,UAAU,CAACI,YAAY,CAAC,CAACL,GAAG,CAAEC,UAAe,EAAKA,UAAU,CAACC,SAAS,CAAC,GAAI,EAAE,CACnK,KAAM,CAAAI,YAAY,CAAG,EAAAV,qBAAA,CAAAJ,WAAW,CAACc,YAAY,UAAAV,qBAAA,iBAAxBA,qBAAA,CAA0BI,GAAG,CAAEO,IAAS,EAAKA,IAAI,CAAC5B,EAAE,CAAC,GAAI,EAAE,CAEhF,MAAO,CACLA,EAAE,CAAEa,WAAW,CAACb,EAAE,CAClBT,KAAK,CAAEsB,WAAW,CAACtB,KAAK,EAAI,EAAE,CAC9BU,IAAI,CAAE,SAAS,CACfC,IAAI,CAAEW,WAAW,CAACX,IAAI,EAAI,EAAE,CAC5BC,MAAM,CAAEU,WAAW,CAACT,UAAU,EAAI,EAAE,CACpCQ,UAAU,CAAEC,WAAW,CAACgB,WAAW,CACnCV,eAAe,CACfK,gBAAgB,CAChBG,YAAY,CACZrB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACM,WAAW,CAACL,UAAU,CAC5C,CAAC,CACH,CAEA,KAAM,IAAI,CAAAT,KAAK,CAAC,qBAAqB,CAAC,CACxC,CAAE,MAAOU,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAV,KAAK,CAACU,KAAK,CAACC,OAAO,EAAI,yBAAyB,CAAC,CAC7D,CACF,CAEA;AACAoB,kBAAkBA,CAAA,CAAW,CAC3B,MAAO,CAAAC,IAAI,CAACC,KAAK,CAAC,OAAO,CAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CACjE,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACtB,WAInB,CAAmB,CAClB,GAAI,CACF,KAAM,CAAAD,UAAU,CAAG,IAAI,CAACkB,kBAAkB,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAM,UAAU,CAAG,KAAM,CAAAlD,eAAe,CAACiD,aAAa,CAAC,CACrDvB,UAAU,CAAEA,UAAU,CACtBV,IAAI,CAAEW,WAAW,CAACX,IAAI,CACtBX,KAAK,CAAEsB,WAAW,CAACtB,KACrB,CAAC,CAAC,CAEF;AACA,GAAIsB,WAAW,CAACM,eAAe,EAAIN,WAAW,CAACM,eAAe,CAACkB,MAAM,CAAG,CAAC,CAAE,CACzE,IAAK,KAAM,CAAAC,QAAQ,GAAI,CAAAzB,WAAW,CAACM,eAAe,CAAE,CAClD,KAAM,CAAAjC,eAAe,CAACqD,aAAa,CAACH,UAAU,CAACpC,EAAE,CAAEsC,QAAQ,CAAC,CAC9D,CACF,CAEA,MAAO,CAAA1B,UAAU,CACnB,CAAE,MAAOH,KAAU,CAAE,CACnB;AACA,GAAIA,KAAK,CAACC,OAAO,CAAC8B,QAAQ,CAAC,eAAe,CAAC,CAAE,CAC3C,MAAO,KAAI,CAACL,aAAa,CAACtB,WAAW,CAAC,CACxC,CACA,KAAM,IAAI,CAAAd,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAEA;AACA,KAAM,CAAA0C,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAA1D,OAAO,CAACE,IAAI,CAAC,CACrB,CAAE,MAAOwB,KAAU,CAAE,CACnB,KAAM,IAAI,CAAAV,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAEA;AACA2C,cAAcA,CAAA,CAAiC,CAC7C,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,WAAW,CAAG7D,kBAAkB,CAACC,IAAI,CAAG6D,IAAI,EAAK,CACrDD,WAAW,CAAC,CAAC,CACbD,OAAO,CAACE,IAAI,CAAC,CACf,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA;AACAC,iBAAiBA,CAACC,QAA6C,CAAE,CAC/D,MAAO,CAAAhE,kBAAkB,CAACC,IAAI,CAAE+D,QAAQ,CAAC,CAC3C,CAEQC,eAAeA,CAACC,SAAiB,CAAU,CACjD,OAAQA,SAAS,EACf,IAAK,qBAAqB,CACxB,MAAO,oBAAoB,CAC7B,IAAK,qBAAqB,CACxB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,4BAA4B,CACrC,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,wBAAwB,CAC3B,MAAO,qCAAqC,CAC9C,QACE,MAAO,yBAAyB,CACpC,CACF,CACF,CAEA,MAAO,MAAM,CAAAC,WAAW,CAAG,GAAI,CAAA9D,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}