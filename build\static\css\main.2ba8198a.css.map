{"version": 3, "file": "static/css/main.2ba8198a.css", "mappings": "AAIA,gDAGE,WACE,cAAe,CACf,cACF,CAGA,eAGE,YAAa,CACb,eAAgB,CAFhB,cAAe,CADf,WAAY,CAIZ,UACF,CAEA,yBACE,UACF,CAEA,oBACE,iBAAkB,CAClB,+BACF,CAEA,sCACE,gBACF,CAGA,iBAGE,YAAa,CACb,eAAgB,CAFhB,cAAe,CADf,WAAY,CAIZ,UACF,CAEA,sBACE,iBAAkB,CAClB,YACF,CAGA,UAEE,eAAiB,CADjB,mBAEF,CAEA,UAEE,cAAe,CADf,aAEF,CAGA,MACE,oBAAsB,CACtB,gCAA0C,CAC1C,oBACF,CAEA,aAEE,+BACF,CAEA,wBAJE,eAMF,CAGA,aACE,6CACF,CAUA,mDACE,6CACF,CAGA,iBAGE,wBAAyB,CADzB,mBAAqB,CADrB,eAGF,CAEA,OAEE,iBAAmB,CADnB,cAEF,CAEA,oBAEE,cAAgB,CAChB,kBACF,CAGA,YACE,qBACF,CAEA,YAIE,aAAc,CAHd,iBAAmB,CACnB,eAAgB,CAChB,mBAEF,CAEA,YAGE,wBAAyB,CACzB,mBAAqB,CACrB,iBAAmB,CAHnB,cAAgB,CAIhB,oEAAwE,CALxE,UAMF,CAEA,kBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,KAQE,kBAAmB,CAJnB,mBAAqB,CAErB,cAAe,CACf,mBAAoB,CALpB,iBAAmB,CACnB,eAAgB,CAOhB,SAAW,CADX,sBAAuB,CARvB,qBAAuB,CAIvB,+BAMF,CAEA,aACE,wBAAyB,CAEzB,wBAAyB,CADzB,UAEF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,eACE,wBAAyB,CAEzB,wBAAyB,CADzB,UAEF,CAEA,qBACE,wBAAyB,CACzB,oBACF,CAGA,iBAKE,oBAAsB,CAFtB,QAAS,CAGT,eAAgB,CAFhB,qBAAsB,CAHtB,iBAAkB,CAClB,UAKF,CAEA,cAKE,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAEF,CAGA,aAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,cACF,CAEA,aACE,eAAiB,CACjB,oBAAsB,CACtB,gCAA0C,CAC1C,eAAgB,CAChB,iDACF,CAEA,mBAEE,mCAA6C,CAD7C,0BAEF,CAGA,OAKE,kBAAmB,CADnB,YAAa,CAFb,OAAQ,CAIR,sBAAuB,CACvB,YAAa,CANb,cAAe,CAEf,UAKF,CAEA,gBAGE,0BAAoC,CADpC,OAAQ,CADR,iBAGF,CAEA,eAEE,eAAiB,CACjB,oBAAsB,CACtB,qCAA+C,CAG/C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAPhB,iBAAkB,CAIlB,UAIF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WACE,eAAiB,CAIjB,wBAAyB,CAFzB,oBAAsB,CACtB,gCAA0C,CAF1C,cAIF,CAEA,WAKE,kBAAmB,CAFnB,mBAAqB,CACrB,YAAa,CAFb,aAAc,CAId,sBAAuB,CACvB,kBAAmB,CANnB,YAOF,CAEA,YACE,kBAAmB,CACnB,eAAgB,CAChB,aAAc,CACd,oBACF,CAEA,YAEE,aAAc,CADd,iBAEF,CAGA,cAGE,wBAAyB,CACzB,oBAAsB,CAFtB,YAAc,CAGd,eAAgB,CAJhB,UAKF,CAEA,eAEE,wBAAyB,CADzB,WAAY,CAEZ,yBACF,CAGA,cACE,eAAgB,CAChB,cACF,CAGA,UACE,kBACF,CAGA,kBAEE,oBAAqB,CADrB,iBAEF,CAEA,cAGE,wBAAyB,CACzB,mBAAqB,CACrB,iBAAmB,CAHnB,iCAAoC,CADpC,UAKF,CAEA,aAOE,aAAc,CADd,WAAY,CAJZ,WAAa,CADb,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAC3B,UAGF,CAGA,iBACE,iBAAmB,CACnB,eACF,CAEA,oBACE,gBAAiB,CAEjB,eAAgB,CADhB,eAEF,CAGA,qCAEE,UAAW,CADX,SAEF,CAEA,2CACE,kBAAmB,CACnB,iBACF,CAEA,2CACE,kBAAmB,CACnB,iBACF,CAEA,iDACE,kBACF,CAGA,SACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,UACE,8BACF,CAEA,mBACE,GACE,2BACF,CACA,GACE,uBACF,CACF,CACF,CAGA,2EACE,gCAEE,2BAA4B,CAC5B,6BACF,CAEA,0CAEE,uBACF,CAEA,0CAEE,aAAc,CACd,YACF,CAEA,oBAME,eAAiB,CACjB,wBAAyB,CACzB,mBAAqB,CAErB,gCAA0C,CAT1C,aAAc,CAGd,SAAU,CAKV,aAAe,CAPf,cAAe,CACf,QAAS,CAET,UAMF,CAEA,aACE,wDACF,CAEA,YACE,mCACF,CACF,CAGA,4EACE,YACE,mCACF,CAEA,aACE,wDACF,CAEA,iBACE,qBACF,CACF,CC5bA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,qCAAmB,CAAnB,6CAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,qCAAmB,CAAnB,+BAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,iCAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,+BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,mCAAmB,CAAnB,2BAAmB,CAAnB,8DAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,mCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,qCAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,4CAAmB,CAAnB,4BAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,yCAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,yCAAmB,CAAnB,2CAAmB,CAAnB,wCAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,0BAAmB,CAAnB,sCAAmB,CAAnB,gCAAmB,CAAnB,kDAAmB,CAAnB,yOAAmB,CAAnB,iDAAmB,CAAnB,sCAAmB,CAAnB,6NAAmB,CAAnB,mGAAmB,CAAnB,mEAAmB,EAAnB,sDAAmB,CAAnB,+BAAmB,EAAnB,4EAAmB,CAAnB,0CAAmB,EAAnB,yDAAmB,CAAnB,gDAAmB,CAAnB,wCAAmB,CAAnB,uDAAmB,CAAnB,iDAAmB,CAAnB,+CAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,yCAAmB,CAAnB,mCAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,0CAAmB,CAAnB,mDAAmB,CAAnB,+CAAmB,CAAnB,gDAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,yEAAmB,CAAnB,gIAAmB,CAAnB,yEAAmB,CAAnB,8HAAmB,CAAnB,yEAAmB,CAAnB,gIAAmB,CAAnB,yEAAmB,CAAnB,4HAAmB,CAAnB,yEAAmB,CAAnB,gIAAmB,CAAnB,yEAAmB,CAAnB,8HAAmB,CAAnB,yEAAmB,CAAnB,gIAAmB,CAAnB,yEAAmB,CAAnB,4HAAmB,CAAnB,yEAAmB,CAAnB,gIAAmB,CAAnB,+EAAmB,CAAnB,yEAAmB,CAAnB,wIAAmB,CAAnB,8EAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,yDAAmB,CAAnB,uCAAmB,CAAnB,4CAAmB,CAAnB,yCAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,0CAAmB,CAAnB,0DAAmB,CAAnB,2DAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,2CAAmB,CAAnB,wCAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,iEAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,gEAAmB,CAAnB,mDAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,+DAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,+DAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,+DAAmB,CAAnB,iDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,mDAAmB,CAAnB,8BAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,6CAAmB,CAAnB,2BAAmB,CAAnB,kEAAmB,CAAnB,8CAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,kEAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,sDAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,gDAAmB,CAAnB,iEAAmB,CAAnB,mDAAmB,CAAnB,yCAAmB,CAAnB,4DAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,qDAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,8CAAmB,CAAnB,kEAAmB,CAAnB,oDAAmB,CAAnB,+CAAmB,CAAnB,iEAAmB,CAAnB,+CAAmB,CAAnB,gEAAmB,CAAnB,qDAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,oDAAmB,CAAnB,8CAAmB,CAAnB,kEAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,qDAAmB,CAAnB,+CAAmB,CAAnB,+DAAmB,CAAnB,+CAAmB,CAAnB,+DAAmB,CAAnB,sDAAmB,CAAnB,gDAAmB,CAAnB,kEAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,gDAAmB,CAAnB,iEAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,wCAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,iDAAmB,CAAnB,iEAAmB,CAAnB,sDAAmB,CAAnB,8CAAmB,CAAnB,kEAAmB,CAAnB,6CAAmB,CAAnB,kEAAmB,CAAnB,kDAAmB,CAAnB,8CAAmB,CAAnB,gEAAmB,CAAnB,uCAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,yCAAmB,CAAnB,kEAAmB,CAAnB,oDAAmB,CAAnB,iDAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,iEAAmB,CAAnB,yCAAmB,CAAnB,kCAAmB,CAAnB,kEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,gEAAmB,CAAnB,4CAAmB,CAAnB,6CAAmB,CAAnB,uGAAmB,CAAnB,8FAAmB,CAAnB,+FAAmB,CAAnB,oFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,oFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,oFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,qFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,uFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,uFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,uFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,mFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,8EAAmB,CAAnB,+DAAmB,CAAnB,2EAAmB,CAAnB,uFAAmB,CAAnB,+DAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,qFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,sFAAmB,CAAnB,mEAAmB,CAAnB,2EAAmB,CAAnB,kFAAmB,CAAnB,mHAAmB,CAAnB,mFAAmB,CAAnB,mHAAmB,CAAnB,+EAAmB,CAAnB,8EAAmB,CAAnB,+EAAmB,CAAnB,+EAAmB,CAAnB,6EAAmB,CAAnB,8EAAmB,CAAnB,6EAAmB,CAAnB,8EAAmB,CAAnB,+EAAmB,CAAnB,gFAAmB,CAAnB,gFAAmB,CAAnB,gFAAmB,CAAnB,iFAAmB,CAAnB,iFAAmB,CAAnB,gFAAmB,CAAnB,gFAAmB,CAAnB,6EAAmB,CAAnB,iFAAmB,CAAnB,gFAAmB,CAAnB,wCAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,iCAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,mCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,6BAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,8BAAmB,CAAnB,iCAAmB,CAAnB,4BAAmB,CAAnB,mCAAmB,CAAnB,8BAAmB,CAAnB,iCAAmB,CAAnB,4BAAmB,CAAnB,uEAAmB,CAAnB,kEAAmB,CAAnB,uEAAmB,CAAnB,+DAAmB,CAAnB,gEAAmB,CAAnB,uEAAmB,CAAnB,kEAAmB,CAAnB,8DAAmB,CAAnB,8DAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,mCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,wCAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,6CAAmB,CAAnB,mDAAmB,CAAnB,oDAAmB,CAAnB,wHAAmB,CAAnB,oCAAmB,CAAnB,0BAAmB,CAAnB,sCAAmB,CAAnB,6BAAmB,CAAnB,qCAAmB,CAAnB,4BAAmB,CAAnB,kCAAmB,CAAnB,uBAAmB,CAAnB,mCAAmB,CAAnB,4BAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,oCAAmB,CAAnB,6BAAmB,CAAnB,oCAAmB,CAAnB,6BAAmB,CAAnB,mCAAmB,CAAnB,0BAAmB,CAAnB,oCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,sCAAmB,CAAnB,wCAAmB,CAAnB,6CAAmB,CAAnB,uCAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,wCAAmB,CAAnB,uDAAmB,CAAnB,0DAAmB,CAAnB,gCAAmB,CAAnB,mDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,4CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,6CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,6CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,6CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,6CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,6CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,+CAAmB,CAAnB,uBAAmB,CAAnB,yDAAmB,CAAnB,+CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,uDAAmB,CAAnB,yDAAmB,CAAnB,qCAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,uDAAmB,CAAnB,yDAAmB,CAAnB,qCAAmB,CAAnB,uDAAmB,CAAnB,iDAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,iDAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,yDAAmB,CAAnB,0DAAmB,CAAnB,wCAAmB,CAAnB,yDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,sDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,uBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,4EAAmB,CAAnB,sGAAmB,CAAnB,6EAAmB,CAAnB,4GAAmB,CAAnB,6DAAmB,CAAnB,sEAAmB,CAAnB,oFAAmB,CAAnB,2GAAmB,CAAnB,+EAAmB,CAAnB,4GAAmB,CAAnB,kFAAmB,CAAnB,yGAAmB,CAAnB,sDAAmB,CAAnB,gEAAmB,CAAnB,+EAAmB,CAAnB,4GAAmB,CAAnB,qFAAmB,CAAnB,4GAAmB,CAAnB,sCAAmB,CAAnB,4HAAmB,CAAnB,kHAAmB,CAAnB,iFAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,gEAAmB,CAAnB,gDAAmB,CAAnB,6GAAmB,CAAnB,kNAAmB,CAAnB,wDAAmB,CAAnB,wRAAmB,CAAnB,gRAAmB,CAAnB,8MAAmB,CAAnB,uJAAmB,CAAnB,+KAAmB,CAAnB,4DAAmB,CAAnB,oFAAmB,CAAnB,4DAAmB,CAAnB,yJAAmB,CAAnB,4DAAmB,CAAnB,4FAAmB,CAAnB,4DAAmB,CAAnB,8FAAmB,CAAnB,4DAAmB,CAAnB,+CAAmB,CAAnB,+CAAmB,CAAnB,+CAAmB,CAUnB,OAHE,aAOF,CAJA,KACE,oCAA2C,CAE3C,gBACF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,SACE,gCACF,CAEA,UACE,8BACF,CAEA,eACE,kCACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,wBACE,MACE,uBACF,CACA,IACE,0BACF,CACF,CAIE,8BAAqH,CAArH,mBAAqH,CAArH,wBAAqH,CAArH,sDAAqH,CAArH,mBAAqH,CAArH,UAAqH,CAArH,+CAAqH,CAArH,eAAqH,CAArH,kBAAqH,CAArH,iHAAqH,CAArH,kDAAqH,CAArH,oCAAqH,CAArH,wBAAqH,CAArH,sDAAqH,CAIrH,gCAAiI,CAAjI,mBAAiI,CAAjI,wBAAiI,CAAjI,wDAAiI,CAAjI,mBAAiI,CAAjI,aAAiI,CAAjI,4CAAiI,CAAjI,eAAiI,CAAjI,kBAAiI,CAAjI,iHAAiI,CAAjI,kDAAiI,CAAjI,sCAAiI,CAAjI,wBAAiI,CAAjI,wDAAiI,CAIjI,kCAAsJ,CAAtJ,mBAAsJ,CAAtJ,oBAAsJ,CAAtJ,sDAAsJ,CAAtJ,mBAAsJ,CAAtJ,gBAAsJ,CAAtJ,aAAsJ,CAAtJ,6CAAsJ,CAAtJ,eAAsJ,CAAtJ,kBAAsJ,CAAtJ,+CAAsJ,CAAtJ,kDAAsJ,CAAtJ,oCAAsJ,CAAtJ,mBAAsJ,CAAtJ,wBAAsJ,CAAtJ,sDAAsJ,CAAtJ,UAAsJ,CAAtJ,+CAAsJ,CAStJ,kBAJA,qBAA+D,CAA/D,iBAA+D,CAA/D,iCAA+D,CAA/D,sDAA+D,CAA/D,qBAA+D,CAA/D,wDAA+D,CAA/D,oBAA+D,CAA/D,wDAA+D,CAA/D,oBAA+D,CAA/D,gBAA+D,CAA/D,+CAA+D,CAA/D,iHAI0D,CAA1D,mCAA0D,CAA1D,8BAA0D,CAA1D,kDAA0D,CAA1D,+EAA0D,CAA1D,iBAA0D,CAA1D,wBAA0D,CAA1D,0EAA0D,CAA1D,qDAA0D,EAA1D,+EAA0D,CAA1D,+FAA0D,CAA1D,+CAA0D,CAA1D,kGAA0D,CAK1D,iCAAqJ,CAArJ,oBAAqJ,CAArJ,wDAAqJ,CAArJ,mBAAqJ,CAArJ,gBAAqJ,CAArJ,mBAAqJ,CAArJ,iHAAqJ,CAArJ,kDAAqJ,CAArJ,UAAqJ,CAArJ,uCAAqJ,CAArJ,0GAAqJ,CAArJ,wGAAqJ,CAArJ,mBAAqJ,CAArJ,6EAAqJ,CAArJ,uDAAqJ,CAArJ,uEAAqJ,CAArJ,wFAAqJ,CAIrJ,+BAAmD,CAAnD,aAAmD,CAAnD,0DAAmD,CAAnD,iBAAmD,CAAnD,eAAmD,CAAnD,mBAAmD,CAAnD,mBAAmD,CAIrD,cACE,oBACF,CAEA,oBAEE,qCAA2C,CAD3C,UAEF,CAEA,gBACE,OACE,UACF,CACA,IACE,WACF,CACA,IACE,YACF,CACA,OACE,aACF,CACF,CAGA,kBACE,kDACF,CAEA,oBACE,kDACF,CAGA,iBAGE,QAAS,CACT,qBAAsB,CAHtB,iBAAkB,CAClB,UAGF,CAEA,+CAOE,kBAAmB,CADnB,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAGF,CAIE,kCAAkH,CAAlH,oBAAkH,CAAlH,wDAAkH,CAAlH,mBAAkH,CAAlH,gBAAkH,CAAlH,cAAkH,CAAlH,YAAkH,CAAlH,+CAAkH,CAAlH,kDAAkH,CAAlH,wCAAkH,CAAlH,oBAAkH,CAAlH,wDAAkH,CAIlH,2CAAuC,CAAvC,iBAAuC,CAAvC,wBAAuC,CAAvC,6EAAuC,CAAvC,uDAAuC,CAIvC,0CAAmC,CAAnC,iBAAmC,CAAnC,wBAAmC,CAAnC,6EAAmC,CAAnC,sDAAmC,CAInC,4CAA+B,CAA/B,iBAA+B,CAA/B,wBAA+B,CAA/B,6EAA+B,CAA/B,sDAA+B,CAK/B,4CAAgF,CAAhF,iBAAgF,CAAhF,+DAAgF,CAAhF,iGAAgF,CAAhF,qBAAgF,CAAhF,wDAAgF,CAChF,kI,CADA,oBAAgF,CAAhF,sDAAgF,CAAhF,mBAAgF,CAAhF,gBAAgF,CAAhF,+CAAgF,CAAhF,kGAAgF,CAAhF,YAAgF,CAAhF,iBAAgF,CAOlF,yBAEI,8BAAkC,CAAlC,qBAAkC,CAAlC,gEAAkC,CAAlC,cAAkC,CAAlC,UAAkC,CAIlC,8DAAgD,CAAhD,kDAAgD,CAAhD,OAAgD,CAAhD,cAAgD,CAAhD,UAAgD,CAKhD,kCAAgB,CAIhB,8BAAc,CAAd,mBAAc,CAId,6BAA0B,CAA1B,sCAA0B,CAI1B,8BAAe,CAAf,kBAAe,CAAf,YAAe,CAIf,mEAAwB,CAIxB,2BAAa,CAIb,uBAAa,CAEjB,CAGA,gDAEI,qEAAwB,CAIxB,mCAAgB,CAIhB,2BAAgB,CAAhB,kBAAgB,CAIhB,2BAAU,CAKV,6BAA0D,CAA1D,wCAA0D,CAK1D,oCALA,eAA0D,CAA1D,yBAKmD,CAAnD,mCAAmD,CAKnD,oEAA0B,CAA1B,sBAA0B,CAA1B,sKAA0B,CAK1B,2BAAW,CAKX,8BAAqB,CAArB,iBAAqB,CAArB,eAAqB,CAEzB,CAGA,0BAEI,oEAAwB,CAIxB,oCAAgB,CAIhB,gCAAc,CAAd,mBAAc,CAId,0BAAU,CAEd,CAIE,sCAA0B,CAA1B,iBAA0B,CAC1B,gBAAiB,CADjB,iBAA0B,CAA1B,kBAA0B,CAA1B,UAA0B,CAI5B,yBAEI,yCAAW,CAAX,oBAAW,CAEf,CAEA,0BAEI,uCAAW,CAAX,kBAAW,CAEf,CAIE,oCAAsC,CAAtC,gBAAsC,CAAtC,8DAAsC,CAAtC,mBAAsC,EAAtC,2DAAsC,CAAtC,kBAAsC,EAItC,qCAAsC,CAAtC,mBAAsC,CAAtC,2DAAsC,CAAtC,kBAAsC,EAAtC,+DAAsC,CAAtC,mBAAsC,EAItC,oCAAsC,CAAtC,kBAAsC,CAAtC,iEAAsC,CAAtC,mBAAsC,EAAtC,gEAAsC,CAAtC,mBAAsC,EAItC,sCAAqC,CAArC,mBAAqC,CAArC,8DAAqC,CAArC,mBAAqC,EAArC,6DAAqC,CAArC,gBAAqC,EAIrC,qCAAsC,CAAtC,mBAAsC,CAAtC,6DAAsC,CAAtC,gBAAsC,EAAtC,+DAAsC,CAAtC,mBAAsC,EAItC,qCAAuC,CAAvC,gBAAuC,CAAvC,gEAAuC,CAAvC,mBAAuC,EAAvC,+DAAuC,CAAvC,kBAAuC,EAKvC,2EAA0C,CAA1C,0GAA0C,CAA1C,oGAA0C,CAA1C,4GAA0C,EAA1C,oGAA0C,CAA1C,wGAA0C,EAI1C,2EAA0C,CAA1C,wGAA0C,CAA1C,oGAA0C,CAA1C,4GAA0C,EAA1C,oGAA0C,CAA1C,wGAA0C,EAI1C,2EAA2C,CAA3C,4GAA2C,CAA3C,oGAA2C,CAA3C,wGAA2C,EAA3C,oGAA2C,CAA3C,wGAA2C,EAK3C,8BAA4F,CAA5F,YAA4F,CAA5F,sDAA4F,CAA5F,kGAA4F,EAA5F,iGAA4F,EAA5F,wFAA4F,EAI5F,gCAAqD,CAArD,YAAqD,CAArD,sDAAqD,CAArD,oGAAqD,EAIrD,gCAAoE,CAApE,YAAoE,CAApE,sDAAoE,CAApE,oGAAoE,EAApE,0FAAoE,EAKpE,mCAA4D,CAA5D,YAA4D,CAA5D,qBAA4D,CAA5D,QAA4D,CAA5D,4DAA4D,CAA5D,UAA4D,EAI5D,2CAAoE,CAApE,YAAoE,CAApE,6BAAoE,CAApE,QAAoE,CAApE,oEAAoE,CAApE,UAAoE,EAKpE,gBACA,0CAA+C,CAD/C,yBAAyB,CAKzB,cACA,+CAAoD,CADpD,eAAmD,CAAnD,cAAmD,CAAnD,yBAAmD,CAKnD,YACA,2CAAgD,CADhD,yBAAyB,CAK3B,eACE,gCAAiC,CACjC,sBACF,CAIE,uIAA4C,CAA5C,wGAA4C,CAA5C,mBAA4C,CAA5C,wDAA4C,CAA5C,kGAA4C,CAA5C,wFAA4C,CAI9C,WACE,wBAAyB,CAGzB,gBACF,CAGA,mCAEI,4BAA6B,CAA7B,mBAA6B,CAA7B,wBAA6B,CAA7B,qDAA6B,CAA7B,UAA6B,CAA7B,+CAA6B,CAI7B,qCAAkC,CAAlC,qDAAkC,CAIlC,wCAJA,qBAAkC,CAAlC,iBAAkC,CAAlC,wBAAkC,CAAlC,qDAI6C,CAA7C,0CAA6C,CAA7C,oBAA6C,CAA7C,qDAA6C,CAA7C,UAA6C,CAA7C,+CAA6C,CAEjD,CA/ZA,0DAgaA,CAhaA,2BAgaA,CAhaA,8DAgaA,CAhaA,kBAgaA,CAhaA,6DAgaA,CAhaA,iBAgaA,CAhaA,qDAgaA,CAhaA,wBAgaA,CAhaA,qDAgaA,CAhaA,uBAgaA,CAhaA,6FAgaA,CAhaA,mFAgaA,CAhaA,6DAgaA,CAhaA,8BAgaA,CAhaA,sGAgaA,CAhaA,kDAgaA,CAhaA,+BAgaA,CAhaA,sGAgaA,CAhaA,gEAgaA,CAhaA,oEAgaA,CAhaA,4DAgaA,CAhaA,wDAgaA,CAhaA,mCAgaA,CAhaA,gEAgaA,CAhaA,4PAgaA,CAhaA,mDAgaA,CAhaA,2BAgaA,CAhaA,6DAgaA,CAhaA,8BAgaA,CAhaA,kEAgaA,CAhaA,+DAgaA,CAhaA,8BAgaA,CAhaA,iEAgaA,CAhaA,qDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,qDAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,qDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,qDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,oDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,qDAgaA,CAhaA,kCAgaA,CAhaA,+DAgaA,CAhaA,sDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,sDAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,uDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,wDAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,wDAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,uDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,uDAgaA,CAhaA,kCAgaA,CAhaA,iEAgaA,CAhaA,mDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,oDAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,0DAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,kDAgaA,CAhaA,+BAgaA,CAhaA,kEAgaA,CAhaA,uDAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,uDAgaA,CAhaA,kCAgaA,CAhaA,+DAgaA,CAhaA,yDAgaA,CAhaA,4GAgaA,CAhaA,iGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,gGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,kGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,mGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,mGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,mGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,gGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,mGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,mGAgaA,CAhaA,mEAgaA,CAhaA,2EAgaA,CAhaA,4FAgaA,CAhaA,4FAgaA,CAhaA,2FAgaA,CAhaA,4FAgaA,CAhaA,6FAgaA,CAhaA,6FAgaA,CAhaA,6FAgaA,CAhaA,0FAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,sDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,sDAgaA,CAhaA,yDAgaA,CAhaA,uBAgaA,CAhaA,sDAgaA,CAhaA,0DAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,0DAgaA,CAhaA,uBAgaA,CAhaA,sDAgaA,CAhaA,4DAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,4DAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,2DAgaA,CAhaA,uBAgaA,CAhaA,wDAgaA,CAhaA,wDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,wDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,wDAgaA,CAhaA,uBAgaA,CAhaA,uDAgaA,CAhaA,sDAgaA,CAhaA,oBAgaA,CAhaA,yDAgaA,CAhaA,iGAgaA,CAhaA,2GAgaA,CAhaA,yGAgaA,CAhaA,4GAgaA,CAhaA,+FAgaA,CAhaA,yGAgaA,CAhaA,mEAgaA,CAhaA,gEAgaA,CAhaA,yGAgaA,CAhaA,4GAgaA,CAhaA,kGAgaA,CAhaA,4GAgaA,CAhaA,yEAgaA,CAhaA,8CAgaA,CAhaA,gEAgaA,CAhaA,8BAgaA,CAhaA,iEAgaA,CAhaA,6DAgaA,CAhaA,4DAgaA,CAhaA,4BAgaA,CAhaA,yIAgaA,CAhaA,kHAgaA,CAhaA,iFAgaA,CAhaA,kGAgaA,CAhaA,yDAgaA,CAhaA,kEAgaA,CAhaA,0DAgaA,CAhaA,iEAgaA,CAhaA,4DAgaA,CAhaA,kEAgaA,CAhaA,wDAgaA,CAhaA,iEAgaA,CAhaA,8DAgaA,CAhaA,mEAgaA,CAhaA,2DAgaA,CAhaA,iEAgaA,CAhaA,gEAgaA,CAhaA,mEAgaA,CAhaA,0DAgaA,CAhaA,kCAgaA,CAhaA,kEAgaA,CAhaA,mDAgaA,CAhaA,mEAgaA,CAhaA,wBAgaA,CAhaA,uMAgaA,CAhaA,sEAgaA,CAhaA,uBAgaA,CAhaA,yDAgaA,CAhaA,0DAgaA,CAhaA,gHAgaA,CAhaA,kCAgaA,CAhaA,gEAgaA,CAhaA,0FAgaA,CAhaA,mCAgaA,CAhaA,uMAgaA,CAhaA,sFAgaA,CAhaA,2BAgaA,CAhaA,sGAgaA,CAhaA,uEAgaA,CAhaA,4BAgaA,CAhaA,oJAgaA,CAhaA,kHAgaA,CAhaA,iFAgaA,CAhaA,kGAgaA,CAhaA,8GAgaA,CAhaA,mEAgaA,CAhaA,6DAgaA,CAhaA,kCAgaA,EAhaA,8DAgaA,CAhaA,iCAgaA,CAhaA,kCAgaA,CAhaA,iCAgaA,CAhaA,wBAgaA,CAhaA,gEAgaA,CAhaA,sCAgaA,CAhaA,wCAgaA,CAhaA,sCAgaA,CAhaA,qCAgaA,CAhaA,sCAgaA,CAhaA,uCAgaA,CAhaA,qCAgaA,CAhaA,gCAgaA,CAhaA,sCAgaA,CAhaA,kCAgaA,CAhaA,oCAgaA,CAhaA,gCAgaA,CAhaA,kCAgaA,CAhaA,iCAgaA,CAhaA,+BAgaA,CAhaA,+BAgaA,CAhaA,8BAgaA,CAhaA,iCAgaA,CAhaA,gCAgaA,CAhaA,8BAgaA,CAhaA,gCAgaA,CAhaA,8BAgaA,CAhaA,8BAgaA,CAhaA,6BAgaA,CAhaA,gCAgaA,CAhaA,+BAgaA,CAhaA,6BAgaA,CAhaA,+BAgaA,CAhaA,gCAgaA,CAhaA,gCAgaA,CAhaA,uCAgaA,CAhaA,kCAgaA,CAhaA,wEAgaA,CAhaA,0CAgaA,CAhaA,0DAgaA,CAhaA,iDAgaA,CAhaA,8CAgaA,CAhaA,4DAgaA,CAhaA,8BAgaA,CAhaA,+BAgaA,CAhaA,6BAgaA,CAhaA,+BAgaA,CAhaA,6BAgaA,CAhaA,6EAgaA,CAhaA,0HAgaA,CAhaA,6EAgaA,CAhaA,8HAgaA,CAhaA,6EAgaA,CAhaA,4HAgaA,CAhaA,4BAgaA,CAhaA,kCAgaA,CAhaA,gCAgaA,CAhaA,iCAgaA,CAhaA,+BAgaA,CAhaA,iCAgaA,CAhaA,+BAgaA,CAhaA,wCAgaA,CAhaA,8BAgaA,CAhaA,uCAgaA,CAhaA,8BAgaA,CAhaA,qCAgaA,CAhaA,4BAgaA,CAhaA,uCAgaA,CAhaA,8BAgaA,CAhaA,qCAgaA,CAhaA,4BAgaA,CAhaA,2EAgaA,CAhaA,oEAgaA,CAhaA,sEAgaA,CAhaA,kEAgaA,CAhaA,wEAgaA,CAhaA,uCAgaA,CAhaA,uCAgaA,CAhaA,0CAgaA,CAhaA,iDAgaA,CAhaA,wCAgaA,CAhaA,0BAgaA,CAhaA,0CAgaA,CAhaA,6BAgaA,CAhaA,yCAgaA,CAhaA,4BAgaA,CAhaA,sCAgaA,CAhaA,uBAgaA,CAhaA,yCAgaA,CAhaA,uBAgaA,CAhaA,uCAgaA,CAhaA,4BAgaA,CAhaA,yCAgaA,CAhaA,6BAgaA,CAhaA,wCAgaA,CAhaA,6BAgaA,CAhaA,wCAgaA,CAhaA,6BAgaA,EAhaA,2DAgaA,CAhaA,gCAgaA,CAhaA,kCAgaA,CAhaA,+BAgaA,CAhaA,0CAgaA,CAhaA,wEAgaA,CAhaA,wEAgaA,CAhaA,wEAgaA,CAhaA,8BAgaA,CAhaA,8BAgaA,CAhaA,6BAgaA,CAhaA,6BAgaA,CAhaA,gCAgaA,CAhaA,gCAgaA,CAhaA,+BAgaA,CAhaA,+BAgaA,CAhaA,sCAgaA,CAhaA,4BAgaA,CAhaA,qCAgaA,CAhaA,4BAgaA,CAhaA,wCAgaA,CAhaA,+BAgaA,CAhaA,wCAgaA,CAhaA,+BAgaA,CAhaA,wCAgaA,CAhaA,+BAgaA,CAhaA,oEAgaA,CAhaA,2EAgaA,CAhaA,2EAgaA,CAhaA,sEAgaA,EAhaA,6EAgaA,CAhaA,mDAgaA,CAhaA,mDAgaA,CAhaA,yCAgaA,CAhaA,wCAgaA,CAhaA,sCAgaA,CAhaA,kCAgaA,CAhaA,kCAgaA,CAhaA,+BAgaA,CAhaA,+BAgaA,CAhaA,8BAgaA,CAhaA,8BAgaA,CAhaA,+BAgaA,CAhaA,uCAgaA,CAhaA,wEAgaA,CAhaA,wEAgaA,CAhaA,wEAgaA,CAhaA,+BAgaA,CAhaA,yCAgaA,CAhaA,4BAgaA,CAhaA,uCAgaA,CAhaA,4BAgaA,CAhaA,yCAgaA,CAhaA,uDAgaA,CAhaA,wCAgaA,EAhaA,yDAgaA,CAhaA,uCAgaA", "sources": ["styles/tablet-responsive.css", "App.css"], "sourcesContent": ["/* Tablet Responsive Styles for ALaa Academy Platform */\n/* Optimized for 768px - 1024px screens */\n\n/* Base tablet styles */\n@media (min-width: 768px) and (max-width: 1024px) {\n  \n  /* Layout adjustments */\n  .container {\n    max-width: 100%;\n    padding: 0 1rem;\n  }\n\n  /* Admin Dashboard Sidebar */\n  .admin-sidebar {\n    width: 240px;\n    position: fixed;\n    height: 100vh;\n    overflow-y: auto;\n    z-index: 40;\n  }\n\n  .admin-sidebar.collapsed {\n    width: 64px;\n  }\n\n  .admin-main-content {\n    margin-left: 240px;\n    transition: margin-left 0.3s ease;\n  }\n\n  .admin-main-content.sidebar-collapsed {\n    margin-left: 64px;\n  }\n\n  /* Student Dashboard */\n  .student-sidebar {\n    width: 220px;\n    position: fixed;\n    height: 100vh;\n    overflow-y: auto;\n    z-index: 40;\n  }\n\n  .student-main-content {\n    margin-left: 220px;\n    padding: 1rem;\n  }\n\n  /* Navigation improvements */\n  .nav-item {\n    padding: 0.75rem 1rem;\n    font-size: 0.9rem;\n  }\n\n  .nav-icon {\n    width: 1.25rem;\n    height: 1.25rem;\n  }\n\n  /* Cards and content */\n  .card {\n    border-radius: 0.75rem;\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n    margin-bottom: 1.5rem;\n  }\n\n  .card-header {\n    padding: 1.25rem;\n    border-bottom: 1px solid #e5e7eb;\n  }\n\n  .card-body {\n    padding: 1.25rem;\n  }\n\n  /* Grid layouts */\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  /* Tables */\n  .table-container {\n    overflow-x: auto;\n    border-radius: 0.5rem;\n    border: 1px solid #e5e7eb;\n  }\n\n  .table {\n    min-width: 100%;\n    font-size: 0.875rem;\n  }\n\n  .table th,\n  .table td {\n    padding: 0.75rem;\n    white-space: nowrap;\n  }\n\n  /* Forms */\n  .form-group {\n    margin-bottom: 1.25rem;\n  }\n\n  .form-label {\n    font-size: 0.875rem;\n    font-weight: 500;\n    margin-bottom: 0.5rem;\n    display: block;\n  }\n\n  .form-input {\n    width: 100%;\n    padding: 0.75rem;\n    border: 1px solid #d1d5db;\n    border-radius: 0.5rem;\n    font-size: 0.875rem;\n    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  }\n\n  .form-input:focus {\n    outline: none;\n    border-color: #3b82f6;\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n  }\n\n  /* Buttons */\n  .btn {\n    padding: 0.75rem 1.5rem;\n    font-size: 0.875rem;\n    font-weight: 500;\n    border-radius: 0.5rem;\n    transition: all 0.15s ease-in-out;\n    cursor: pointer;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .btn-primary {\n    background-color: #3b82f6;\n    color: white;\n    border: 1px solid #3b82f6;\n  }\n\n  .btn-primary:hover {\n    background-color: #2563eb;\n    border-color: #2563eb;\n  }\n\n  .btn-secondary {\n    background-color: #6b7280;\n    color: white;\n    border: 1px solid #6b7280;\n  }\n\n  .btn-secondary:hover {\n    background-color: #4b5563;\n    border-color: #4b5563;\n  }\n\n  /* Video Player */\n  .video-container {\n    position: relative;\n    width: 100%;\n    height: 0;\n    padding-bottom: 56.25%; /* 16:9 aspect ratio */\n    border-radius: 0.75rem;\n    overflow: hidden;\n  }\n\n  .video-player {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n\n  /* Course Grid */\n  .course-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 1.5rem;\n    padding: 1rem 0;\n  }\n\n  .course-card {\n    background: white;\n    border-radius: 0.75rem;\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n    overflow: hidden;\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n  }\n\n  .course-card:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n  }\n\n  /* Modal adjustments */\n  .modal {\n    position: fixed;\n    inset: 0;\n    z-index: 50;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 1rem;\n  }\n\n  .modal-backdrop {\n    position: absolute;\n    inset: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n  }\n\n  .modal-content {\n    position: relative;\n    background: white;\n    border-radius: 0.75rem;\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n    width: 100%;\n    max-width: 32rem;\n    max-height: 90vh;\n    overflow-y: auto;\n  }\n\n  /* Stats cards */\n  .stats-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  .stat-card {\n    background: white;\n    padding: 1.5rem;\n    border-radius: 0.75rem;\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n    border: 1px solid #e5e7eb;\n  }\n\n  .stat-icon {\n    width: 2.5rem;\n    height: 2.5rem;\n    border-radius: 0.5rem;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 1rem;\n  }\n\n  .stat-value {\n    font-size: 1.875rem;\n    font-weight: 700;\n    line-height: 1;\n    margin-bottom: 0.25rem;\n  }\n\n  .stat-label {\n    font-size: 0.875rem;\n    color: #6b7280;\n  }\n\n  /* Progress bars */\n  .progress-bar {\n    width: 100%;\n    height: 0.5rem;\n    background-color: #e5e7eb;\n    border-radius: 0.25rem;\n    overflow: hidden;\n  }\n\n  .progress-fill {\n    height: 100%;\n    background-color: #3b82f6;\n    transition: width 0.5s ease;\n  }\n\n  /* Touch-friendly elements */\n  .touch-target {\n    min-height: 44px;\n    min-width: 44px;\n  }\n\n  /* Improved spacing for touch */\n  .btn + .btn {\n    margin-left: 0.75rem;\n  }\n\n  /* Search and filters */\n  .search-container {\n    position: relative;\n    margin-bottom: 1.5rem;\n  }\n\n  .search-input {\n    width: 100%;\n    padding: 0.75rem 1rem 0.75rem 2.5rem;\n    border: 1px solid #d1d5db;\n    border-radius: 0.5rem;\n    font-size: 0.875rem;\n  }\n\n  .search-icon {\n    position: absolute;\n    left: 0.75rem;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 1rem;\n    height: 1rem;\n    color: #9ca3af;\n  }\n\n  /* Responsive text */\n  .text-responsive {\n    font-size: 0.875rem;\n    line-height: 1.5;\n  }\n\n  .heading-responsive {\n    font-size: 1.5rem;\n    line-height: 1.3;\n    font-weight: 600;\n  }\n\n  /* Improved scrollbars */\n  .custom-scrollbar::-webkit-scrollbar {\n    width: 6px;\n    height: 6px;\n  }\n\n  .custom-scrollbar::-webkit-scrollbar-track {\n    background: #f1f5f9;\n    border-radius: 3px;\n  }\n\n  .custom-scrollbar::-webkit-scrollbar-thumb {\n    background: #cbd5e1;\n    border-radius: 3px;\n  }\n\n  .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n    background: #94a3b8;\n  }\n\n  /* Animation improvements */\n  .fade-in {\n    animation: fadeIn 0.3s ease-in-out;\n  }\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(10px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n\n  .slide-in {\n    animation: slideIn 0.3s ease-out;\n  }\n\n  @keyframes slideIn {\n    from {\n      transform: translateX(-100%);\n    }\n    to {\n      transform: translateX(0);\n    }\n  }\n}\n\n/* Portrait tablet specific styles */\n@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {\n  .admin-sidebar,\n  .student-sidebar {\n    transform: translateX(-100%);\n    transition: transform 0.3s ease;\n  }\n\n  .admin-sidebar.open,\n  .student-sidebar.open {\n    transform: translateX(0);\n  }\n\n  .admin-main-content,\n  .student-main-content {\n    margin-left: 0;\n    padding: 1rem;\n  }\n\n  .mobile-menu-button {\n    display: block;\n    position: fixed;\n    top: 1rem;\n    left: 1rem;\n    z-index: 50;\n    background: white;\n    border: 1px solid #e5e7eb;\n    border-radius: 0.5rem;\n    padding: 0.5rem;\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\n  }\n\n  .course-grid {\n    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  }\n\n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n/* Landscape tablet specific styles */\n@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {\n  .stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n\n  .course-grid {\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  }\n\n  .video-container {\n    padding-bottom: 42.25%; /* Adjust for landscape */\n  }\n}\n", "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Import tablet responsive styles */\n@import './styles/tablet-responsive.css';\n\n/* RTL Support */\n* {\n  direction: rtl;\n}\n\nbody {\n  font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;\n  direction: rtl;\n  text-align: right;\n}\n\n/* Custom Scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Animation Classes */\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n.slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n\n.bounce-gentle {\n  animation: bounceGentle 2s infinite;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(10px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n@keyframes bounceGentle {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-5px);\n  }\n}\n\n/* Custom Button Styles */\n.btn-primary {\n  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;\n}\n\n.btn-secondary {\n  @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;\n}\n\n.btn-outline {\n  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;\n}\n\n/* Card Styles */\n.card {\n  @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;\n}\n\n.card-hover {\n  @apply card hover:shadow-md transition-shadow duration-200;\n}\n\n/* Form Styles */\n.form-input {\n  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;\n}\n\n.form-label {\n  @apply block text-sm font-medium text-gray-700 mb-2;\n}\n\n/* Loading Animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.loading-dots::after {\n  content: '';\n  animation: dots 1.5s steps(4, end) infinite;\n}\n\n@keyframes dots {\n  0%, 20% {\n    content: '';\n  }\n  40% {\n    content: '.';\n  }\n  60% {\n    content: '..';\n  }\n  80%, 100% {\n    content: '...';\n  }\n}\n\n/* Gradient Backgrounds */\n.gradient-primary {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n}\n\n.gradient-secondary {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n/* Video Player Styles */\n.video-container {\n  position: relative;\n  width: 100%;\n  height: 0;\n  padding-bottom: 56.25%; /* 16:9 aspect ratio */\n}\n\n.video-container iframe,\n.video-container video {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border-radius: 12px;\n}\n\n/* Quiz Styles */\n.quiz-option {\n  @apply p-4 border-2 border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-300;\n}\n\n.quiz-option.selected {\n  @apply border-primary-500 bg-primary-50;\n}\n\n.quiz-option.correct {\n  @apply border-green-500 bg-green-50;\n}\n\n.quiz-option.incorrect {\n  @apply border-red-500 bg-red-50;\n}\n\n/* Certificate Styles */\n.certificate-container {\n  @apply bg-white border-4 border-primary-600 rounded-lg p-8 text-center shadow-lg;\n  background-image: \n    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);\n}\n\n/* Mobile Responsive */\n@media (max-width: 768px) {\n  .mobile-menu {\n    @apply fixed inset-0 z-50 bg-white;\n  }\n\n  .mobile-menu-overlay {\n    @apply fixed inset-0 bg-black bg-opacity-50 z-40;\n  }\n\n  /* Mobile-specific adjustments */\n  .mobile-padding {\n    @apply px-4 py-2;\n  }\n\n  .mobile-text {\n    @apply text-sm;\n  }\n\n  .mobile-button {\n    @apply py-3 px-4 text-base;\n  }\n\n  .mobile-card {\n    @apply p-4 mx-2;\n  }\n\n  .mobile-grid {\n    @apply grid-cols-1 gap-4;\n  }\n\n  .mobile-hidden {\n    @apply hidden;\n  }\n\n  .mobile-full {\n    @apply w-full;\n  }\n}\n\n/* Tablet Responsive */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .tablet-grid {\n    @apply grid-cols-2 gap-6;\n  }\n\n  .tablet-padding {\n    @apply px-6 py-4;\n  }\n\n  .tablet-text {\n    @apply text-base;\n  }\n\n  .tablet-card {\n    @apply p-6;\n  }\n\n  /* Touch-friendly buttons for tablets */\n  .tablet-button {\n    @apply py-3 px-6 text-base min-h-[44px] touch-manipulation;\n  }\n\n  /* Improved touch targets */\n  .tablet-touch-target {\n    @apply min-h-[44px] min-w-[44px] touch-manipulation;\n  }\n\n  /* Better spacing for tablet layouts */\n  .tablet-spacing {\n    @apply space-y-4 space-x-4;\n  }\n\n  /* Tablet-specific sidebar width */\n  .tablet-sidebar {\n    @apply w-72;\n  }\n\n  /* Tablet modal adjustments */\n  .tablet-modal {\n    @apply max-w-2xl mx-4;\n  }\n}\n\n/* Desktop Responsive */\n@media (min-width: 1025px) {\n  .desktop-grid {\n    @apply grid-cols-3 gap-8;\n  }\n\n  .desktop-padding {\n    @apply px-8 py-6;\n  }\n\n  .desktop-text {\n    @apply text-lg;\n  }\n\n  .desktop-card {\n    @apply p-8;\n  }\n}\n\n/* Container Responsive Classes */\n.container-responsive {\n  @apply w-full mx-auto px-4;\n  max-width: 1200px;\n}\n\n@media (min-width: 640px) {\n  .container-responsive {\n    @apply px-6;\n  }\n}\n\n@media (min-width: 1024px) {\n  .container-responsive {\n    @apply px-8;\n  }\n}\n\n/* Responsive Typography */\n.text-responsive-xs {\n  @apply text-xs sm:text-sm md:text-base;\n}\n\n.text-responsive-sm {\n  @apply text-sm sm:text-base md:text-lg;\n}\n\n.text-responsive-base {\n  @apply text-base sm:text-lg md:text-xl;\n}\n\n.text-responsive-lg {\n  @apply text-lg sm:text-xl md:text-2xl;\n}\n\n.text-responsive-xl {\n  @apply text-xl sm:text-2xl md:text-3xl;\n}\n\n.text-responsive-2xl {\n  @apply text-2xl sm:text-3xl md:text-4xl;\n}\n\n/* Responsive Spacing */\n.spacing-responsive-sm {\n  @apply space-y-2 sm:space-y-3 md:space-y-4;\n}\n\n.spacing-responsive-md {\n  @apply space-y-4 sm:space-y-6 md:space-y-8;\n}\n\n.spacing-responsive-lg {\n  @apply space-y-6 sm:space-y-8 md:space-y-12;\n}\n\n/* Responsive Grid */\n.grid-responsive {\n  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8;\n}\n\n.grid-responsive-2 {\n  @apply grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6;\n}\n\n.grid-responsive-3 {\n  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;\n}\n\n/* Responsive Flex */\n.flex-responsive {\n  @apply flex flex-col sm:flex-row items-center gap-4 sm:gap-6;\n}\n\n.flex-responsive-reverse {\n  @apply flex flex-col-reverse sm:flex-row items-center gap-4 sm:gap-6;\n}\n\n/* Touch Optimizations */\n.touch-friendly {\n  @apply touch-manipulation;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);\n}\n\n.touch-button {\n  @apply min-h-[44px] min-w-[44px] touch-manipulation;\n  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.2);\n}\n\n.touch-card {\n  @apply touch-manipulation;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.05);\n}\n\n/* Improved scrolling for touch devices */\n.smooth-scroll {\n  -webkit-overflow-scrolling: touch;\n  scroll-behavior: smooth;\n}\n\n/* Better focus states for touch devices */\n.focus-visible:focus-visible {\n  @apply ring-2 ring-primary-500 ring-offset-2;\n}\n\n/* Prevent text selection on interactive elements */\n.no-select {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Dark Mode Support (Optional) */\n@media (prefers-color-scheme: dark) {\n  .dark-mode {\n    @apply bg-gray-900 text-white;\n  }\n\n  .dark-mode .card {\n    @apply bg-gray-800 border-gray-700;\n  }\n\n  .dark-mode .form-input {\n    @apply bg-gray-800 border-gray-600 text-white;\n  }\n}\n"], "names": [], "sourceRoot": ""}