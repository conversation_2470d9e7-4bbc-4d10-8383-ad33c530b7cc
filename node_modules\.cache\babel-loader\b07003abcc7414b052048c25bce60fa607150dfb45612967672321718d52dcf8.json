{"ast": null, "code": "import React from'react';import{NavLink,useLocation}from'react-router-dom';import{motion}from'framer-motion';import{HomeIcon,AcademicCapIcon,ClipboardDocumentListIcon,DocumentTextIcon,UserIcon,XMarkIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const StudentSidebar=_ref=>{let{isOpen,onClose}=_ref;const location=useLocation();const menuItems=[{name:'الرئيسية',href:'/student',icon:HomeIcon,exact:true},{name:'كورساتي',href:'/student/courses',icon:AcademicCapIcon},{name:'الاختبارات',href:'/student/quizzes',icon:ClipboardDocumentListIcon},{name:'شهاداتي',href:'/student/certificates',icon:DocumentTextIcon},{name:'الملف الشخصي',href:'/student/profile',icon:UserIcon}];const isActive=(href,exact)=>{if(exact){return location.pathname===href;}return location.pathname.startsWith(href);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex md:flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col w-64 xl:w-72\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col flex-grow bg-white border-l border-gray-200 pt-4 sm:pt-5 pb-4 overflow-y-auto shadow-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center flex-shrink-0 px-3 sm:px-4 mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary-600 rounded-lg p-2 shadow-md\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 sm:w-8 sm:h-8 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-2 sm:mr-3\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-base sm:text-lg font-bold text-gray-900 font-display\",children:\"ALaa Abd Hamied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs sm:text-sm text-gray-500 font-body\",children:\"\\u0645\\u0646\\u0635\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645\"})]})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-3 sm:mt-5 flex-1 px-2 space-y-1\",children:menuItems.map(item=>{const Icon=item.icon;const active=isActive(item.href,item.exact);return/*#__PURE__*/_jsxs(NavLink,{to:item.href,className:`\n                      group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105\n                      ${active?'bg-gradient-to-r from-primary-100 to-primary-50 text-primary-900 border-l-4 border-primary-600 shadow-sm':'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-25 hover:text-gray-900 hover:shadow-sm'}\n                    `,children:[/*#__PURE__*/_jsx(Icon,{className:`\n                        ml-3 flex-shrink-0 h-5 w-5 sm:h-6 sm:w-6 transition-all duration-200\n                        ${active?'text-primary-600':'text-gray-400 group-hover:text-gray-500'}\n                      `}),/*#__PURE__*/_jsx(\"span\",{className:\"font-body\",children:item.name})]},item.name);})}),/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-4 border-t border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u0637\\u0627\\u0644\\u0628 \\u0646\\u0634\\u0637\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645!\"})]})]})})})]})})}),/*#__PURE__*/_jsx(motion.div,{initial:{x:-300},animate:{x:isOpen?0:-300},transition:{type:'tween',duration:0.3},className:\"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl md:hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-primary-600 rounded-lg p-2\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-bold text-gray-900\",children:\"ALaa Abd Hamied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u0645\\u0646\\u0635\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645\"})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-6 h-6\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",children:menuItems.map(item=>{const Icon=item.icon;const active=isActive(item.href,item.exact);return/*#__PURE__*/_jsxs(NavLink,{to:item.href,onClick:onClose,className:`\n                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                    ${active?'bg-primary-100 text-primary-900 border-l-4 border-primary-600':'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}\n                  `,children:[/*#__PURE__*/_jsx(Icon,{className:`\n                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                      ${active?'text-primary-600':'text-gray-400 group-hover:text-gray-500'}\n                    `}),item.name]},item.name);})}),/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-4 border-t border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:\"\\u0637\\u0627\\u0644\\u0628 \\u0646\\u0634\\u0637\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645!\"})]})]})})})]})})]});};export default StudentSidebar;", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "motion", "HomeIcon", "AcademicCapIcon", "ClipboardDocumentListIcon", "DocumentTextIcon", "UserIcon", "XMarkIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "StudentSidebar", "_ref", "isOpen", "onClose", "location", "menuItems", "name", "href", "icon", "exact", "isActive", "pathname", "startsWith", "children", "className", "map", "item", "Icon", "active", "to", "div", "initial", "x", "animate", "transition", "type", "duration", "onClick"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  HomeIcon,\n  AcademicCapIcon,\n  ClipboardDocumentListIcon,\n  DocumentTextIcon,\n  UserIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\ninterface StudentSidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst StudentSidebar: React.FC<StudentSidebarProps> = ({ isOpen, onClose }) => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      name: 'الرئيسية',\n      href: '/student',\n      icon: HomeIcon,\n      exact: true\n    },\n    {\n      name: 'كورساتي',\n      href: '/student/courses',\n      icon: AcademicCapIcon\n    },\n    {\n      name: 'الاختبارات',\n      href: '/student/quizzes',\n      icon: ClipboardDocumentListIcon\n    },\n    {\n      name: 'شهاداتي',\n      href: '/student/certificates',\n      icon: DocumentTextIcon\n    },\n    {\n      name: 'الملف الشخصي',\n      href: '/student/profile',\n      icon: UserIcon\n    }\n  ];\n\n  const isActive = (href: string, exact?: boolean) => {\n    if (exact) {\n      return location.pathname === href;\n    }\n    return location.pathname.startsWith(href);\n  };\n\n  return (\n    <>\n      {/* Desktop & Tablet Sidebar */}\n      <div className=\"hidden md:flex md:flex-shrink-0\">\n        <div className=\"flex flex-col w-64 xl:w-72\">\n          <div className=\"flex flex-col flex-grow bg-white border-l border-gray-200 pt-4 sm:pt-5 pb-4 overflow-y-auto shadow-sm\">\n            {/* Logo */}\n            <div className=\"flex items-center flex-shrink-0 px-3 sm:px-4 mb-6 sm:mb-8\">\n              <div className=\"bg-primary-600 rounded-lg p-2 shadow-md\">\n                <AcademicCapIcon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\n              </div>\n              <div className=\"mr-2 sm:mr-3\">\n                <h2 className=\"text-base sm:text-lg font-bold text-gray-900 font-display\">ALaa Abd Hamied</h2>\n                <p className=\"text-xs sm:text-sm text-gray-500 font-body\">منصة التعلم</p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"mt-3 sm:mt-5 flex-1 px-2 space-y-1\">\n              {menuItems.map((item) => {\n                const Icon = item.icon;\n                const active = isActive(item.href, item.exact);\n\n                return (\n                  <NavLink\n                    key={item.name}\n                    to={item.href}\n                    className={`\n                      group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105\n                      ${active\n                        ? 'bg-gradient-to-r from-primary-100 to-primary-50 text-primary-900 border-l-4 border-primary-600 shadow-sm'\n                        : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-25 hover:text-gray-900 hover:shadow-sm'\n                      }\n                    `}\n                  >\n                    <Icon\n                      className={`\n                        ml-3 flex-shrink-0 h-5 w-5 sm:h-6 sm:w-6 transition-all duration-200\n                        ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}\n                      `}\n                    />\n                    <span className=\"font-body\">{item.name}</span>\n                  </NavLink>\n                );\n              })}\n            </nav>\n\n            {/* Student Info Card */}\n            <div className=\"px-4 py-4 border-t border-gray-200\">\n              <div className=\"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <UserIcon className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div className=\"mr-3\">\n                    <p className=\"text-sm font-medium text-gray-900\">طالب نشط</p>\n                    <p className=\"text-xs text-gray-500\">استمر في التعلم!</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Sidebar */}\n      <motion.div\n        initial={{ x: -300 }}\n        animate={{ x: isOpen ? 0 : -300 }}\n        transition={{ type: 'tween', duration: 0.3 }}\n        className=\"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl md:hidden\"\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"bg-primary-600 rounded-lg p-2\">\n                <AcademicCapIcon className=\"w-6 h-6 text-white\" />\n              </div>\n              <div className=\"mr-3\">\n                <h2 className=\"text-lg font-bold text-gray-900\">ALaa Abd Hamied</h2>\n                <p className=\"text-sm text-gray-500\">منصة التعلم</p>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\">\n            {menuItems.map((item) => {\n              const Icon = item.icon;\n              const active = isActive(item.href, item.exact);\n              \n              return (\n                <NavLink\n                  key={item.name}\n                  to={item.href}\n                  onClick={onClose}\n                  className={`\n                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                    ${active\n                      ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <Icon\n                    className={`\n                      ml-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                      ${active ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}\n                    `}\n                  />\n                  {item.name}\n                </NavLink>\n              );\n            })}\n          </nav>\n\n          {/* Student Info Card */}\n          <div className=\"px-4 py-4 border-t border-gray-200\">\n            <div className=\"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                  <UserIcon className=\"w-5 h-5 text-white\" />\n                </div>\n                <div className=\"mr-3\">\n                  <p className=\"text-sm font-medium text-gray-900\">طالب نشط</p>\n                  <p className=\"text-xs text-gray-500\">استمر في التعلم!</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </>\n  );\n};\n\nexport default StudentSidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,CAAEC,WAAW,KAAQ,kBAAkB,CACvD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,eAAe,CACfC,yBAAyB,CACzBC,gBAAgB,CAChBC,QAAQ,CACRC,SAAS,KACJ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAOrC,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,MAAM,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CACxE,KAAM,CAAAG,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAmB,SAAS,CAAG,CAChB,CACEC,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAEpB,QAAQ,CACdqB,KAAK,CAAE,IACT,CAAC,CACD,CACEH,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAEnB,eACR,CAAC,CACD,CACEiB,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAElB,yBACR,CAAC,CACD,CACEgB,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,uBAAuB,CAC7BC,IAAI,CAAEjB,gBACR,CAAC,CACD,CACEe,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAEhB,QACR,CAAC,CACF,CAED,KAAM,CAAAkB,QAAQ,CAAGA,CAACH,IAAY,CAAEE,KAAe,GAAK,CAClD,GAAIA,KAAK,CAAE,CACT,MAAO,CAAAL,QAAQ,CAACO,QAAQ,GAAKJ,IAAI,CACnC,CACA,MAAO,CAAAH,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC,CAC3C,CAAC,CAED,mBACEV,KAAA,CAAAE,SAAA,EAAAc,QAAA,eAEElB,IAAA,QAAKmB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9ClB,IAAA,QAAKmB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzChB,KAAA,QAAKiB,SAAS,CAAC,uGAAuG,CAAAD,QAAA,eAEpHhB,KAAA,QAAKiB,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxElB,IAAA,QAAKmB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cACtDlB,IAAA,CAACN,eAAe,EAACyB,SAAS,CAAC,kCAAkC,CAAE,CAAC,CAC7D,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BlB,IAAA,OAAImB,SAAS,CAAC,2DAA2D,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC9FlB,IAAA,MAAGmB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CAAC,+DAAW,CAAG,CAAC,EACtE,CAAC,EACH,CAAC,cAGNlB,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDR,SAAS,CAACU,GAAG,CAAEC,IAAI,EAAK,CACvB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,KAAM,CAAAU,MAAM,CAAGR,QAAQ,CAACM,IAAI,CAACT,IAAI,CAAES,IAAI,CAACP,KAAK,CAAC,CAE9C,mBACEZ,KAAA,CAACZ,OAAO,EAENkC,EAAE,CAAEH,IAAI,CAACT,IAAK,CACdO,SAAS,CAAE;AAC/B;AACA,wBAAwBI,MAAM,CACJ,0GAA0G,CAC1G,8GAA8G;AACxI,qBACsB,CAAAL,QAAA,eAEFlB,IAAA,CAACsB,IAAI,EACHH,SAAS,CAAE;AACjC;AACA,0BAA0BI,MAAM,CAAG,kBAAkB,CAAG,yCAAyC;AACjG,uBAAwB,CACH,CAAC,cACFvB,IAAA,SAAMmB,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAEG,IAAI,CAACV,IAAI,CAAO,CAAC,GAhBzCU,IAAI,CAACV,IAiBH,CAAC,CAEd,CAAC,CAAC,CACC,CAAC,cAGNX,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDlB,IAAA,QAAKmB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzEhB,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChClB,IAAA,QAAKmB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,cACrFlB,IAAA,CAACH,QAAQ,EAACsB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBlB,IAAA,MAAGmB,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,6CAAQ,CAAG,CAAC,cAC7DlB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,mFAAgB,CAAG,CAAC,EACtD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGNlB,IAAA,CAACR,MAAM,CAACiC,GAAG,EACTC,OAAO,CAAE,CAAEC,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBC,OAAO,CAAE,CAAED,CAAC,CAAEpB,MAAM,CAAG,CAAC,CAAG,CAAC,GAAI,CAAE,CAClCsB,UAAU,CAAE,CAAEC,IAAI,CAAE,OAAO,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC7CZ,SAAS,CAAC,gEAAgE,CAAAD,QAAA,cAE1EhB,KAAA,QAAKiB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAEnChB,KAAA,QAAKiB,SAAS,CAAC,gEAAgE,CAAAD,QAAA,eAC7EhB,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChClB,IAAA,QAAKmB,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5ClB,IAAA,CAACN,eAAe,EAACyB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC/C,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBlB,IAAA,OAAImB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACpElB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,+DAAW,CAAG,CAAC,EACjD,CAAC,EACH,CAAC,cACNlB,IAAA,WACEgC,OAAO,CAAExB,OAAQ,CACjBW,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9ElB,IAAA,CAACF,SAAS,EAACqB,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cAGNnB,IAAA,QAAKmB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDR,SAAS,CAACU,GAAG,CAAEC,IAAI,EAAK,CACvB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACR,IAAI,CACtB,KAAM,CAAAU,MAAM,CAAGR,QAAQ,CAACM,IAAI,CAACT,IAAI,CAAES,IAAI,CAACP,KAAK,CAAC,CAE9C,mBACEZ,KAAA,CAACZ,OAAO,EAENkC,EAAE,CAAEH,IAAI,CAACT,IAAK,CACdoB,OAAO,CAAExB,OAAQ,CACjBW,SAAS,CAAE;AAC7B;AACA,sBAAsBI,MAAM,CACJ,+DAA+D,CAC/D,oDAAoD;AAC5E,mBACoB,CAAAL,QAAA,eAEFlB,IAAA,CAACsB,IAAI,EACHH,SAAS,CAAE;AAC/B;AACA,wBAAwBI,MAAM,CAAG,kBAAkB,CAAG,yCAAyC;AAC/F,qBAAsB,CACH,CAAC,CACDF,IAAI,CAACV,IAAI,GAjBLU,IAAI,CAACV,IAkBH,CAAC,CAEd,CAAC,CAAC,CACC,CAAC,cAGNX,IAAA,QAAKmB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjDlB,IAAA,QAAKmB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cACzEhB,KAAA,QAAKiB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChClB,IAAA,QAAKmB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,cACrFlB,IAAA,CAACH,QAAQ,EAACsB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACNjB,KAAA,QAAKiB,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBlB,IAAA,MAAGmB,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,6CAAQ,CAAG,CAAC,cAC7DlB,IAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,mFAAgB,CAAG,CAAC,EACtD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,CACI,CAAC,EACb,CAAC,CAEP,CAAC,CAED,cAAe,CAAAb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}