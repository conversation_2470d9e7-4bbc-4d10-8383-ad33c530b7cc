{"ast": null, "code": "import React,{useState}from'react';import{Routes,Route,Navigate}from'react-router-dom';import{motion}from'framer-motion';// Components\nimport StudentSidebar from'../../components/Student/StudentSidebar';import StudentHeader from'../../components/Student/StudentHeader';import MyCourses from'../../components/Student/MyCourses';import ProgressiveCourseViewer from'../../components/Student/ProgressiveCourseViewer';import QuizPage from'../../components/Student/QuizPage';import MyCertificates from'../../components/Student/MyCertificates';import EditableStudentProfile from'../../components/Student/EditableStudentProfile';import AIAssistant from'../../components/AIAssistant/AIAssistant';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentDashboard=_ref=>{let{user,onLogout}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-screen bg-gray-50\",dir:\"rtl\",children:[/*#__PURE__*/_jsx(StudentSidebar,{isOpen:sidebarOpen,onClose:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col overflow-hidden\",children:[/*#__PURE__*/_jsx(StudentHeader,{user:user,onMenuClick:()=>setSidebarOpen(true),onLogout:onLogout}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(StudentOverview,{user:user})}),/*#__PURE__*/_jsx(Route,{path:\"/courses\",element:/*#__PURE__*/_jsx(MyCourses,{user:user})}),/*#__PURE__*/_jsx(Route,{path:\"/course/:courseId\",element:/*#__PURE__*/_jsx(ProgressiveCourseViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/quiz/:quizId\",element:/*#__PURE__*/_jsx(QuizPage,{user:user})}),/*#__PURE__*/_jsx(Route,{path:\"/certificates\",element:/*#__PURE__*/_jsx(MyCertificates,{user:user})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(EditableStudentProfile,{user:user})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/student\",replace:true})})]})})})]}),sidebarOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",onClick:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsx(AIAssistant,{context:\"student\"})]});};export default StudentDashboard;", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "Navigate", "motion", "StudentSidebar", "StudentHeader", "MyCourses", "ProgressiveCourseViewer", "QuizPage", "MyCertificates", "EditableStudentProfile", "AIAssistant", "jsx", "_jsx", "jsxs", "_jsxs", "StudentDashboard", "_ref", "user", "onLogout", "sidebarOpen", "setSidebarOpen", "className", "dir", "children", "isOpen", "onClose", "onMenuClick", "div", "initial", "opacity", "y", "animate", "transition", "duration", "path", "element", "StudentOverview", "to", "replace", "onClick", "context"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/student/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport StudentSidebar from '../../components/Student/StudentSidebar';\nimport StudentHeader from '../../components/Student/StudentHeader';\nimport RealTimeStudentOverview from '../../components/Student/RealTimeStudentOverview';\nimport MyCourses from '../../components/Student/MyCourses';\nimport CourseViewer from '../../components/Student/CourseViewer';\nimport ProgressiveCourseViewer from '../../components/Student/ProgressiveCourseViewer';\nimport QuizPage from '../../components/Student/QuizPage';\nimport MyCertificates from '../../components/Student/MyCertificates';\nimport EditableStudentProfile from '../../components/Student/EditableStudentProfile';\nimport AIAssistant from '../../components/AIAssistant/AIAssistant';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentDashboardProps {\n  user: Student;\n  onLogout: () => void;\n}\n\nconst StudentDashboard: React.FC<StudentDashboardProps> = ({ user, onLogout }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Sidebar */}\n      <StudentSidebar \n        isOpen={sidebarOpen} \n        onClose={() => setSidebarOpen(false)} \n      />\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <StudentHeader \n          user={user}\n          onMenuClick={() => setSidebarOpen(true)}\n          onLogout={onLogout}\n        />\n        \n        {/* Page Content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Routes>\n              <Route path=\"/\" element={<StudentOverview user={user} />} />\n              <Route path=\"/courses\" element={<MyCourses user={user} />} />\n              <Route path=\"/course/:courseId\" element={<ProgressiveCourseViewer />} />\n              <Route path=\"/quiz/:quizId\" element={<QuizPage user={user} />} />\n              <Route path=\"/certificates\" element={<MyCertificates user={user} />} />\n              <Route path=\"/profile\" element={<EditableStudentProfile user={user} />} />\n              <Route path=\"*\" element={<Navigate to=\"/student\" replace />} />\n            </Routes>\n          </motion.div>\n        </main>\n      </div>\n      \n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* AI Assistant */}\n      <AIAssistant context=\"student\" />\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,MAAM,KAAQ,eAAe,CAEtC;AACA,MAAO,CAAAC,cAAc,KAAM,yCAAyC,CACpE,MAAO,CAAAC,aAAa,KAAM,wCAAwC,CAElE,MAAO,CAAAC,SAAS,KAAM,oCAAoC,CAE1D,MAAO,CAAAC,uBAAuB,KAAM,kDAAkD,CACtF,MAAO,CAAAC,QAAQ,KAAM,mCAAmC,CACxD,MAAO,CAAAC,cAAc,KAAM,yCAAyC,CACpE,MAAO,CAAAC,sBAAsB,KAAM,iDAAiD,CACpF,MAAO,CAAAC,WAAW,KAAM,0CAA0C,CAElE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQA,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAAAF,IAAA,CAC3E,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAErD,mBACEgB,KAAA,QAAKO,SAAS,CAAC,0BAA0B,CAACC,GAAG,CAAC,KAAK,CAAAC,QAAA,eAEjDX,IAAA,CAACT,cAAc,EACbqB,MAAM,CAAEL,WAAY,CACpBM,OAAO,CAAEA,CAAA,GAAML,cAAc,CAAC,KAAK,CAAE,CACtC,CAAC,cAGFN,KAAA,QAAKO,SAAS,CAAC,sCAAsC,CAAAE,QAAA,eAEnDX,IAAA,CAACR,aAAa,EACZa,IAAI,CAAEA,IAAK,CACXS,WAAW,CAAEA,CAAA,GAAMN,cAAc,CAAC,IAAI,CAAE,CACxCF,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAGFN,IAAA,SAAMS,SAAS,CAAC,yDAAyD,CAAAE,QAAA,cACvEX,IAAA,CAACV,MAAM,CAACyB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAV,QAAA,cAE9BT,KAAA,CAACf,MAAM,EAAAwB,QAAA,eACLX,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvB,IAAA,CAACwB,eAAe,EAACnB,IAAI,CAAEA,IAAK,CAAE,CAAE,CAAE,CAAC,cAC5DL,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACP,SAAS,EAACY,IAAI,CAAEA,IAAK,CAAE,CAAE,CAAE,CAAC,cAC7DL,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAACN,uBAAuB,GAAE,CAAE,CAAE,CAAC,cACxEM,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACL,QAAQ,EAACU,IAAI,CAAEA,IAAK,CAAE,CAAE,CAAE,CAAC,cACjEL,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACJ,cAAc,EAACS,IAAI,CAAEA,IAAK,CAAE,CAAE,CAAE,CAAC,cACvEL,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACH,sBAAsB,EAACQ,IAAI,CAAEA,IAAK,CAAE,CAAE,CAAE,CAAC,cAC1EL,IAAA,CAACZ,KAAK,EAACkC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvB,IAAA,CAACX,QAAQ,EAACoC,EAAE,CAAC,UAAU,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACzD,CAAC,CACC,CAAC,CACT,CAAC,EACJ,CAAC,CAGLnB,WAAW,eACVP,IAAA,QACES,SAAS,CAAC,qDAAqD,CAC/DkB,OAAO,CAAEA,CAAA,GAAMnB,cAAc,CAAC,KAAK,CAAE,CACtC,CACF,cAGDR,IAAA,CAACF,WAAW,EAAC8B,OAAO,CAAC,SAAS,CAAE,CAAC,EAC9B,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}