{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{PlayIcon,PlusIcon,PencilIcon,TrashIcon,ArrowUpIcon,ArrowDownIcon,EyeIcon,ClockIcon,FilmIcon,MagnifyingGlassIcon}from'@heroicons/react/24/outline';import{supabaseService}from'../../services/supabaseService';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const VideoManagement=()=>{const[videos,setVideos]=useState([]);const[courses,setCourses]=useState([]);const[selectedCourse,setSelectedCourse]=useState('');const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showAddModal,setShowAddModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[editingVideo,setEditingVideo]=useState(null);const[formData,setFormData]=useState({title:'',description:'',video_url:'',duration:0,is_free:false});useEffect(()=>{loadCourses();},[]);useEffect(()=>{if(selectedCourse){loadVideos();}},[selectedCourse]);const loadCourses=async()=>{try{const coursesData=await supabaseService.getAllCourses();setCourses(coursesData||[]);if(coursesData&&coursesData.length>0){setSelectedCourse(coursesData[0].id);}}catch(error){console.error('Error loading courses:',error);toast.error('فشل في تحميل الكورسات');}};const loadVideos=async()=>{if(!selectedCourse)return;try{setLoading(true);const videosData=await supabaseService.getVideosByCourseId(selectedCourse);setVideos(videosData||[]);}catch(error){console.error('Error loading videos:',error);toast.error('فشل في تحميل الفيديوهات');}finally{setLoading(false);}};const handleAddVideo=async()=>{if(!selectedCourse||!formData.title||!formData.video_url){toast.error('يرجى ملء جميع الحقول المطلوبة');return;}try{const nextOrderIndex=Math.max(...videos.map(v=>v.order_index),0)+1;await supabaseService.createVideo({courseId:selectedCourse,title:formData.title,description:formData.description,videoUrl:formData.video_url,duration:formData.duration,orderIndex:nextOrderIndex});toast.success('تم إضافة الفيديو بنجاح');await loadVideos();resetForm();setShowAddModal(false);}catch(error){console.error('Error adding video:',error);toast.error('فشل في إضافة الفيديو');}};const handleEditVideo=async()=>{if(!editingVideo||!formData.title||!formData.video_url){toast.error('يرجى ملء جميع الحقول المطلوبة');return;}try{await supabaseService.updateVideo(editingVideo.id,{title:formData.title,description:formData.description,videoUrl:formData.video_url,duration:formData.duration});toast.success('تم تحديث الفيديو بنجاح');await loadVideos();resetForm();setShowEditModal(false);setEditingVideo(null);}catch(error){console.error('Error updating video:',error);toast.error('فشل في تحديث الفيديو');}};const handleDeleteVideo=async videoId=>{if(!window.confirm('هل أنت متأكد من حذف هذا الفيديو؟'))return;try{await supabaseService.deleteVideo(videoId);toast.success('تم حذف الفيديو بنجاح');await loadVideos();}catch(error){console.error('Error deleting video:',error);toast.error('فشل في حذف الفيديو');}};const handleMoveVideo=async(videoId,direction)=>{const currentVideo=videos.find(v=>v.id===videoId);if(!currentVideo)return;const sortedVideos=[...videos].sort((a,b)=>a.order_index-b.order_index);const currentIndex=sortedVideos.findIndex(v=>v.id===videoId);if(direction==='up'&&currentIndex===0)return;if(direction==='down'&&currentIndex===sortedVideos.length-1)return;const targetIndex=direction==='up'?currentIndex-1:currentIndex+1;const targetVideo=sortedVideos[targetIndex];try{// Swap order indices\nawait Promise.all([supabaseService.updateVideo(currentVideo.id,{orderIndex:targetVideo.order_index}),supabaseService.updateVideo(targetVideo.id,{orderIndex:currentVideo.order_index})]);toast.success('تم تحديث ترتيب الفيديو');await loadVideos();}catch(error){console.error('Error moving video:',error);toast.error('فشل في تحديث الترتيب');}};const openEditModal=video=>{setEditingVideo(video);setFormData({title:video.title,description:video.description||'',video_url:video.video_url,duration:video.duration,is_free:video.is_free});setShowEditModal(true);};const resetForm=()=>{setFormData({title:'',description:'',video_url:'',duration:0,is_free:false});};const filteredVideos=videos.filter(video=>{var _video$description;return video.title.toLowerCase().includes(searchTerm.toLowerCase())||((_video$description=video.description)===null||_video$description===void 0?void 0:_video$description.toLowerCase().includes(searchTerm.toLowerCase()));}).sort((a,b)=>a.order_index-b.order_index);const formatDuration=seconds=>{const hours=Math.floor(seconds/3600);const minutes=Math.floor(seconds%3600/60);const secs=seconds%60;if(hours>0){return`${hours}:${minutes.toString().padStart(2,'0')}:${secs.toString().padStart(2,'0')}`;}return`${minutes}:${secs.toString().padStart(2,'0')}`;};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0648\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0648\\u062A\\u0631\\u062A\\u064A\\u0628 \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddModal(true),disabled:!selectedCourse,className:\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u062C\\u062F\\u064A\\u062F\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCourse,onChange:e=>setSelectedCourse(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- \\u0627\\u062E\\u062A\\u0631 \\u0643\\u0648\\u0631\\u0633 --\"}),courses.map(course=>/*#__PURE__*/_jsx(\"option\",{value:course.id,children:course.title},course.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-4 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(FilmIcon,{className:\"w-5 h-5 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl font-bold text-gray-900\",children:videos.length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-4 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(ClockIcon,{className:\"w-5 h-5 text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl font-bold text-gray-900\",children:formatDuration(videos.reduce((sum,v)=>sum+v.duration,0))})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-4 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-5 h-5 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0645\\u062C\\u0627\\u0646\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl font-bold text-gray-900\",children:videos.filter(v=>v.is_free).length})]})]})})]})]}),selectedCourse&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-4 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]})}),selectedCourse&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm border overflow-hidden\",children:filteredVideos.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(FilmIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 mb-4\",children:\"\\u0627\\u0628\\u062F\\u0623 \\u0628\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u062C\\u062F\\u064A\\u062F \\u0644\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddModal(true),className:\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648\"]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"divide-y divide-gray-200\",children:filteredVideos.map((video,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.1},className:\"p-6 hover:bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded\",children:[\"#\",video.order_index]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-primary-100 rounded-lg\",children:/*#__PURE__*/_jsx(PlayIcon,{className:\"w-5 h-5 text-primary-600\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 truncate\",children:video.title}),video.is_free&&/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:\"\\u0645\\u062C\\u0627\\u0646\\u064A\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 mt-1 line-clamp-2\",children:video.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse mt-2 text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 ml-1\"}),formatDuration(video.duration)]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u062A\\u0645 \\u0627\\u0644\\u0625\\u0646\\u0634\\u0627\\u0621: \",new Date(video.created_at).toLocaleDateString('ar-SA')]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleMoveVideo(video.id,'up'),disabled:index===0,className:\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\",title:\"\\u062A\\u062D\\u0631\\u064A\\u0643 \\u0644\\u0623\\u0639\\u0644\\u0649\",children:/*#__PURE__*/_jsx(ArrowUpIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleMoveVideo(video.id,'down'),disabled:index===filteredVideos.length-1,className:\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\",title:\"\\u062A\\u062D\\u0631\\u064A\\u0643 \\u0644\\u0623\\u0633\\u0641\\u0644\",children:/*#__PURE__*/_jsx(ArrowDownIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>openEditModal(video),className:\"p-2 text-blue-600 hover:text-blue-800\",title:\"\\u062A\\u0639\\u062F\\u064A\\u0644\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteVideo(video.id),className:\"p-2 text-red-600 hover:text-red-800\",title:\"\\u062D\\u0630\\u0641\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})]})]})},video.id))})}),showAddModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},className:\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u062C\\u062F\\u064A\\u062F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.title,onChange:e=>setFormData({...formData,title:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"\\u0623\\u062F\\u062E\\u0644 \\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formData.description,onChange:e=>setFormData({...formData,description:e.target.value}),rows:3,className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"\\u0623\\u062F\\u062E\\u0644 \\u0648\\u0635\\u0641 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:formData.video_url,onChange:e=>setFormData({...formData,video_url:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"https://example.com/video.mp4\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 (\\u0628\\u0627\\u0644\\u062B\\u0648\\u0627\\u0646\\u064A)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:formData.duration,onChange:e=>setFormData({...formData,duration:parseInt(e.target.value)||0}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",placeholder:\"0\",min:\"0\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"is_free\",checked:formData.is_free,onChange:e=>setFormData({...formData,is_free:e.target.checked}),className:\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"is_free\",className:\"mr-2 block text-sm text-gray-900\",children:\"\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0645\\u062C\\u0627\\u0646\\u064A (\\u064A\\u0645\\u0643\\u0646 \\u0645\\u0634\\u0627\\u0647\\u062F\\u062A\\u0647 \\u0628\\u062F\\u0648\\u0646 \\u062A\\u0633\\u062C\\u064A\\u0644)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 space-x-reverse mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowAddModal(false);resetForm();},className:\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleAddVideo,className:\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"})]})]})}),showEditModal&&editingVideo&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},className:\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.title,onChange:e=>setFormData({...formData,title:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formData.description,onChange:e=>setFormData({...formData,description:e.target.value}),rows:3,className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:formData.video_url,onChange:e=>setFormData({...formData,video_url:e.target.value}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 (\\u0628\\u0627\\u0644\\u062B\\u0648\\u0627\\u0646\\u064A)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:formData.duration,onChange:e=>setFormData({...formData,duration:parseInt(e.target.value)||0}),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",min:\"0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 space-x-reverse mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setShowEditModal(false);setEditingVideo(null);resetForm();},className:\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleEditVideo,className:\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:\"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u063A\\u064A\\u064A\\u0631\\u0627\\u062A\"})]})]})})]});};export default VideoManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "PlayIcon", "PlusIcon", "PencilIcon", "TrashIcon", "ArrowUpIcon", "ArrowDownIcon", "EyeIcon", "ClockIcon", "FilmIcon", "MagnifyingGlassIcon", "supabaseService", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "VideoManagement", "videos", "setVideos", "courses", "setCourses", "selectedCourse", "setSelectedCourse", "loading", "setLoading", "searchTerm", "setSearchTerm", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "editingVideo", "setEditingVideo", "formData", "setFormData", "title", "description", "video_url", "duration", "is_free", "loadCourses", "loadVideos", "coursesData", "getAllCourses", "length", "id", "error", "console", "videosData", "getVideosByCourseId", "handleAddVideo", "nextOrderIndex", "Math", "max", "map", "v", "order_index", "createVideo", "courseId", "videoUrl", "orderIndex", "success", "resetForm", "handleEditVideo", "updateVideo", "handleDeleteVideo", "videoId", "window", "confirm", "deleteVideo", "handleMoveVideo", "direction", "currentVideo", "find", "sortedVideos", "sort", "a", "b", "currentIndex", "findIndex", "targetIndex", "targetVideo", "Promise", "all", "openEditModal", "video", "filteredVideos", "filter", "_video$description", "toLowerCase", "includes", "formatDuration", "seconds", "hours", "floor", "minutes", "secs", "toString", "padStart", "className", "children", "onClick", "disabled", "value", "onChange", "e", "target", "course", "reduce", "sum", "type", "placeholder", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "Date", "created_at", "toLocaleDateString", "scale", "rows", "parseInt", "min", "checked", "htmlFor"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/VideoManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlayIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  ArrowUpIcon,\n  ArrowDownIcon,\n  EyeIcon,\n  ClockIcon,\n  FilmIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline';\nimport { supabaseService } from '../../services/supabaseService';\nimport { toast } from 'react-hot-toast';\n\ninterface Video {\n  id: string;\n  course_id: string;\n  title: string;\n  description?: string;\n  video_url: string;\n  duration: number;\n  order_index: number;\n  is_free: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface Course {\n  id: string;\n  title: string;\n  description: string;\n  is_active: boolean;\n}\n\ninterface VideoFormData {\n  title: string;\n  description: string;\n  video_url: string;\n  duration: number;\n  is_free: boolean;\n}\n\nconst VideoManagement: React.FC = () => {\n  const [videos, setVideos] = useState<Video[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [selectedCourse, setSelectedCourse] = useState<string>('');\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [editingVideo, setEditingVideo] = useState<Video | null>(null);\n  const [formData, setFormData] = useState<VideoFormData>({\n    title: '',\n    description: '',\n    video_url: '',\n    duration: 0,\n    is_free: false\n  });\n\n  useEffect(() => {\n    loadCourses();\n  }, []);\n\n  useEffect(() => {\n    if (selectedCourse) {\n      loadVideos();\n    }\n  }, [selectedCourse]);\n\n  const loadCourses = async () => {\n    try {\n      const coursesData = await supabaseService.getAllCourses();\n      setCourses(coursesData || []);\n      if (coursesData && coursesData.length > 0) {\n        setSelectedCourse(coursesData[0].id);\n      }\n    } catch (error) {\n      console.error('Error loading courses:', error);\n      toast.error('فشل في تحميل الكورسات');\n    }\n  };\n\n  const loadVideos = async () => {\n    if (!selectedCourse) return;\n\n    try {\n      setLoading(true);\n      const videosData = await supabaseService.getVideosByCourseId(selectedCourse);\n      setVideos(videosData || []);\n    } catch (error) {\n      console.error('Error loading videos:', error);\n      toast.error('فشل في تحميل الفيديوهات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddVideo = async () => {\n    if (!selectedCourse || !formData.title || !formData.video_url) {\n      toast.error('يرجى ملء جميع الحقول المطلوبة');\n      return;\n    }\n\n    try {\n      const nextOrderIndex = Math.max(...videos.map(v => v.order_index), 0) + 1;\n      \n      await supabaseService.createVideo({\n        courseId: selectedCourse,\n        title: formData.title,\n        description: formData.description,\n        videoUrl: formData.video_url,\n        duration: formData.duration,\n        orderIndex: nextOrderIndex\n      });\n\n      toast.success('تم إضافة الفيديو بنجاح');\n      await loadVideos();\n      resetForm();\n      setShowAddModal(false);\n    } catch (error) {\n      console.error('Error adding video:', error);\n      toast.error('فشل في إضافة الفيديو');\n    }\n  };\n\n  const handleEditVideo = async () => {\n    if (!editingVideo || !formData.title || !formData.video_url) {\n      toast.error('يرجى ملء جميع الحقول المطلوبة');\n      return;\n    }\n\n    try {\n      await supabaseService.updateVideo(editingVideo.id, {\n        title: formData.title,\n        description: formData.description,\n        videoUrl: formData.video_url,\n        duration: formData.duration\n      });\n\n      toast.success('تم تحديث الفيديو بنجاح');\n      await loadVideos();\n      resetForm();\n      setShowEditModal(false);\n      setEditingVideo(null);\n    } catch (error) {\n      console.error('Error updating video:', error);\n      toast.error('فشل في تحديث الفيديو');\n    }\n  };\n\n  const handleDeleteVideo = async (videoId: string) => {\n    if (!window.confirm('هل أنت متأكد من حذف هذا الفيديو؟')) return;\n\n    try {\n      await supabaseService.deleteVideo(videoId);\n      toast.success('تم حذف الفيديو بنجاح');\n      await loadVideos();\n    } catch (error) {\n      console.error('Error deleting video:', error);\n      toast.error('فشل في حذف الفيديو');\n    }\n  };\n\n  const handleMoveVideo = async (videoId: string, direction: 'up' | 'down') => {\n    const currentVideo = videos.find(v => v.id === videoId);\n    if (!currentVideo) return;\n\n    const sortedVideos = [...videos].sort((a, b) => a.order_index - b.order_index);\n    const currentIndex = sortedVideos.findIndex(v => v.id === videoId);\n    \n    if (direction === 'up' && currentIndex === 0) return;\n    if (direction === 'down' && currentIndex === sortedVideos.length - 1) return;\n\n    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;\n    const targetVideo = sortedVideos[targetIndex];\n\n    try {\n      // Swap order indices\n      await Promise.all([\n        supabaseService.updateVideo(currentVideo.id, { orderIndex: targetVideo.order_index }),\n        supabaseService.updateVideo(targetVideo.id, { orderIndex: currentVideo.order_index })\n      ]);\n\n      toast.success('تم تحديث ترتيب الفيديو');\n      await loadVideos();\n    } catch (error) {\n      console.error('Error moving video:', error);\n      toast.error('فشل في تحديث الترتيب');\n    }\n  };\n\n  const openEditModal = (video: Video) => {\n    setEditingVideo(video);\n    setFormData({\n      title: video.title,\n      description: video.description || '',\n      video_url: video.video_url,\n      duration: video.duration,\n      is_free: video.is_free\n    });\n    setShowEditModal(true);\n  };\n\n  const resetForm = () => {\n    setFormData({\n      title: '',\n      description: '',\n      video_url: '',\n      duration: 0,\n      is_free: false\n    });\n  };\n\n  const filteredVideos = videos\n    .filter(video => \n      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      video.description?.toLowerCase().includes(searchTerm.toLowerCase())\n    )\n    .sort((a, b) => a.order_index - b.order_index);\n\n  const formatDuration = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الفيديوهات</h1>\n          <p className=\"text-gray-600 mt-1\">إضافة وتعديل وترتيب فيديوهات الكورسات</p>\n        </div>\n        <button\n          onClick={() => setShowAddModal(true)}\n          disabled={!selectedCourse}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          <PlusIcon className=\"w-5 h-5 ml-2\" />\n          إضافة فيديو جديد\n        </button>\n      </div>\n\n      {/* Course Selection and Stats */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        <div className=\"lg:col-span-1\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            اختر الكورس\n          </label>\n          <select\n            value={selectedCourse}\n            onChange={(e) => setSelectedCourse(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          >\n            <option value=\"\">-- اختر كورس --</option>\n            {courses.map((course) => (\n              <option key={course.id} value={course.id}>\n                {course.title}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"bg-white rounded-lg p-4 shadow-sm border\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <FilmIcon className=\"w-5 h-5 text-blue-600\" />\n              </div>\n              <div className=\"mr-3\">\n                <p className=\"text-sm text-gray-600\">إجمالي الفيديوهات</p>\n                <p className=\"text-xl font-bold text-gray-900\">{videos.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-4 shadow-sm border\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <ClockIcon className=\"w-5 h-5 text-green-600\" />\n              </div>\n              <div className=\"mr-3\">\n                <p className=\"text-sm text-gray-600\">إجمالي المدة</p>\n                <p className=\"text-xl font-bold text-gray-900\">\n                  {formatDuration(videos.reduce((sum, v) => sum + v.duration, 0))}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-4 shadow-sm border\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <EyeIcon className=\"w-5 h-5 text-purple-600\" />\n              </div>\n              <div className=\"mr-3\">\n                <p className=\"text-sm text-gray-600\">فيديوهات مجانية</p>\n                <p className=\"text-xl font-bold text-gray-900\">\n                  {videos.filter(v => v.is_free).length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search */}\n      {selectedCourse && (\n        <div className=\"bg-white rounded-lg p-4 shadow-sm border\">\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث في الفيديوهات...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Videos List */}\n      {selectedCourse && (\n        <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n          {filteredVideos.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <FilmIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد فيديوهات</h3>\n              <p className=\"text-gray-500 mb-4\">ابدأ بإضافة فيديو جديد لهذا الكورس</p>\n              <button\n                onClick={() => setShowAddModal(true)}\n                className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                <PlusIcon className=\"w-5 h-5 ml-2\" />\n                إضافة فيديو\n              </button>\n            </div>\n          ) : (\n            <div className=\"divide-y divide-gray-200\">\n              {filteredVideos.map((video, index) => (\n                <motion.div\n                  key={video.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  className=\"p-6 hover:bg-gray-50\"\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4 space-x-reverse flex-1\">\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <span className=\"text-sm font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                          #{video.order_index}\n                        </span>\n                        <div className=\"p-2 bg-primary-100 rounded-lg\">\n                          <PlayIcon className=\"w-5 h-5 text-primary-600\" />\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {video.title}\n                          </h3>\n                          {video.is_free && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                              مجاني\n                            </span>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-500 mt-1 line-clamp-2\">\n                          {video.description}\n                        </p>\n                        <div className=\"flex items-center space-x-4 space-x-reverse mt-2 text-sm text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <ClockIcon className=\"w-4 h-4 ml-1\" />\n                            {formatDuration(video.duration)}\n                          </span>\n                          <span>\n                            تم الإنشاء: {new Date(video.created_at).toLocaleDateString('ar-SA')}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      {/* Move buttons */}\n                      <button\n                        onClick={() => handleMoveVideo(video.id, 'up')}\n                        disabled={index === 0}\n                        className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        title=\"تحريك لأعلى\"\n                      >\n                        <ArrowUpIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleMoveVideo(video.id, 'down')}\n                        disabled={index === filteredVideos.length - 1}\n                        className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        title=\"تحريك لأسفل\"\n                      >\n                        <ArrowDownIcon className=\"w-4 h-4\" />\n                      </button>\n\n                      {/* Action buttons */}\n                      <button\n                        onClick={() => openEditModal(video)}\n                        className=\"p-2 text-blue-600 hover:text-blue-800\"\n                        title=\"تعديل\"\n                      >\n                        <PencilIcon className=\"w-4 h-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleDeleteVideo(video.id)}\n                        className=\"p-2 text-red-600 hover:text-red-800\"\n                        title=\"حذف\"\n                      >\n                        <TrashIcon className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Add Video Modal */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\n          >\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">إضافة فيديو جديد</h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  عنوان الفيديو *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.title}\n                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"أدخل عنوان الفيديو\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  وصف الفيديو\n                </label>\n                <textarea\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"أدخل وصف الفيديو\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رابط الفيديو *\n                </label>\n                <input\n                  type=\"url\"\n                  value={formData.video_url}\n                  onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"https://example.com/video.mp4\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  مدة الفيديو (بالثواني)\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.duration}\n                  onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 0 })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"0\"\n                  min=\"0\"\n                />\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"is_free\"\n                  checked={formData.is_free}\n                  onChange={(e) => setFormData({ ...formData, is_free: e.target.checked })}\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"is_free\" className=\"mr-2 block text-sm text-gray-900\">\n                  فيديو مجاني (يمكن مشاهدته بدون تسجيل)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 space-x-reverse mt-6\">\n              <button\n                onClick={() => {\n                  setShowAddModal(false);\n                  resetForm();\n                }}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                إلغاء\n              </button>\n              <button\n                onClick={handleAddVideo}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                إضافة الفيديو\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* Edit Video Modal */}\n      {showEditModal && editingVideo && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\n          >\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">تعديل الفيديو</h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  عنوان الفيديو *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.title}\n                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  وصف الفيديو\n                </label>\n                <textarea\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رابط الفيديو *\n                </label>\n                <input\n                  type=\"url\"\n                  value={formData.video_url}\n                  onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  مدة الفيديو (بالثواني)\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.duration}\n                  onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 0 })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  min=\"0\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 space-x-reverse mt-6\">\n              <button\n                onClick={() => {\n                  setShowEditModal(false);\n                  setEditingVideo(null);\n                  resetForm();\n                }}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                إلغاء\n              </button>\n              <button\n                onClick={handleEditVideo}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                حفظ التغييرات\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,WAAW,CACXC,aAAa,CACbC,OAAO,CACPC,SAAS,CACTC,QAAQ,CACRC,mBAAmB,KACd,6BAA6B,CACpC,OAASC,eAAe,KAAQ,gCAAgC,CAChE,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA8BxC,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGrB,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAACwB,cAAc,CAAEC,iBAAiB,CAAC,CAAGzB,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAe,IAAI,CAAC,CACpE,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGrC,QAAQ,CAAgB,CACtDsC,KAAK,CAAE,EAAE,CACTC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CAEFzC,SAAS,CAAC,IAAM,CACd0C,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN1C,SAAS,CAAC,IAAM,CACd,GAAIuB,cAAc,CAAE,CAClBoB,UAAU,CAAC,CAAC,CACd,CACF,CAAC,CAAE,CAACpB,cAAc,CAAC,CAAC,CAEpB,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAE,WAAW,CAAG,KAAM,CAAAhC,eAAe,CAACiC,aAAa,CAAC,CAAC,CACzDvB,UAAU,CAACsB,WAAW,EAAI,EAAE,CAAC,CAC7B,GAAIA,WAAW,EAAIA,WAAW,CAACE,MAAM,CAAG,CAAC,CAAE,CACzCtB,iBAAiB,CAACoB,WAAW,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,CACtC,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CnC,KAAK,CAACmC,KAAK,CAAC,uBAAuB,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAL,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACpB,cAAc,CAAE,OAErB,GAAI,CACFG,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,UAAU,CAAG,KAAM,CAAAtC,eAAe,CAACuC,mBAAmB,CAAC5B,cAAc,CAAC,CAC5EH,SAAS,CAAC8B,UAAU,EAAI,EAAE,CAAC,CAC7B,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CnC,KAAK,CAACmC,KAAK,CAAC,yBAAyB,CAAC,CACxC,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAAC7B,cAAc,EAAI,CAACY,QAAQ,CAACE,KAAK,EAAI,CAACF,QAAQ,CAACI,SAAS,CAAE,CAC7D1B,KAAK,CAACmC,KAAK,CAAC,+BAA+B,CAAC,CAC5C,OACF,CAEA,GAAI,CACF,KAAM,CAAAK,cAAc,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGpC,MAAM,CAACqC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CAAE,CAAC,CAAC,CAAG,CAAC,CAEzE,KAAM,CAAA9C,eAAe,CAAC+C,WAAW,CAAC,CAChCC,QAAQ,CAAErC,cAAc,CACxBc,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,WAAW,CAAEH,QAAQ,CAACG,WAAW,CACjCuB,QAAQ,CAAE1B,QAAQ,CAACI,SAAS,CAC5BC,QAAQ,CAAEL,QAAQ,CAACK,QAAQ,CAC3BsB,UAAU,CAAET,cACd,CAAC,CAAC,CAEFxC,KAAK,CAACkD,OAAO,CAAC,wBAAwB,CAAC,CACvC,KAAM,CAAApB,UAAU,CAAC,CAAC,CAClBqB,SAAS,CAAC,CAAC,CACXlC,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CnC,KAAK,CAACmC,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAiB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAChC,YAAY,EAAI,CAACE,QAAQ,CAACE,KAAK,EAAI,CAACF,QAAQ,CAACI,SAAS,CAAE,CAC3D1B,KAAK,CAACmC,KAAK,CAAC,+BAA+B,CAAC,CAC5C,OACF,CAEA,GAAI,CACF,KAAM,CAAApC,eAAe,CAACsD,WAAW,CAACjC,YAAY,CAACc,EAAE,CAAE,CACjDV,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,WAAW,CAAEH,QAAQ,CAACG,WAAW,CACjCuB,QAAQ,CAAE1B,QAAQ,CAACI,SAAS,CAC5BC,QAAQ,CAAEL,QAAQ,CAACK,QACrB,CAAC,CAAC,CAEF3B,KAAK,CAACkD,OAAO,CAAC,wBAAwB,CAAC,CACvC,KAAM,CAAApB,UAAU,CAAC,CAAC,CAClBqB,SAAS,CAAC,CAAC,CACXhC,gBAAgB,CAAC,KAAK,CAAC,CACvBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,MAAOc,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CnC,KAAK,CAACmC,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAmB,iBAAiB,CAAG,KAAO,CAAAC,OAAe,EAAK,CACnD,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,CAAE,OAEzD,GAAI,CACF,KAAM,CAAA1D,eAAe,CAAC2D,WAAW,CAACH,OAAO,CAAC,CAC1CvD,KAAK,CAACkD,OAAO,CAAC,sBAAsB,CAAC,CACrC,KAAM,CAAApB,UAAU,CAAC,CAAC,CACpB,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CnC,KAAK,CAACmC,KAAK,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAwB,eAAe,CAAG,KAAAA,CAAOJ,OAAe,CAAEK,SAAwB,GAAK,CAC3E,KAAM,CAAAC,YAAY,CAAGvD,MAAM,CAACwD,IAAI,CAAClB,CAAC,EAAIA,CAAC,CAACV,EAAE,GAAKqB,OAAO,CAAC,CACvD,GAAI,CAACM,YAAY,CAAE,OAEnB,KAAM,CAAAE,YAAY,CAAG,CAAC,GAAGzD,MAAM,CAAC,CAAC0D,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACpB,WAAW,CAAGqB,CAAC,CAACrB,WAAW,CAAC,CAC9E,KAAM,CAAAsB,YAAY,CAAGJ,YAAY,CAACK,SAAS,CAACxB,CAAC,EAAIA,CAAC,CAACV,EAAE,GAAKqB,OAAO,CAAC,CAElE,GAAIK,SAAS,GAAK,IAAI,EAAIO,YAAY,GAAK,CAAC,CAAE,OAC9C,GAAIP,SAAS,GAAK,MAAM,EAAIO,YAAY,GAAKJ,YAAY,CAAC9B,MAAM,CAAG,CAAC,CAAE,OAEtE,KAAM,CAAAoC,WAAW,CAAGT,SAAS,GAAK,IAAI,CAAGO,YAAY,CAAG,CAAC,CAAGA,YAAY,CAAG,CAAC,CAC5E,KAAM,CAAAG,WAAW,CAAGP,YAAY,CAACM,WAAW,CAAC,CAE7C,GAAI,CACF;AACA,KAAM,CAAAE,OAAO,CAACC,GAAG,CAAC,CAChBzE,eAAe,CAACsD,WAAW,CAACQ,YAAY,CAAC3B,EAAE,CAAE,CAAEe,UAAU,CAAEqB,WAAW,CAACzB,WAAY,CAAC,CAAC,CACrF9C,eAAe,CAACsD,WAAW,CAACiB,WAAW,CAACpC,EAAE,CAAE,CAAEe,UAAU,CAAEY,YAAY,CAAChB,WAAY,CAAC,CAAC,CACtF,CAAC,CAEF7C,KAAK,CAACkD,OAAO,CAAC,wBAAwB,CAAC,CACvC,KAAM,CAAApB,UAAU,CAAC,CAAC,CACpB,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CnC,KAAK,CAACmC,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAsC,aAAa,CAAIC,KAAY,EAAK,CACtCrD,eAAe,CAACqD,KAAK,CAAC,CACtBnD,WAAW,CAAC,CACVC,KAAK,CAAEkD,KAAK,CAAClD,KAAK,CAClBC,WAAW,CAAEiD,KAAK,CAACjD,WAAW,EAAI,EAAE,CACpCC,SAAS,CAAEgD,KAAK,CAAChD,SAAS,CAC1BC,QAAQ,CAAE+C,KAAK,CAAC/C,QAAQ,CACxBC,OAAO,CAAE8C,KAAK,CAAC9C,OACjB,CAAC,CAAC,CACFT,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAgC,SAAS,CAAGA,CAAA,GAAM,CACtB5B,WAAW,CAAC,CACVC,KAAK,CAAE,EAAE,CACTC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA+C,cAAc,CAAGrE,MAAM,CAC1BsE,MAAM,CAACF,KAAK,OAAAG,kBAAA,OACX,CAAAH,KAAK,CAAClD,KAAK,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC,IAAAD,kBAAA,CAC5DH,KAAK,CAACjD,WAAW,UAAAoD,kBAAA,iBAAjBA,kBAAA,CAAmBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC,GACrE,CAAC,CACAd,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACpB,WAAW,CAAGqB,CAAC,CAACrB,WAAW,CAAC,CAEhD,KAAM,CAAAmC,cAAc,CAAIC,OAAe,EAAK,CAC1C,KAAM,CAAAC,KAAK,CAAGzC,IAAI,CAAC0C,KAAK,CAACF,OAAO,CAAG,IAAI,CAAC,CACxC,KAAM,CAAAG,OAAO,CAAG3C,IAAI,CAAC0C,KAAK,CAAEF,OAAO,CAAG,IAAI,CAAI,EAAE,CAAC,CACjD,KAAM,CAAAI,IAAI,CAAGJ,OAAO,CAAG,EAAE,CAEzB,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,MAAO,GAAGA,KAAK,IAAIE,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9F,CACA,MAAO,GAAGH,OAAO,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzD,CAAC,CAED,GAAI3E,OAAO,CAAE,CACX,mBACEV,IAAA,QAAKsF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDvF,IAAA,QAAKsF,SAAS,CAAC,mEAAmE,CAAM,CAAC,CACtF,CAAC,CAEV,CAEA,mBACEpF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBrF,KAAA,QAAKoF,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6FAAgB,CAAI,CAAC,cACtEvF,IAAA,MAAGsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,4MAAqC,CAAG,CAAC,EACxE,CAAC,cACNrF,KAAA,WACEsF,OAAO,CAAEA,CAAA,GAAMzE,eAAe,CAAC,IAAI,CAAE,CACrC0E,QAAQ,CAAE,CAACjF,cAAe,CAC1B8E,SAAS,CAAC,gKAAgK,CAAAC,QAAA,eAE1KvF,IAAA,CAACZ,QAAQ,EAACkG,SAAS,CAAC,cAAc,CAAE,CAAC,yFAEvC,EAAQ,CAAC,EACN,CAAC,cAGNpF,KAAA,QAAKoF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDrF,KAAA,QAAKoF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRrF,KAAA,WACEwF,KAAK,CAAElF,cAAe,CACtBmF,QAAQ,CAAGC,CAAC,EAAKnF,iBAAiB,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDJ,SAAS,CAAC,iHAAiH,CAAAC,QAAA,eAE3HvF,IAAA,WAAQ0F,KAAK,CAAC,EAAE,CAAAH,QAAA,CAAC,yDAAe,CAAQ,CAAC,CACxCjF,OAAO,CAACmC,GAAG,CAAEqD,MAAM,eAClB9F,IAAA,WAAwB0F,KAAK,CAAEI,MAAM,CAAC9D,EAAG,CAAAuD,QAAA,CACtCO,MAAM,CAACxE,KAAK,EADFwE,MAAM,CAAC9D,EAEZ,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN9B,KAAA,QAAKoF,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEvF,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCvF,IAAA,CAACL,QAAQ,EAAC2F,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mGAAiB,CAAG,CAAC,cAC1DvF,IAAA,MAAGsF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAEnF,MAAM,CAAC2B,MAAM,CAAI,CAAC,EAC/D,CAAC,EACH,CAAC,CACH,CAAC,cAEN/B,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CvF,IAAA,CAACN,SAAS,EAAC4F,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC7C,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qEAAY,CAAG,CAAC,cACrDvF,IAAA,MAAGsF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC3CT,cAAc,CAAC1E,MAAM,CAAC2F,MAAM,CAAC,CAACC,GAAG,CAAEtD,CAAC,GAAKsD,GAAG,CAAGtD,CAAC,CAACjB,QAAQ,CAAE,CAAC,CAAC,CAAC,CAC9D,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAENzB,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvF,IAAA,CAACP,OAAO,EAAC6F,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC5C,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uFAAe,CAAG,CAAC,cACxDvF,IAAA,MAAGsF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC3CnF,MAAM,CAACsE,MAAM,CAAChC,CAAC,EAAIA,CAAC,CAAChB,OAAO,CAAC,CAACK,MAAM,CACpC,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CAGLvB,cAAc,eACbR,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBvF,IAAA,CAACJ,mBAAmB,EAAC0F,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5GtF,IAAA,UACEiG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,6GAAwB,CACpCR,KAAK,CAAE9E,UAAW,CAClB+E,QAAQ,CAAGC,CAAC,EAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CJ,SAAS,CAAC,uHAAuH,CAClI,CAAC,EACC,CAAC,CACH,CACN,CAGA9E,cAAc,eACbR,IAAA,QAAKsF,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEd,cAAc,CAAC1C,MAAM,GAAK,CAAC,cAC1B7B,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,CAACL,QAAQ,EAAC2F,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7DtF,IAAA,OAAIsF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wFAAgB,CAAI,CAAC,cAC5EvF,IAAA,MAAGsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,qLAAkC,CAAG,CAAC,cACxErF,KAAA,WACEsF,OAAO,CAAEA,CAAA,GAAMzE,eAAe,CAAC,IAAI,CAAE,CACrCuE,SAAS,CAAC,gHAAgH,CAAAC,QAAA,eAE1HvF,IAAA,CAACZ,QAAQ,EAACkG,SAAS,CAAC,cAAc,CAAE,CAAC,gEAEvC,EAAQ,CAAC,EACN,CAAC,cAENtF,IAAA,QAAKsF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtCd,cAAc,CAAChC,GAAG,CAAC,CAAC+B,KAAK,CAAE2B,KAAK,gBAC/BnG,IAAA,CAACd,MAAM,CAACkH,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAEP,KAAK,CAAG,GAAI,CAAE,CACnCb,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cAEhCrF,KAAA,QAAKoF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrF,KAAA,QAAKoF,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjErF,KAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DrF,KAAA,SAAMoF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,EAAC,GAC/E,CAACf,KAAK,CAAC7B,WAAW,EACf,CAAC,cACP3C,IAAA,QAAKsF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CvF,IAAA,CAACb,QAAQ,EAACmG,SAAS,CAAC,0BAA0B,CAAE,CAAC,CAC9C,CAAC,EACH,CAAC,cAENpF,KAAA,QAAKoF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrF,KAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DvF,IAAA,OAAIsF,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACvDf,KAAK,CAAClD,KAAK,CACV,CAAC,CACJkD,KAAK,CAAC9C,OAAO,eACZ1B,IAAA,SAAMsF,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAAC,gCAElH,CAAM,CACP,EACE,CAAC,cACNvF,IAAA,MAAGsF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CACnDf,KAAK,CAACjD,WAAW,CACjB,CAAC,cACJrB,KAAA,QAAKoF,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eACrFrF,KAAA,SAAMoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCvF,IAAA,CAACN,SAAS,EAAC4F,SAAS,CAAC,cAAc,CAAE,CAAC,CACrCR,cAAc,CAACN,KAAK,CAAC/C,QAAQ,CAAC,EAC3B,CAAC,cACPvB,KAAA,SAAAqF,QAAA,EAAM,2DACQ,CAAC,GAAI,CAAAoB,IAAI,CAACnC,KAAK,CAACoC,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,EAC/D,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAEN3G,KAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1DvF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM/B,eAAe,CAACe,KAAK,CAACxC,EAAE,CAAE,IAAI,CAAE,CAC/CyD,QAAQ,CAAEU,KAAK,GAAK,CAAE,CACtBb,SAAS,CAAC,uFAAuF,CACjGhE,KAAK,CAAC,+DAAa,CAAAiE,QAAA,cAEnBvF,IAAA,CAACT,WAAW,EAAC+F,SAAS,CAAC,SAAS,CAAE,CAAC,CAC7B,CAAC,cACTtF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM/B,eAAe,CAACe,KAAK,CAACxC,EAAE,CAAE,MAAM,CAAE,CACjDyD,QAAQ,CAAEU,KAAK,GAAK1B,cAAc,CAAC1C,MAAM,CAAG,CAAE,CAC9CuD,SAAS,CAAC,uFAAuF,CACjGhE,KAAK,CAAC,+DAAa,CAAAiE,QAAA,cAEnBvF,IAAA,CAACR,aAAa,EAAC8F,SAAS,CAAC,SAAS,CAAE,CAAC,CAC/B,CAAC,cAGTtF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMjB,aAAa,CAACC,KAAK,CAAE,CACpCc,SAAS,CAAC,uCAAuC,CACjDhE,KAAK,CAAC,gCAAO,CAAAiE,QAAA,cAEbvF,IAAA,CAACX,UAAU,EAACiG,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTtF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMpC,iBAAiB,CAACoB,KAAK,CAACxC,EAAE,CAAE,CAC3CsD,SAAS,CAAC,qCAAqC,CAC/ChE,KAAK,CAAC,oBAAK,CAAAiE,QAAA,cAEXvF,IAAA,CAACV,SAAS,EAACgG,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,EACH,CAAC,EA9EDd,KAAK,CAACxC,EA+ED,CACb,CAAC,CACC,CACN,CACE,CACN,CAGAlB,YAAY,eACXd,IAAA,QAAKsF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FrF,KAAA,CAAChB,MAAM,CAACkH,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEQ,KAAK,CAAE,IAAK,CAAE,CACrCN,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEQ,KAAK,CAAE,CAAE,CAAE,CAClCxB,SAAS,CAAC,uEAAuE,CAAAC,QAAA,eAEjFvF,IAAA,OAAIsF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wFAAgB,CAAI,CAAC,cAE5ErF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6EAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,MAAM,CACXP,KAAK,CAAEtE,QAAQ,CAACE,KAAM,CACtBqE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEE,KAAK,CAAEsE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CACrEJ,SAAS,CAAC,iHAAiH,CAC3HY,WAAW,CAAC,oGAAoB,CACjC,CAAC,EACC,CAAC,cAENhG,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRvF,IAAA,aACE0F,KAAK,CAAEtE,QAAQ,CAACG,WAAY,CAC5BoE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEG,WAAW,CAAEqE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CAC3EqB,IAAI,CAAE,CAAE,CACRzB,SAAS,CAAC,iHAAiH,CAC3HY,WAAW,CAAC,wFAAkB,CAC/B,CAAC,EACC,CAAC,cAENhG,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,uEAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,KAAK,CACVP,KAAK,CAAEtE,QAAQ,CAACI,SAAU,CAC1BmE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEI,SAAS,CAAEoE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CACzEJ,SAAS,CAAC,iHAAiH,CAC3HY,WAAW,CAAC,+BAA+B,CAC5C,CAAC,EACC,CAAC,cAENhG,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kHAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,QAAQ,CACbP,KAAK,CAAEtE,QAAQ,CAACK,QAAS,CACzBkE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEK,QAAQ,CAAEuF,QAAQ,CAACpB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAAI,CAAE,CAAC,CAAE,CACvFJ,SAAS,CAAC,iHAAiH,CAC3HY,WAAW,CAAC,GAAG,CACfe,GAAG,CAAC,GAAG,CACR,CAAC,EACC,CAAC,cAEN/G,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,UACEiG,IAAI,CAAC,UAAU,CACfjE,EAAE,CAAC,SAAS,CACZkF,OAAO,CAAE9F,QAAQ,CAACM,OAAQ,CAC1BiE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEM,OAAO,CAAEkE,CAAC,CAACC,MAAM,CAACqB,OAAQ,CAAC,CAAE,CACzE5B,SAAS,CAAC,yEAAyE,CACpF,CAAC,cACFtF,IAAA,UAAOmH,OAAO,CAAC,SAAS,CAAC7B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,6LAEtE,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAENrF,KAAA,QAAKoF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DvF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM,CACbzE,eAAe,CAAC,KAAK,CAAC,CACtBkC,SAAS,CAAC,CAAC,CACb,CAAE,CACFqC,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,gCAED,CAAQ,CAAC,cACTvF,IAAA,WACEwF,OAAO,CAAEnD,cAAe,CACxBiD,SAAS,CAAC,uFAAuF,CAAAC,QAAA,CAClG,2EAED,CAAQ,CAAC,EACN,CAAC,EACI,CAAC,CACV,CACN,CAGAvE,aAAa,EAAIE,YAAY,eAC5BlB,IAAA,QAAKsF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FrF,KAAA,CAAChB,MAAM,CAACkH,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEQ,KAAK,CAAE,IAAK,CAAE,CACrCN,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEQ,KAAK,CAAE,CAAE,CAAE,CAClCxB,SAAS,CAAC,uEAAuE,CAAAC,QAAA,eAEjFvF,IAAA,OAAIsF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2EAAa,CAAI,CAAC,cAEzErF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6EAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,MAAM,CACXP,KAAK,CAAEtE,QAAQ,CAACE,KAAM,CACtBqE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEE,KAAK,CAAEsE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CACrEJ,SAAS,CAAC,iHAAiH,CAC5H,CAAC,EACC,CAAC,cAENpF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRvF,IAAA,aACE0F,KAAK,CAAEtE,QAAQ,CAACG,WAAY,CAC5BoE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEG,WAAW,CAAEqE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CAC3EqB,IAAI,CAAE,CAAE,CACRzB,SAAS,CAAC,iHAAiH,CAC5H,CAAC,EACC,CAAC,cAENpF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,uEAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,KAAK,CACVP,KAAK,CAAEtE,QAAQ,CAACI,SAAU,CAC1BmE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEI,SAAS,CAAEoE,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CACzEJ,SAAS,CAAC,iHAAiH,CAC5H,CAAC,EACC,CAAC,cAENpF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kHAEhE,CAAO,CAAC,cACRvF,IAAA,UACEiG,IAAI,CAAC,QAAQ,CACbP,KAAK,CAAEtE,QAAQ,CAACK,QAAS,CACzBkE,QAAQ,CAAGC,CAAC,EAAKvE,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEK,QAAQ,CAAEuF,QAAQ,CAACpB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAAI,CAAE,CAAC,CAAE,CACvFJ,SAAS,CAAC,iHAAiH,CAC3H2B,GAAG,CAAC,GAAG,CACR,CAAC,EACC,CAAC,EACH,CAAC,cAEN/G,KAAA,QAAKoF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DvF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM,CACbvE,gBAAgB,CAAC,KAAK,CAAC,CACvBE,eAAe,CAAC,IAAI,CAAC,CACrB8B,SAAS,CAAC,CAAC,CACb,CAAE,CACFqC,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,gCAED,CAAQ,CAAC,cACTvF,IAAA,WACEwF,OAAO,CAAEtC,eAAgB,CACzBoC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,CAClG,2EAED,CAAQ,CAAC,EACN,CAAC,EACI,CAAC,CACV,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAApF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}