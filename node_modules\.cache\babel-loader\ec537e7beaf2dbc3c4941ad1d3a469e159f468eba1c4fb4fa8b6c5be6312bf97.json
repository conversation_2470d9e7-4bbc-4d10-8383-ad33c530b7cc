{"ast": null, "code": "import React,{useState}from'react';import{Routes,Route,Navigate}from'react-router-dom';import{motion}from'framer-motion';// Components\nimport AdminSidebar from'../../components/Admin/AdminSidebar';import AdminHeader from'../../components/Admin/AdminHeader';import DashboardOverview from'../../components/Admin/DashboardOverview';import CategoriesManagement from'../../components/Admin/CategoriesManagement';import CoursesManagement from'../../components/Admin/CoursesManagement';import StudentsManagement from'../../components/Admin/StudentsManagement';import QuizzesManagement from'../../components/Admin/QuizzesManagement';import AdvancedCertificatesManagement from'../../components/Admin/AdvancedCertificatesManagement';import CourseEnrollmentManagement from'../../components/Admin/CourseEnrollmentManagement';import VideoManagement from'../../components/Admin/VideoManagement';import AnalyticsPage from'../../components/Admin/AnalyticsPage';import SettingsPage from'../../components/Admin/SettingsPage';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminDashboard=_ref=>{let{user,onLogout}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-screen bg-gray-50\",dir:\"rtl\",children:[/*#__PURE__*/_jsx(AdminSidebar,{isOpen:sidebarOpen,onClose:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col overflow-hidden\",children:[/*#__PURE__*/_jsx(AdminHeader,{user:user,onMenuClick:()=>setSidebarOpen(true),onLogout:onLogout}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(DashboardOverview,{})}),/*#__PURE__*/_jsx(Route,{path:\"/categories\",element:/*#__PURE__*/_jsx(CategoriesManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/courses\",element:/*#__PURE__*/_jsx(CoursesManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/videos\",element:/*#__PURE__*/_jsx(VideoManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/students\",element:/*#__PURE__*/_jsx(StudentsManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/enrollments\",element:/*#__PURE__*/_jsx(CourseEnrollmentManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/quizzes\",element:/*#__PURE__*/_jsx(QuizzesManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/certificates\",element:/*#__PURE__*/_jsx(AdvancedCertificatesManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"/analytics\",element:/*#__PURE__*/_jsx(AnalyticsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(SettingsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/admin\",replace:true})})]})})})]}),sidebarOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",onClick:()=>setSidebarOpen(false)})]});};export default AdminDashboard;", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "Navigate", "motion", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "DashboardOverview", "CategoriesManagement", "CoursesManagement", "StudentsManagement", "QuizzesManagement", "AdvancedCertificatesManagement", "CourseEnrollmentManagement", "VideoManagement", "AnalyticsPage", "SettingsPage", "jsx", "_jsx", "jsxs", "_jsxs", "AdminDashboard", "_ref", "user", "onLogout", "sidebarOpen", "setSidebarOpen", "className", "dir", "children", "isOpen", "onClose", "onMenuClick", "div", "initial", "opacity", "y", "animate", "transition", "duration", "path", "element", "to", "replace", "onClick"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/pages/admin/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\n// Components\nimport AdminSidebar from '../../components/Admin/AdminSidebar';\nimport AdminHeader from '../../components/Admin/AdminHeader';\nimport DashboardOverview from '../../components/Admin/DashboardOverview';\nimport CategoriesManagement from '../../components/Admin/CategoriesManagement';\nimport CoursesManagement from '../../components/Admin/CoursesManagement';\nimport StudentsManagement from '../../components/Admin/StudentsManagement';\nimport QuizzesManagement from '../../components/Admin/QuizzesManagement';\nimport AdvancedCertificatesManagement from '../../components/Admin/AdvancedCertificatesManagement';\nimport CourseEnrollmentManagement from '../../components/Admin/CourseEnrollmentManagement';\nimport VideoManagement from '../../components/Admin/VideoManagement';\nimport AnalyticsPage from '../../components/Admin/AnalyticsPage';\nimport SettingsPage from '../../components/Admin/SettingsPage';\n\n// Types\nimport { Admin } from '../../types';\n\ninterface AdminDashboardProps {\n  user: Admin;\n  onLogout: () => void;\n}\n\nconst AdminDashboard: React.FC<AdminDashboardProps> = ({ user, onLogout }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Sidebar */}\n      <AdminSidebar \n        isOpen={sidebarOpen} \n        onClose={() => setSidebarOpen(false)} \n      />\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <AdminHeader \n          user={user}\n          onMenuClick={() => setSidebarOpen(true)}\n          onLogout={onLogout}\n        />\n        \n        {/* Page Content */}\n        <main className=\"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Routes>\n              <Route path=\"/\" element={<DashboardOverview />} />\n              <Route path=\"/categories\" element={<CategoriesManagement />} />\n              <Route path=\"/courses\" element={<CoursesManagement />} />\n              <Route path=\"/videos\" element={<VideoManagement />} />\n              <Route path=\"/students\" element={<StudentsManagement />} />\n              <Route path=\"/enrollments\" element={<CourseEnrollmentManagement />} />\n              <Route path=\"/quizzes\" element={<QuizzesManagement />} />\n              <Route path=\"/certificates\" element={<AdvancedCertificatesManagement />} />\n              <Route path=\"/analytics\" element={<AnalyticsPage />} />\n              <Route path=\"/settings\" element={<SettingsPage />} />\n              <Route path=\"*\" element={<Navigate to=\"/admin\" replace />} />\n            </Routes>\n          </motion.div>\n        </main>\n      </div>\n      \n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,MAAM,KAAQ,eAAe,CAEtC;AACA,MAAO,CAAAC,YAAY,KAAM,qCAAqC,CAC9D,MAAO,CAAAC,WAAW,KAAM,oCAAoC,CAC5D,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,MAAO,CAAAC,oBAAoB,KAAM,6CAA6C,CAC9E,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,MAAO,CAAAC,kBAAkB,KAAM,2CAA2C,CAC1E,MAAO,CAAAC,iBAAiB,KAAM,0CAA0C,CACxE,MAAO,CAAAC,8BAA8B,KAAM,uDAAuD,CAClG,MAAO,CAAAC,0BAA0B,KAAM,mDAAmD,CAC1F,MAAO,CAAAC,eAAe,KAAM,wCAAwC,CACpE,MAAO,CAAAC,aAAa,KAAM,sCAAsC,CAChE,MAAO,CAAAC,YAAY,KAAM,qCAAqC,CAE9D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQA,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAAAF,IAAA,CACvE,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAErD,mBACEoB,KAAA,QAAKO,SAAS,CAAC,0BAA0B,CAACC,GAAG,CAAC,KAAK,CAAAC,QAAA,eAEjDX,IAAA,CAACb,YAAY,EACXyB,MAAM,CAAEL,WAAY,CACpBM,OAAO,CAAEA,CAAA,GAAML,cAAc,CAAC,KAAK,CAAE,CACtC,CAAC,cAGFN,KAAA,QAAKO,SAAS,CAAC,sCAAsC,CAAAE,QAAA,eAEnDX,IAAA,CAACZ,WAAW,EACViB,IAAI,CAAEA,IAAK,CACXS,WAAW,CAAEA,CAAA,GAAMN,cAAc,CAAC,IAAI,CAAE,CACxCF,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAGFN,IAAA,SAAMS,SAAS,CAAC,yDAAyD,CAAAE,QAAA,cACvEX,IAAA,CAACd,MAAM,CAAC6B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAV,QAAA,cAE9BT,KAAA,CAACnB,MAAM,EAAA4B,QAAA,eACLX,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvB,IAAA,CAACX,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAClDW,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEvB,IAAA,CAACV,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAC/DU,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACT,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACzDS,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAE,CAAC,cACtDI,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvB,IAAA,CAACR,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAC3DQ,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAACL,0BAA0B,GAAE,CAAE,CAAE,CAAC,cACtEK,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACP,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACzDO,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACN,8BAA8B,GAAE,CAAE,CAAE,CAAC,cAC3EM,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEvB,IAAA,CAACH,aAAa,GAAE,CAAE,CAAE,CAAC,cACvDG,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvB,IAAA,CAACF,YAAY,GAAE,CAAE,CAAE,CAAC,cACrDE,IAAA,CAAChB,KAAK,EAACsC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvB,IAAA,CAACf,QAAQ,EAACuC,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACvD,CAAC,CACC,CAAC,CACT,CAAC,EACJ,CAAC,CAGLlB,WAAW,eACVP,IAAA,QACES,SAAS,CAAC,qDAAqD,CAC/DiB,OAAO,CAAEA,CAAA,GAAMlB,cAAc,CAAC,KAAK,CAAE,CACtC,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}