import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowPathIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { useSessionWarning } from '../../hooks/useSessionPersistence';

const SessionWarning: React.FC = () => {
  const { showWarning, timeRemaining, extendSession, logout } = useSessionWarning();
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    if (showWarning && timeRemaining > 0) {
      setCountdown(Math.ceil(timeRemaining / 1000));
      
      const countdownInterval = setInterval(() => {
        const remaining = Math.ceil(timeRemaining / 1000);
        setCountdown(remaining);
        
        if (remaining <= 0) {
          clearInterval(countdownInterval);
          logout();
        }
      }, 1000);

      return () => clearInterval(countdownInterval);
    }
  }, [showWarning, timeRemaining, logout]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <AnimatePresence>
      {showWarning && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
          />
          
          {/* Warning Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
              {/* Header */}
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="w-8 h-8 text-yellow-500" />
                </div>
                <div className="mr-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    تحذير انتهاء الجلسة
                  </h3>
                </div>
              </div>

              {/* Content */}
              <div className="mb-6">
                <p className="text-gray-600 mb-4">
                  ستنتهي جلستك قريباً بسبب عدم النشاط. هل تريد الاستمرار؟
                </p>
                
                {/* Countdown */}
                <div className="flex items-center justify-center p-4 bg-yellow-50 rounded-lg">
                  <ClockIcon className="w-6 h-6 text-yellow-600 ml-2" />
                  <span className="text-2xl font-bold text-yellow-800">
                    {formatTime(countdown)}
                  </span>
                </div>
                
                <p className="text-sm text-gray-500 text-center mt-2">
                  الوقت المتبقي قبل تسجيل الخروج التلقائي
                </p>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 space-x-reverse">
                <button
                  onClick={extendSession}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <ArrowPathIcon className="w-5 h-5 ml-2" />
                  تمديد الجلسة
                </button>
                
                <button
                  onClick={logout}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="w-5 h-5 ml-2" />
                  تسجيل الخروج
                </button>
              </div>

              {/* Auto-logout warning */}
              <div className="mt-4 p-3 bg-red-50 rounded-lg">
                <p className="text-sm text-red-800 text-center">
                  <strong>تنبيه:</strong> سيتم تسجيل خروجك تلقائياً عند انتهاء الوقت
                </p>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SessionWarning;
