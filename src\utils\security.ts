// Security utilities for ALaa Academy Platform

export interface SecurityContext {
  userId: string;
  userType: 'student' | 'admin';
  permissions: string[];
}

// Input validation utilities
export const validateInput = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  accessCode: (code: string): boolean => {
    // Access code should be 6-8 characters, alphanumeric
    const codeRegex = /^[A-Za-z0-9]{6,8}$/;
    return codeRegex.test(code);
  },

  password: (password: string): boolean => {
    // Password should be at least 8 characters with at least one letter and one number
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  },

  uuid: (id: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  },

  name: (name: string): boolean => {
    // Name should be 2-50 characters, letters and spaces only
    const nameRegex = /^[\u0600-\u06FFa-zA-Z\s]{2,50}$/;
    return nameRegex.test(name.trim());
  },

  courseTitle: (title: string): boolean => {
    // Course title should be 3-100 characters
    return title.trim().length >= 3 && title.trim().length <= 100;
  },

  url: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
};

// Data sanitization utilities
export const sanitizeInput = {
  text: (input: string): string => {
    return input.trim().replace(/[<>]/g, '');
  },

  html: (input: string): string => {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  },

  sql: (input: string): string => {
    // Basic SQL injection prevention
    return input.replace(/['";\\]/g, '');
  }
};

// Permission checking utilities
export const checkPermissions = {
  canViewStudent: (context: SecurityContext, studentId: string): boolean => {
    return context.userType === 'admin' || context.userId === studentId;
  },

  canEditStudent: (context: SecurityContext, studentId: string): boolean => {
    return context.userType === 'admin' || context.userId === studentId;
  },

  canManageCourses: (context: SecurityContext): boolean => {
    return context.userType === 'admin';
  },

  canViewCourse: (context: SecurityContext, courseId: string, isEnrolled: boolean): boolean => {
    return context.userType === 'admin' || isEnrolled;
  },

  canManageVideos: (context: SecurityContext): boolean => {
    return context.userType === 'admin';
  },

  canViewVideo: (context: SecurityContext, videoId: string, isEnrolled: boolean, isFree: boolean): boolean => {
    return context.userType === 'admin' || isEnrolled || isFree;
  },

  canManageQuizzes: (context: SecurityContext): boolean => {
    return context.userType === 'admin';
  },

  canTakeQuiz: (context: SecurityContext, quizId: string, isEnrolled: boolean): boolean => {
    return context.userType === 'admin' || isEnrolled;
  },

  canViewCertificate: (context: SecurityContext, certificateStudentId: string): boolean => {
    return context.userType === 'admin' || context.userId === certificateStudentId;
  },

  canIssueCertificate: (context: SecurityContext): boolean => {
    return context.userType === 'admin';
  }
};

// Rate limiting utilities
class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();

  isAllowed(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const record = this.attempts.get(key);

    if (!record || now > record.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (record.count >= maxAttempts) {
      return false;
    }

    record.count++;
    return true;
  }

  reset(key: string): void {
    this.attempts.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Security headers and CSRF protection
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
};

// CSRF token generation and validation
export const csrf = {
  generateToken: (): string => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  },

  validateToken: (token: string, storedToken: string): boolean => {
    return token === storedToken;
  }
};

// Secure session management
export const sessionSecurity = {
  generateSessionId: (): string => {
    return crypto.getRandomValues(new Uint8Array(32)).reduce((acc, byte) => 
      acc + byte.toString(16).padStart(2, '0'), ''
    );
  },

  isSessionExpired: (lastActivity: string, timeoutMs: number): boolean => {
    const now = new Date().getTime();
    const lastActivityTime = new Date(lastActivity).getTime();
    return (now - lastActivityTime) > timeoutMs;
  },

  hashPassword: async (password: string): Promise<string> => {
    // In a real implementation, use bcrypt or similar
    // This is a simplified version for demonstration
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  },

  verifyPassword: async (password: string, hash: string): Promise<boolean> => {
    const passwordHash = await sessionSecurity.hashPassword(password);
    return passwordHash === hash;
  }
};

// Audit logging
export interface AuditLog {
  userId: string;
  userType: 'student' | 'admin';
  action: string;
  resource: string;
  resourceId?: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  details?: any;
}

export const auditLogger = {
  log: (entry: AuditLog): void => {
    // In a real implementation, this would send to a logging service
    console.log('AUDIT:', JSON.stringify(entry));
    
    // Store in localStorage for demonstration (in production, use proper logging)
    const logs = JSON.parse(localStorage.getItem('audit_logs') || '[]');
    logs.push(entry);
    
    // Keep only last 100 logs
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem('audit_logs', JSON.stringify(logs));
  },

  getLogs: (): AuditLog[] => {
    return JSON.parse(localStorage.getItem('audit_logs') || '[]');
  },

  clearLogs: (): void => {
    localStorage.removeItem('audit_logs');
  }
};

// Error handling and security
export const secureError = {
  sanitizeError: (error: any): string => {
    // Don't expose sensitive information in error messages
    if (typeof error === 'string') {
      return error.includes('password') || error.includes('token') ? 'Authentication error' : error;
    }
    
    if (error?.message) {
      return error.message.includes('password') || error.message.includes('token') 
        ? 'Authentication error' 
        : error.message;
    }
    
    return 'An unexpected error occurred';
  },

  logSecurityEvent: (event: string, details: any): void => {
    auditLogger.log({
      userId: 'system',
      userType: 'admin',
      action: 'security_event',
      resource: 'system',
      timestamp: new Date().toISOString(),
      success: false,
      details: { event, ...details }
    });
  }
};

// Content Security Policy helpers
export const csp = {
  generateNonce: (): string => {
    return btoa(String.fromCharCode(...crypto.getRandomValues(new Uint8Array(16))));
  },

  buildPolicy: (nonce: string): string => {
    return [
      "default-src 'self'",
      `script-src 'self' 'nonce-${nonce}'`,
      `style-src 'self' 'nonce-${nonce}' 'unsafe-inline'`,
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https://srnyumtbsyxiqkvwkcpi.supabase.co",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
  }
};

export default {
  validateInput,
  sanitizeInput,
  checkPermissions,
  rateLimiter,
  securityHeaders,
  csrf,
  sessionSecurity,
  auditLogger,
  secureError,
  csp
};
