import { Course, Video, Quiz, Certificate } from '../types';

export const mockCourses: Course[] = [
  {
    id: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',
    title: 'أساسيات البرمجة بـ JavaScript',
    description: 'تعلم أساسيات البرمجة باستخدام لغة JavaScript من الصفر حتى الاحتراف',
    categoryId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    thumbnailUrl: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',
    price: 299,
    duration: 25,
    level: 'beginner',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 15,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '41c61139-b980-4f5d-8036-c006b1ed6bc2',
    title: 'تطوير المواقع بـ React',
    description: 'كورس شامل لتعلم تطوير المواقع الحديثة باستخدام مكتبة React',
    categoryId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    thumbnailUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
    price: 499,
    duration: 40,
    level: 'intermediate',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 12,
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '3de6741f-0444-439e-a0f2-5f169c4d1970',
    title: 'تطوير تطبيقات الهاتف بـ React Native',
    description: 'تعلم تطوير تطبيقات الهاتف المحمول لنظامي iOS و Android',
    categoryId: 'b2c3d4e5-f6a7-8901-bcde-f23456789012',
    instructorId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    thumbnailUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    price: 699,
    duration: 50,
    level: 'intermediate',
    videos: [],
    pdfs: [],
    quizzes: [],
    enrolledStudents: 8,
    isActive: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01')
  }
];

export const mockVideos: Video[] = [
  {
    id: 'c0628c50-5bc5-4d9e-adce-63e135037949',
    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',
    title: 'مقدمة في JavaScript',
    videoUrl: 'https://example.com/video1.mp4',
    duration: 15,
    orderIndex: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'd1739d61-6cd6-5e0f-bede-74e246148a5a',
    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',
    title: 'المتغيرات والثوابت',
    videoUrl: 'https://example.com/video2.mp4',
    duration: 20,
    orderIndex: 2,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockQuizzes: Quiz[] = [
  {
    id: '5d3d6ffe-45ea-424c-87b5-328e5a191bae',
    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',
    title: 'اختبار JavaScript الأساسي',
    description: 'اختبار لقياس فهمك لأساسيات JavaScript',
    questions: [
      {
        id: 'e2f4a6b8-c9d1-4e3f-a5b7-c8d9e0f1a2b3',
        question: 'ما هو JavaScript؟',
        type: 'multiple-choice',
        options: ['لغة برمجة', 'قاعدة بيانات', 'نظام تشغيل', 'متصفح'],
        correctAnswer: 0,
        points: 1
      }
    ],
    passingScore: 70,
    timeLimit: 30,
    attempts: 3,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export const mockCertificates: Certificate[] = [
  {
    id: 'f3a5b7c9-d1e3-4f5a-b7c9-d1e3f5a7b9c1',
    studentId: 'b4d6f8a0-c2e4-4f6a-b8c0-e2f4a6b8c0d2',
    courseId: '138ec959-163c-4ce9-9ad3-2b0597bdcfea',
    templateUrl: '/templates/cert-template.pdf',
    certificateUrl: '/certificates/cert-001.pdf',
    issuedAt: new Date('2024-01-20'),
    verificationCode: 'CERT-2024-001'
  }
];
