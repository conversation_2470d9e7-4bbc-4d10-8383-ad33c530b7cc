{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{UserPlusIcon,AcademicCapIcon,CheckCircleIcon,XCircleIcon,MagnifyingGlassIcon,UserIcon,BookOpenIcon,CalendarIcon,ClockIcon}from'@heroicons/react/24/outline';import{supabaseService}from'../../services/supabaseService';import{supabase}from'../../config/supabase';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CourseEnrollmentManagement=()=>{const[students,setStudents]=useState([]);const[courses,setCourses]=useState([]);const[enrollments,setEnrollments]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[selectedStudent,setSelectedStudent]=useState('');const[selectedCourse,setSelectedCourse]=useState('');const[showEnrollModal,setShowEnrollModal]=useState(false);useEffect(()=>{loadData();},[]);const loadData=async()=>{try{setLoading(true);const[studentsData,coursesData,enrollmentsData]=await Promise.all([supabaseService.getAllStudents(),supabaseService.getAllCourses(),loadAllEnrollments()]);setStudents(studentsData||[]);setCourses(coursesData||[]);setEnrollments(enrollmentsData||[]);}catch(error){console.error('Error loading data:',error);toast.error('فشل في تحميل البيانات');}finally{setLoading(false);}};const loadAllEnrollments=async()=>{try{const{data,error}=await supabase.from('student_enrollments').select(`\n          *,\n          students (\n            id,\n            name,\n            access_code,\n            email,\n            is_active,\n            created_at\n          ),\n          courses (\n            id,\n            title,\n            description,\n            thumbnail_url,\n            price,\n            duration_hours,\n            level,\n            is_active\n          )\n        `).order('enrolled_at',{ascending:false});if(error)throw error;return data;}catch(error){console.error('Error loading enrollments:',error);return[];}};const handleEnrollStudent=async()=>{if(!selectedStudent||!selectedCourse){toast.error('يرجى اختيار الطالب والكورس');return;}try{// Check if already enrolled\nconst existingEnrollment=enrollments.find(e=>e.student_id===selectedStudent&&e.course_id===selectedCourse);if(existingEnrollment){toast.error('الطالب مسجل بالفعل في هذا الكورس');return;}await supabaseService.enrollStudent(selectedStudent,selectedCourse);toast.success('تم تسجيل الطالب في الكورس بنجاح');// Reload data\nawait loadData();// Reset form\nsetSelectedStudent('');setSelectedCourse('');setShowEnrollModal(false);}catch(error){console.error('Error enrolling student:',error);toast.error('فشل في تسجيل الطالب');}};const handleUnenrollStudent=async enrollmentId=>{if(!window.confirm('هل أنت متأكد من إلغاء تسجيل الطالب؟'))return;try{const{error}=await supabase.from('student_enrollments').delete().eq('id',enrollmentId);if(error)throw error;toast.success('تم إلغاء تسجيل الطالب بنجاح');await loadData();}catch(error){console.error('Error unenrolling student:',error);toast.error('فشل في إلغاء التسجيل');}};const filteredEnrollments=enrollments.filter(enrollment=>{var _enrollment$students,_enrollment$students$,_enrollment$students2,_enrollment$students3,_enrollment$courses,_enrollment$courses$t;const searchLower=searchTerm.toLowerCase();return((_enrollment$students=enrollment.students)===null||_enrollment$students===void 0?void 0:(_enrollment$students$=_enrollment$students.name)===null||_enrollment$students$===void 0?void 0:_enrollment$students$.toLowerCase().includes(searchLower))||((_enrollment$students2=enrollment.students)===null||_enrollment$students2===void 0?void 0:(_enrollment$students3=_enrollment$students2.access_code)===null||_enrollment$students3===void 0?void 0:_enrollment$students3.toLowerCase().includes(searchLower))||((_enrollment$courses=enrollment.courses)===null||_enrollment$courses===void 0?void 0:(_enrollment$courses$t=_enrollment$courses.title)===null||_enrollment$courses$t===void 0?void 0:_enrollment$courses$t.toLowerCase().includes(searchLower));});const getProgressColor=progress=>{if(progress===0)return'text-gray-500';if(progress<50)return'text-yellow-500';if(progress<100)return'text-blue-500';return'text-green-500';};const getProgressBgColor=progress=>{if(progress===0)return'bg-gray-200';if(progress<50)return'bg-yellow-200';if(progress<100)return'bg-blue-200';return'bg-green-200';};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0641\\u064A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowEnrollModal(true),className:\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(UserPlusIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0637\\u0627\\u0644\\u0628 \\u062C\\u062F\\u064A\\u062F\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:students.length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-6 h-6 text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:courses.length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-6 h-6 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:enrollments.length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-orange-100 rounded-lg\",children:/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-6 h-6 text-orange-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0627\\u0644\\u0645\\u0643\\u062A\\u0645\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:enrollments.filter(e=>e.progress===100).length})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col sm:flex-row gap-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0637\\u0627\\u0644\\u0628 \\u0623\\u0648 \\u0643\\u0648\\u0631\\u0633...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm border overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredEnrollments.map(enrollment=>{var _enrollment$students4,_enrollment$students5,_enrollment$courses2,_enrollment$courses3,_enrollment$courses4;return/*#__PURE__*/_jsxs(motion.tr,{initial:{opacity:0},animate:{opacity:1},className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 h-10 w-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"h-6 w-6 text-primary-600\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:((_enrollment$students4=enrollment.students)===null||_enrollment$students4===void 0?void 0:_enrollment$students4.name)||'غير محدد'}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644: \",(_enrollment$students5=enrollment.students)===null||_enrollment$students5===void 0?void 0:_enrollment$students5.access_code]})]})]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:(_enrollment$courses2=enrollment.courses)===null||_enrollment$courses2===void 0?void 0:_enrollment$courses2.title}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[(_enrollment$courses3=enrollment.courses)===null||_enrollment$courses3===void 0?void 0:_enrollment$courses3.level,\" \\u2022 \",(_enrollment$courses4=enrollment.courses)===null||_enrollment$courses4===void 0?void 0:_enrollment$courses4.duration_hours,\" \\u0633\\u0627\\u0639\\u0629\"]})]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between mb-1\",children:/*#__PURE__*/_jsxs(\"span\",{className:`text-sm font-medium ${getProgressColor(enrollment.progress)}`,children:[enrollment.progress,\"%\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:`h-2 rounded-full ${getProgressBgColor(enrollment.progress)}`,style:{width:`${enrollment.progress}%`}})})]})})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CalendarIcon,{className:\"w-4 h-4 ml-1\"}),new Date(enrollment.enrolled_at).toLocaleDateString('ar-SA')]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:enrollment.progress===100?/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-4 h-4 ml-1\"}),\"\\u0645\\u0643\\u062A\\u0645\\u0644\"]}):enrollment.progress>0?/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 ml-1\"}),\"\\u0642\\u064A\\u062F \\u0627\\u0644\\u062A\\u0642\\u062F\\u0645\"]}):/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",children:\"\\u0644\\u0645 \\u064A\\u0628\\u062F\\u0623\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleUnenrollStudent(enrollment.id),className:\"text-red-600 hover:text-red-900 transition-colors\",children:/*#__PURE__*/_jsx(XCircleIcon,{className:\"w-5 h-5\"})})})]},enrollment.id);})})]})})}),showEnrollModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},className:\"bg-white rounded-lg p-6 w-full max-w-md\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0637\\u0627\\u0644\\u0628 \\u0641\\u064A \\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedStudent,onChange:e=>setSelectedStudent(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- \\u0627\\u062E\\u062A\\u0631 \\u0637\\u0627\\u0644\\u0628 --\"}),students.map(student=>/*#__PURE__*/_jsxs(\"option\",{value:student.id,children:[student.name,\" (\",student.access_code,\")\"]},student.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCourse,onChange:e=>setSelectedCourse(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- \\u0627\\u062E\\u062A\\u0631 \\u0643\\u0648\\u0631\\u0633 --\"}),courses.map(course=>/*#__PURE__*/_jsx(\"option\",{value:course.id,children:course.title},course.id))]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 space-x-reverse mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowEnrollModal(false),className:\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleEnrollStudent,className:\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:\"\\u062A\\u0633\\u062C\\u064A\\u0644\"})]})]})})]});};export default CourseEnrollmentManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "UserPlusIcon", "AcademicCapIcon", "CheckCircleIcon", "XCircleIcon", "MagnifyingGlassIcon", "UserIcon", "BookOpenIcon", "CalendarIcon", "ClockIcon", "supabaseService", "supabase", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "CourseEnrollmentManagement", "students", "setStudents", "courses", "setCourses", "enrollments", "setEnrollments", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedStudent", "setSelectedStudent", "selectedCourse", "setSelectedCourse", "showEnrollModal", "setShowEnrollModal", "loadData", "studentsData", "coursesData", "enrollmentsData", "Promise", "all", "getAllStudents", "getAllCourses", "loadAllEnrollments", "error", "console", "data", "from", "select", "order", "ascending", "handleEnrollStudent", "existingEnrollment", "find", "e", "student_id", "course_id", "enrollStudent", "success", "handleUnenrollStudent", "enrollmentId", "window", "confirm", "delete", "eq", "filteredEnrollments", "filter", "enrollment", "_enrollment$students", "_enrollment$students$", "_enrollment$students2", "_enrollment$students3", "_enrollment$courses", "_enrollment$courses$t", "searchLower", "toLowerCase", "name", "includes", "access_code", "title", "getProgressColor", "progress", "getProgressBgColor", "className", "children", "onClick", "length", "type", "placeholder", "value", "onChange", "target", "map", "_enrollment$students4", "_enrollment$students5", "_enrollment$courses2", "_enrollment$courses3", "_enrollment$courses4", "tr", "initial", "opacity", "animate", "level", "duration_hours", "style", "width", "Date", "enrolled_at", "toLocaleDateString", "id", "div", "scale", "student", "course"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/CourseEnrollmentManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserPlusIcon,\n  AcademicCapIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  BookOpenIcon,\n  CalendarIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\nimport { supabaseService } from '../../services/supabaseService';\nimport { supabase } from '../../config/supabase';\nimport { toast } from 'react-hot-toast';\n\ninterface Student {\n  id: string;\n  name: string;\n  access_code: string;\n  email?: string;\n  is_active: boolean;\n  created_at: string;\n}\n\ninterface Course {\n  id: string;\n  title: string;\n  description: string;\n  thumbnail_url?: string;\n  price: number;\n  duration_hours: number;\n  level: string;\n  is_active: boolean;\n}\n\ninterface Enrollment {\n  id: string;\n  student_id: string;\n  course_id: string;\n  enrolled_at: string;\n  progress: number;\n  completed_at?: string;\n  students: Student;\n  courses: Course;\n}\n\nconst CourseEnrollmentManagement: React.FC = () => {\n  const [students, setStudents] = useState<Student[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedStudent, setSelectedStudent] = useState<string>('');\n  const [selectedCourse, setSelectedCourse] = useState<string>('');\n  const [showEnrollModal, setShowEnrollModal] = useState(false);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [studentsData, coursesData, enrollmentsData] = await Promise.all([\n        supabaseService.getAllStudents(),\n        supabaseService.getAllCourses(),\n        loadAllEnrollments()\n      ]);\n\n      setStudents(studentsData || []);\n      setCourses(coursesData || []);\n      setEnrollments(enrollmentsData || []);\n    } catch (error) {\n      console.error('Error loading data:', error);\n      toast.error('فشل في تحميل البيانات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAllEnrollments = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('student_enrollments')\n        .select(`\n          *,\n          students (\n            id,\n            name,\n            access_code,\n            email,\n            is_active,\n            created_at\n          ),\n          courses (\n            id,\n            title,\n            description,\n            thumbnail_url,\n            price,\n            duration_hours,\n            level,\n            is_active\n          )\n        `)\n        .order('enrolled_at', { ascending: false });\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('Error loading enrollments:', error);\n      return [];\n    }\n  };\n\n  const handleEnrollStudent = async () => {\n    if (!selectedStudent || !selectedCourse) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n\n    try {\n      // Check if already enrolled\n      const existingEnrollment = enrollments.find(\n        e => e.student_id === selectedStudent && e.course_id === selectedCourse\n      );\n\n      if (existingEnrollment) {\n        toast.error('الطالب مسجل بالفعل في هذا الكورس');\n        return;\n      }\n\n      await supabaseService.enrollStudent(selectedStudent, selectedCourse);\n      toast.success('تم تسجيل الطالب في الكورس بنجاح');\n      \n      // Reload data\n      await loadData();\n      \n      // Reset form\n      setSelectedStudent('');\n      setSelectedCourse('');\n      setShowEnrollModal(false);\n    } catch (error) {\n      console.error('Error enrolling student:', error);\n      toast.error('فشل في تسجيل الطالب');\n    }\n  };\n\n  const handleUnenrollStudent = async (enrollmentId: string) => {\n    if (!window.confirm('هل أنت متأكد من إلغاء تسجيل الطالب؟')) return;\n\n    try {\n      const { error } = await supabase\n        .from('student_enrollments')\n        .delete()\n        .eq('id', enrollmentId);\n\n      if (error) throw error;\n\n      toast.success('تم إلغاء تسجيل الطالب بنجاح');\n      await loadData();\n    } catch (error) {\n      console.error('Error unenrolling student:', error);\n      toast.error('فشل في إلغاء التسجيل');\n    }\n  };\n\n  const filteredEnrollments = enrollments.filter(enrollment => {\n    const searchLower = searchTerm.toLowerCase();\n    return (\n      enrollment.students?.name?.toLowerCase().includes(searchLower) ||\n      enrollment.students?.access_code?.toLowerCase().includes(searchLower) ||\n      enrollment.courses?.title?.toLowerCase().includes(searchLower)\n    );\n  });\n\n  const getProgressColor = (progress: number) => {\n    if (progress === 0) return 'text-gray-500';\n    if (progress < 50) return 'text-yellow-500';\n    if (progress < 100) return 'text-blue-500';\n    return 'text-green-500';\n  };\n\n  const getProgressBgColor = (progress: number) => {\n    if (progress === 0) return 'bg-gray-200';\n    if (progress < 50) return 'bg-yellow-200';\n    if (progress < 100) return 'bg-blue-200';\n    return 'bg-green-200';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إدارة تسجيل الطلاب</h1>\n          <p className=\"text-gray-600 mt-1\">تفعيل وإدارة تسجيل الطلاب في الكورسات</p>\n        </div>\n        <button\n          onClick={() => setShowEnrollModal(true)}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <UserPlusIcon className=\"w-5 h-5 ml-2\" />\n          تسجيل طالب جديد\n        </button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <UserIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">إجمالي الطلاب</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{students.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <BookOpenIcon className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">الكورسات المتاحة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{courses.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <AcademicCapIcon className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">إجمالي التسجيلات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{enrollments.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-orange-100 rounded-lg\">\n              <CheckCircleIcon className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">الكورسات المكتملة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {enrollments.filter(e => e.progress === 100).length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <MagnifyingGlassIcon className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"البحث عن طالب أو كورس...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enrollments Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورس\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  التقدم\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ التسجيل\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الحالة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredEnrollments.map((enrollment) => (\n                <motion.tr\n                  key={enrollment.id}\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  className=\"hover:bg-gray-50\"\n                >\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\">\n                          <UserIcon className=\"h-6 w-6 text-primary-600\" />\n                        </div>\n                      </div>\n                      <div className=\"mr-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {enrollment.students?.name || 'غير محدد'}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          كود الوصول: {enrollment.students?.access_code}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {enrollment.courses?.title}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {enrollment.courses?.level} • {enrollment.courses?.duration_hours} ساعة\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <span className={`text-sm font-medium ${getProgressColor(enrollment.progress)}`}>\n                            {enrollment.progress}%\n                          </span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className={`h-2 rounded-full ${getProgressBgColor(enrollment.progress)}`}\n                            style={{ width: `${enrollment.progress}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    <div className=\"flex items-center\">\n                      <CalendarIcon className=\"w-4 h-4 ml-1\" />\n                      {new Date(enrollment.enrolled_at).toLocaleDateString('ar-SA')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {enrollment.progress === 100 ? (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                        <CheckCircleIcon className=\"w-4 h-4 ml-1\" />\n                        مكتمل\n                      </span>\n                    ) : enrollment.progress > 0 ? (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        <ClockIcon className=\"w-4 h-4 ml-1\" />\n                        قيد التقدم\n                      </span>\n                    ) : (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                        لم يبدأ\n                      </span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => handleUnenrollStudent(enrollment.id)}\n                      className=\"text-red-600 hover:text-red-900 transition-colors\"\n                    >\n                      <XCircleIcon className=\"w-5 h-5\" />\n                    </button>\n                  </td>\n                </motion.tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Enroll Student Modal */}\n      {showEnrollModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg p-6 w-full max-w-md\"\n          >\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">تسجيل طالب في كورس</h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  اختر الطالب\n                </label>\n                <select\n                  value={selectedStudent}\n                  onChange={(e) => setSelectedStudent(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value=\"\">-- اختر طالب --</option>\n                  {students.map((student) => (\n                    <option key={student.id} value={student.id}>\n                      {student.name} ({student.access_code})\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  اختر الكورس\n                </label>\n                <select\n                  value={selectedCourse}\n                  onChange={(e) => setSelectedCourse(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value=\"\">-- اختر كورس --</option>\n                  {courses.map((course) => (\n                    <option key={course.id} value={course.id}>\n                      {course.title}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 space-x-reverse mt-6\">\n              <button\n                onClick={() => setShowEnrollModal(false)}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                إلغاء\n              </button>\n              <button\n                onClick={handleEnrollStudent}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                تسجيل\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CourseEnrollmentManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,YAAY,CACZC,eAAe,CACfC,eAAe,CACfC,WAAW,CACXC,mBAAmB,CACnBC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,SAAS,KACJ,6BAA6B,CACpC,OAASC,eAAe,KAAQ,gCAAgC,CAChE,OAASC,QAAQ,KAAQ,uBAAuB,CAChD,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiCxC,KAAM,CAAAC,0BAAoC,CAAGA,CAAA,GAAM,CACjD,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAACwB,WAAW,CAAEC,cAAc,CAAC,CAAGzB,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8B,eAAe,CAAEC,kBAAkB,CAAC,CAAG/B,QAAQ,CAAS,EAAE,CAAC,CAClE,KAAM,CAACgC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjC,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAE7DC,SAAS,CAAC,IAAM,CACdmC,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACFT,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAACU,YAAY,CAAEC,WAAW,CAAEC,eAAe,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACrE7B,eAAe,CAAC8B,cAAc,CAAC,CAAC,CAChC9B,eAAe,CAAC+B,aAAa,CAAC,CAAC,CAC/BC,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAEFvB,WAAW,CAACgB,YAAY,EAAI,EAAE,CAAC,CAC/Bd,UAAU,CAACe,WAAW,EAAI,EAAE,CAAC,CAC7Bb,cAAc,CAACc,eAAe,EAAI,EAAE,CAAC,CACvC,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C/B,KAAK,CAAC+B,KAAK,CAAC,uBAAuB,CAAC,CACtC,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAEG,IAAI,CAAEF,KAAM,CAAC,CAAG,KAAM,CAAAhC,QAAQ,CACnCmC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,aAAa,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE7C,GAAIN,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAE,IAAI,CACb,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,EAAE,CACX,CACF,CAAC,CAED,KAAM,CAAAO,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAACtB,eAAe,EAAI,CAACE,cAAc,CAAE,CACvClB,KAAK,CAAC+B,KAAK,CAAC,4BAA4B,CAAC,CACzC,OACF,CAEA,GAAI,CACF;AACA,KAAM,CAAAQ,kBAAkB,CAAG7B,WAAW,CAAC8B,IAAI,CACzCC,CAAC,EAAIA,CAAC,CAACC,UAAU,GAAK1B,eAAe,EAAIyB,CAAC,CAACE,SAAS,GAAKzB,cAC3D,CAAC,CAED,GAAIqB,kBAAkB,CAAE,CACtBvC,KAAK,CAAC+B,KAAK,CAAC,kCAAkC,CAAC,CAC/C,OACF,CAEA,KAAM,CAAAjC,eAAe,CAAC8C,aAAa,CAAC5B,eAAe,CAAEE,cAAc,CAAC,CACpElB,KAAK,CAAC6C,OAAO,CAAC,iCAAiC,CAAC,CAEhD;AACA,KAAM,CAAAvB,QAAQ,CAAC,CAAC,CAEhB;AACAL,kBAAkB,CAAC,EAAE,CAAC,CACtBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAE,MAAOU,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD/B,KAAK,CAAC+B,KAAK,CAAC,qBAAqB,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAAe,qBAAqB,CAAG,KAAO,CAAAC,YAAoB,EAAK,CAC5D,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,qCAAqC,CAAC,CAAE,OAE5D,GAAI,CACF,KAAM,CAAElB,KAAM,CAAC,CAAG,KAAM,CAAAhC,QAAQ,CAC7BmC,IAAI,CAAC,qBAAqB,CAAC,CAC3BgB,MAAM,CAAC,CAAC,CACRC,EAAE,CAAC,IAAI,CAAEJ,YAAY,CAAC,CAEzB,GAAIhB,KAAK,CAAE,KAAM,CAAAA,KAAK,CAEtB/B,KAAK,CAAC6C,OAAO,CAAC,6BAA6B,CAAC,CAC5C,KAAM,CAAAvB,QAAQ,CAAC,CAAC,CAClB,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD/B,KAAK,CAAC+B,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAqB,mBAAmB,CAAG1C,WAAW,CAAC2C,MAAM,CAACC,UAAU,EAAI,KAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAC3D,KAAM,CAAAC,WAAW,CAAG/C,UAAU,CAACgD,WAAW,CAAC,CAAC,CAC5C,MACE,EAAAP,oBAAA,CAAAD,UAAU,CAAChD,QAAQ,UAAAiD,oBAAA,kBAAAC,qBAAA,CAAnBD,oBAAA,CAAqBQ,IAAI,UAAAP,qBAAA,iBAAzBA,qBAAA,CAA2BM,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,KAAAJ,qBAAA,CAC9DH,UAAU,CAAChD,QAAQ,UAAAmD,qBAAA,kBAAAC,qBAAA,CAAnBD,qBAAA,CAAqBQ,WAAW,UAAAP,qBAAA,iBAAhCA,qBAAA,CAAkCI,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,KAAAF,mBAAA,CACrEL,UAAU,CAAC9C,OAAO,UAAAmD,mBAAA,kBAAAC,qBAAA,CAAlBD,mBAAA,CAAoBO,KAAK,UAAAN,qBAAA,iBAAzBA,qBAAA,CAA2BE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,EAElE,CAAC,CAAC,CAEF,KAAM,CAAAM,gBAAgB,CAAIC,QAAgB,EAAK,CAC7C,GAAIA,QAAQ,GAAK,CAAC,CAAE,MAAO,eAAe,CAC1C,GAAIA,QAAQ,CAAG,EAAE,CAAE,MAAO,iBAAiB,CAC3C,GAAIA,QAAQ,CAAG,GAAG,CAAE,MAAO,eAAe,CAC1C,MAAO,gBAAgB,CACzB,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAID,QAAgB,EAAK,CAC/C,GAAIA,QAAQ,GAAK,CAAC,CAAE,MAAO,aAAa,CACxC,GAAIA,QAAQ,CAAG,EAAE,CAAE,MAAO,eAAe,CACzC,GAAIA,QAAQ,CAAG,GAAG,CAAE,MAAO,aAAa,CACxC,MAAO,cAAc,CACvB,CAAC,CAED,GAAIxD,OAAO,CAAE,CACX,mBACEV,IAAA,QAAKoE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDrE,IAAA,QAAKoE,SAAS,CAAC,mEAAmE,CAAM,CAAC,CACtF,CAAC,CAEV,CAEA,mBACElE,KAAA,QAAKkE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBnE,KAAA,QAAKkE,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFnE,KAAA,QAAAmE,QAAA,eACErE,IAAA,OAAIoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,oGAAkB,CAAI,CAAC,cACxErE,IAAA,MAAGoE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,uMAAqC,CAAG,CAAC,EACxE,CAAC,cACNnE,KAAA,WACEoE,OAAO,CAAEA,CAAA,GAAMnD,kBAAkB,CAAC,IAAI,CAAE,CACxCiD,SAAS,CAAC,gHAAgH,CAAAC,QAAA,eAE1HrE,IAAA,CAACb,YAAY,EAACiF,SAAS,CAAC,cAAc,CAAE,CAAC,mFAE3C,EAAQ,CAAC,EACN,CAAC,cAGNlE,KAAA,QAAKkE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDrE,IAAA,QAAKoE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,QAAKoE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCrE,IAAA,CAACR,QAAQ,EAAC4E,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,cACNlE,KAAA,QAAKkE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrE,IAAA,MAAGoE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2EAAa,CAAG,CAAC,cACtDrE,IAAA,MAAGoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEjE,QAAQ,CAACmE,MAAM,CAAI,CAAC,EAClE,CAAC,EACH,CAAC,CACH,CAAC,cAENvE,IAAA,QAAKoE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,QAAKoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CrE,IAAA,CAACP,YAAY,EAAC2E,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAChD,CAAC,cACNlE,KAAA,QAAKkE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrE,IAAA,MAAGoE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6FAAgB,CAAG,CAAC,cACzDrE,IAAA,MAAGoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAE/D,OAAO,CAACiE,MAAM,CAAI,CAAC,EACjE,CAAC,EACH,CAAC,CACH,CAAC,cAENvE,IAAA,QAAKoE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,QAAKoE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CrE,IAAA,CAACZ,eAAe,EAACgF,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACpD,CAAC,cACNlE,KAAA,QAAKkE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrE,IAAA,MAAGoE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6FAAgB,CAAG,CAAC,cACzDrE,IAAA,MAAGoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAE7D,WAAW,CAAC+D,MAAM,CAAI,CAAC,EACrE,CAAC,EACH,CAAC,CACH,CAAC,cAENvE,IAAA,QAAKoE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,QAAKoE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CrE,IAAA,CAACX,eAAe,EAAC+E,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACpD,CAAC,cACNlE,KAAA,QAAKkE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrE,IAAA,MAAGoE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mGAAiB,CAAG,CAAC,cAC1DrE,IAAA,MAAGoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5C7D,WAAW,CAAC2C,MAAM,CAACZ,CAAC,EAAIA,CAAC,CAAC2B,QAAQ,GAAK,GAAG,CAAC,CAACK,MAAM,CAClD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNvE,IAAA,QAAKoE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrE,IAAA,QAAKoE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAC9CrE,IAAA,QAAKoE,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBnE,KAAA,QAAKkE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrE,IAAA,CAACT,mBAAmB,EAAC6E,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5GpE,IAAA,UACEwE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,+GAA0B,CACtCC,KAAK,CAAE9D,UAAW,CAClB+D,QAAQ,CAAGpC,CAAC,EAAK1B,aAAa,CAAC0B,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE,CAC/CN,SAAS,CAAC,uHAAuH,CAClI,CAAC,EACC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,cAGNpE,IAAA,QAAKoE,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnErE,IAAA,QAAKoE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BnE,KAAA,UAAOkE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDrE,IAAA,UAAOoE,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BnE,KAAA,OAAAmE,QAAA,eACErE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,2EAEhG,CAAI,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,wDAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRrE,IAAA,UAAOoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDnB,mBAAmB,CAAC2B,GAAG,CAAEzB,UAAU,OAAA0B,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,oBAClChF,KAAA,CAAChB,MAAM,CAACiG,EAAE,EAERC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBjB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5BrE,IAAA,OAAIoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,QAAKoE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCrE,IAAA,QAAKoE,SAAS,CAAC,wEAAwE,CAAAC,QAAA,cACrFrE,IAAA,CAACR,QAAQ,EAAC4E,SAAS,CAAC,0BAA0B,CAAE,CAAC,CAC9C,CAAC,CACH,CAAC,cACNlE,KAAA,QAAKkE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrE,IAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C,EAAAS,qBAAA,CAAA1B,UAAU,CAAChD,QAAQ,UAAA0E,qBAAA,iBAAnBA,qBAAA,CAAqBjB,IAAI,GAAI,UAAU,CACrC,CAAC,cACN3D,KAAA,QAAKkE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,2DACzB,EAAAU,qBAAA,CAAC3B,UAAU,CAAChD,QAAQ,UAAA2E,qBAAA,iBAAnBA,qBAAA,CAAqBhB,WAAW,EAC1C,CAAC,EACH,CAAC,EACH,CAAC,CACJ,CAAC,cACL7D,KAAA,OAAIkE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBrE,IAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAAW,oBAAA,CAC/C5B,UAAU,CAAC9C,OAAO,UAAA0E,oBAAA,iBAAlBA,oBAAA,CAAoBhB,KAAK,CACvB,CAAC,cACN9D,KAAA,QAAKkE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,GAAAY,oBAAA,CACnC7B,UAAU,CAAC9C,OAAO,UAAA2E,oBAAA,iBAAlBA,oBAAA,CAAoBM,KAAK,CAAC,UAAG,EAAAL,oBAAA,CAAC9B,UAAU,CAAC9C,OAAO,UAAA4E,oBAAA,iBAAlBA,oBAAA,CAAoBM,cAAc,CAAC,2BACpE,EAAK,CAAC,EACJ,CAAC,cACLxF,IAAA,OAAIoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCrE,IAAA,QAAKoE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCnE,KAAA,QAAKkE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBrE,IAAA,QAAKoE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDnE,KAAA,SAAMkE,SAAS,CAAE,uBAAuBH,gBAAgB,CAACb,UAAU,CAACc,QAAQ,CAAC,EAAG,CAAAG,QAAA,EAC7EjB,UAAU,CAACc,QAAQ,CAAC,GACvB,EAAM,CAAC,CACJ,CAAC,cACNlE,IAAA,QAAKoE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDrE,IAAA,QACEoE,SAAS,CAAE,oBAAoBD,kBAAkB,CAACf,UAAU,CAACc,QAAQ,CAAC,EAAG,CACzEuB,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGtC,UAAU,CAACc,QAAQ,GAAI,CAAE,CACxC,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,CACJ,CAAC,cACLlE,IAAA,OAAIoE,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAC/DnE,KAAA,QAAKkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrE,IAAA,CAACN,YAAY,EAAC0E,SAAS,CAAC,cAAc,CAAE,CAAC,CACxC,GAAI,CAAAuB,IAAI,CAACvC,UAAU,CAACwC,WAAW,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,EAC1D,CAAC,CACJ,CAAC,cACL7F,IAAA,OAAIoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACxCjB,UAAU,CAACc,QAAQ,GAAK,GAAG,cAC1BhE,KAAA,SAAMkE,SAAS,CAAC,qGAAqG,CAAAC,QAAA,eACnHrE,IAAA,CAACX,eAAe,EAAC+E,SAAS,CAAC,cAAc,CAAE,CAAC,iCAE9C,EAAM,CAAC,CACLhB,UAAU,CAACc,QAAQ,CAAG,CAAC,cACzBhE,KAAA,SAAMkE,SAAS,CAAC,mGAAmG,CAAAC,QAAA,eACjHrE,IAAA,CAACL,SAAS,EAACyE,SAAS,CAAC,cAAc,CAAE,CAAC,0DAExC,EAAM,CAAC,cAEPpE,IAAA,SAAMoE,SAAS,CAAC,mGAAmG,CAAAC,QAAA,CAAC,uCAEpH,CAAM,CACP,CACC,CAAC,cACLrE,IAAA,OAAIoE,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7DrE,IAAA,WACEsE,OAAO,CAAEA,CAAA,GAAM1B,qBAAqB,CAACQ,UAAU,CAAC0C,EAAE,CAAE,CACpD1B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAE7DrE,IAAA,CAACV,WAAW,EAAC8E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC7B,CAAC,CACP,CAAC,GA7EAhB,UAAU,CAAC0C,EA8EP,CAAC,EACb,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACH,CAAC,CAGL5E,eAAe,eACdlB,IAAA,QAAKoE,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FnE,KAAA,CAAChB,MAAM,CAAC6G,GAAG,EACTX,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEW,KAAK,CAAE,IAAK,CAAE,CACrCV,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEW,KAAK,CAAE,CAAE,CAAE,CAClC5B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEnDrE,IAAA,OAAIoE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,+FAAkB,CAAI,CAAC,cAE9EnE,KAAA,QAAKkE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnE,KAAA,QAAAmE,QAAA,eACErE,IAAA,UAAOoE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRnE,KAAA,WACEwE,KAAK,CAAE5D,eAAgB,CACvB6D,QAAQ,CAAGpC,CAAC,EAAKxB,kBAAkB,CAACwB,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE,CACpDN,SAAS,CAAC,iHAAiH,CAAAC,QAAA,eAE3HrE,IAAA,WAAQ0E,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,yDAAe,CAAQ,CAAC,CACxCjE,QAAQ,CAACyE,GAAG,CAAEoB,OAAO,eACpB/F,KAAA,WAAyBwE,KAAK,CAAEuB,OAAO,CAACH,EAAG,CAAAzB,QAAA,EACxC4B,OAAO,CAACpC,IAAI,CAAC,IAAE,CAACoC,OAAO,CAAClC,WAAW,CAAC,GACvC,GAFakC,OAAO,CAACH,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN5F,KAAA,QAAAmE,QAAA,eACErE,IAAA,UAAOoE,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRnE,KAAA,WACEwE,KAAK,CAAE1D,cAAe,CACtB2D,QAAQ,CAAGpC,CAAC,EAAKtB,iBAAiB,CAACsB,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE,CACnDN,SAAS,CAAC,iHAAiH,CAAAC,QAAA,eAE3HrE,IAAA,WAAQ0E,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,yDAAe,CAAQ,CAAC,CACxC/D,OAAO,CAACuE,GAAG,CAAEqB,MAAM,eAClBlG,IAAA,WAAwB0E,KAAK,CAAEwB,MAAM,CAACJ,EAAG,CAAAzB,QAAA,CACtC6B,MAAM,CAAClC,KAAK,EADFkC,MAAM,CAACJ,EAEZ,CACT,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,cAEN5F,KAAA,QAAKkE,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DrE,IAAA,WACEsE,OAAO,CAAEA,CAAA,GAAMnD,kBAAkB,CAAC,KAAK,CAAE,CACzCiD,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,gCAED,CAAQ,CAAC,cACTrE,IAAA,WACEsE,OAAO,CAAElC,mBAAoB,CAC7BgC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,CAClG,gCAED,CAAQ,CAAC,EACN,CAAC,EACI,CAAC,CACV,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlE,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}