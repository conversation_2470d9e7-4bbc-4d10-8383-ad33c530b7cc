import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  LockClosedIcon,
  CheckCircleIcon,
  ClockIcon,
  BookOpenIcon,
  StarIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  EyeIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { supabaseService } from '../../services/supabaseService';
import { supabase } from '../../config/supabase';
import { toast } from 'react-hot-toast';
import LoadingSpinner from '../common/LoadingSpinner';

interface Video {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  video_url: string;
  duration: number;
  order_index: number;
  is_free: boolean;
  created_at: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail_url?: string;
  price: number;
  duration_hours: number;
  level: string;
  is_active: boolean;
}

interface VideoProgress {
  id: string;
  student_id?: string;
  user_id?: number;
  video_id: string;
  watched_duration: number;
  total_duration?: number;
  completed: boolean;
  completed_at?: string;
  last_watched_at?: string;
}

interface StudentEnrollment {
  id: string;
  student_id: string;
  course_id: string;
  progress: number;
  enrolled_at: string;
  completed_at?: string;
}

const ProgressiveCourseViewer: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  
  const [course, setCourse] = useState<Course | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [videoProgress, setVideoProgress] = useState<VideoProgress[]>([]);
  const [enrollment, setEnrollment] = useState<StudentEnrollment | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [currentStudent, setCurrentStudent] = useState<any>(null);

  useEffect(() => {
    // Get current student from localStorage
    const studentData = localStorage.getItem('currentStudent');
    if (studentData) {
      setCurrentStudent(JSON.parse(studentData));
    } else {
      navigate('/student/login');
      return;
    }

    if (courseId) {
      loadCourseData();
    }
  }, [courseId, navigate]);

  const loadCourseData = async () => {
    if (!courseId || !currentStudent) return;

    try {
      setLoading(true);
      
      // Load course details
      const courseData = await supabaseService.getCourseById(courseId);
      setCourse(courseData);

      // Load videos
      const videosData = await supabaseService.getVideosByCourseId(courseId);
      const sortedVideos = videosData?.sort((a, b) => a.order_index - b.order_index) || [];
      setVideos(sortedVideos);

      // Load student enrollment
      const enrollmentData = await loadStudentEnrollment();
      setEnrollment(enrollmentData);

      // Load video progress
      const progressData = await loadVideoProgress();
      setVideoProgress(progressData);

      // Set first available video
      if (sortedVideos.length > 0) {
        const firstAvailableVideo = getFirstAvailableVideo(sortedVideos, progressData);
        setSelectedVideo(firstAvailableVideo?.id || sortedVideos[0].id);
      }

    } catch (error) {
      console.error('Error loading course data:', error);
      toast.error('فشل في تحميل بيانات الكورس');
    } finally {
      setLoading(false);
    }
  };

  const loadStudentEnrollment = async () => {
    if (!courseId || !currentStudent) return null;

    try {
      const { data, error } = await supabase
        .from('student_enrollments')
        .select('*')
        .eq('student_id', currentStudent.id)
        .eq('course_id', courseId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error loading enrollment:', error);
      return null;
    }
  };

  const loadVideoProgress = async () => {
    if (!currentStudent) return [];

    try {
      const { data, error } = await supabase
        .from('video_progress')
        .select('*')
        .eq('student_id', currentStudent.id);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error loading video progress:', error);
      return [];
    }
  };

  const getFirstAvailableVideo = (videos: Video[], progress: VideoProgress[]) => {
    // Find the first video that is not completed
    for (const video of videos) {
      const videoProgress = progress.find(p => p.video_id === video.id);
      if (!videoProgress || !videoProgress.completed) {
        return video;
      }
    }
    // If all videos are completed, return the last one
    return videos[videos.length - 1];
  };

  const isVideoUnlocked = (video: Video, index: number) => {
    // First video is always unlocked
    if (index === 0) return true;
    
    // Free videos are always unlocked
    if (video.is_free) return true;

    // Check if previous video is completed
    const previousVideo = videos[index - 1];
    if (!previousVideo) return true;

    const previousProgress = videoProgress.find(p => p.video_id === previousVideo.id);
    return previousProgress?.completed || false;
  };

  const markVideoAsCompleted = async (videoId: string) => {
    if (!currentStudent) return;

    try {
      // Check if progress record exists
      const existingProgress = videoProgress.find(p => p.video_id === videoId);

      if (existingProgress) {
        // Update existing record
        const { error } = await supabase
          .from('video_progress')
          .update({
            completed: true,
            completed_at: new Date().toISOString(),
            watched_duration: videos.find(v => v.id === videoId)?.duration || 0
          })
          .eq('id', existingProgress.id);

        if (error) throw error;
      } else {
        // Create new progress record
        const { error } = await supabase
          .from('video_progress')
          .insert([{
            student_id: currentStudent.id,
            video_id: videoId,
            watched_duration: videos.find(v => v.id === videoId)?.duration || 0,
            completed: true,
            completed_at: new Date().toISOString()
          }]);

        if (error) throw error;
      }

      // Reload progress
      const updatedProgress = await loadVideoProgress();
      setVideoProgress(updatedProgress);

      // Update course progress
      await updateCourseProgress();

      toast.success('تم إكمال الفيديو بنجاح!');
    } catch (error) {
      console.error('Error marking video as completed:', error);
      toast.error('فشل في حفظ التقدم');
    }
  };

  const updateCourseProgress = async () => {
    if (!enrollment || !currentStudent) return;

    try {
      const completedVideos = videoProgress.filter(p => p.completed).length;
      const totalVideos = videos.length;
      const progressPercentage = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;

      const { error } = await supabase
        .from('student_enrollments')
        .update({
          progress: progressPercentage,
          completed_at: progressPercentage === 100 ? new Date().toISOString() : null
        })
        .eq('id', enrollment.id);

      if (error) throw error;

      // Update local enrollment state
      setEnrollment(prev => prev ? { ...prev, progress: progressPercentage } : null);
    } catch (error) {
      console.error('Error updating course progress:', error);
    }
  };

  const handleVideoSelect = (video: Video, index: number) => {
    if (!isVideoUnlocked(video, index)) {
      toast.error('يجب إكمال الفيديو السابق أولاً');
      return;
    }
    setSelectedVideo(video.id);
  };

  const getVideoProgress = (videoId: string) => {
    return videoProgress.find(p => p.video_id === videoId);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const currentVideo = videos.find(v => v.id === selectedVideo);
  const currentVideoIndex = videos.findIndex(v => v.id === selectedVideo);
  const completedVideosCount = videoProgress.filter(p => p.completed).length;
  const totalVideosCount = videos.length;
  const overallProgress = totalVideosCount > 0 ? Math.round((completedVideosCount / totalVideosCount) * 100) : 0;

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!course) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BookOpenIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">الكورس غير موجود</h3>
          <p className="text-gray-500">لم يتم العثور على الكورس المطلوب</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Course Header */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{course.title}</h1>
            <p className="text-gray-600 mb-4">{course.description}</p>
            
            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">التقدم الإجمالي</span>
                <span className="text-sm font-medium text-primary-600">{overallProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-primary-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${overallProgress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
              <div className="flex items-center justify-between mt-2 text-sm text-gray-500">
                <span>{completedVideosCount} من {totalVideosCount} فيديو مكتمل</span>
                {overallProgress === 100 && (
                  <span className="flex items-center text-green-600">
                    <TrophyIcon className="w-4 h-4 ml-1" />
                    مبروك! أكملت الكورس
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            {currentVideo ? (
              <>
                {/* Video */}
                <div className="aspect-video bg-black">
                  <video
                    key={currentVideo.id}
                    controls
                    className="w-full h-full"
                    src={currentVideo.video_url}
                    onEnded={() => markVideoAsCompleted(currentVideo.id)}
                  >
                    متصفحك لا يدعم تشغيل الفيديو
                  </video>
                </div>

                {/* Video Info */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h2 className="text-xl font-bold text-gray-900 mb-2">
                        {currentVideo.title}
                      </h2>
                      {currentVideo.description && (
                        <p className="text-gray-600 mb-4">{currentVideo.description}</p>
                      )}
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <span className="flex items-center">
                          <ClockIcon className="w-4 h-4 ml-1" />
                          {formatDuration(currentVideo.duration)}
                        </span>
                        <span className="flex items-center">
                          <EyeIcon className="w-4 h-4 ml-1" />
                          المرحلة {currentVideoIndex + 1}
                        </span>
                        {currentVideo.is_free && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            مجاني
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {getVideoProgress(currentVideo.id)?.completed && (
                      <div className="flex items-center text-green-600">
                        <CheckCircleIcon className="w-6 h-6 ml-2" />
                        <span className="text-sm font-medium">مكتمل</span>
                      </div>
                    )}
                  </div>

                  {/* Mark as Complete Button */}
                  {!getVideoProgress(currentVideo.id)?.completed && (
                    <button
                      onClick={() => markVideoAsCompleted(currentVideo.id)}
                      className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      تم إكمال الفيديو
                    </button>
                  )}
                </div>
              </>
            ) : (
              <div className="aspect-video flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <PlayIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">اختر فيديو لبدء المشاهدة</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Videos List */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b">
              <h3 className="text-lg font-medium text-gray-900">قائمة الفيديوهات</h3>
              <p className="text-sm text-gray-500 mt-1">
                {completedVideosCount} من {totalVideosCount} مكتمل
              </p>
            </div>
            
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {videos.map((video, index) => {
                const isUnlocked = isVideoUnlocked(video, index);
                const isCompleted = getVideoProgress(video.id)?.completed;
                const isSelected = selectedVideo === video.id;

                return (
                  <motion.div
                    key={video.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 cursor-pointer transition-colors ${
                      isSelected ? 'bg-primary-50 border-l-4 border-primary-600' : 'hover:bg-gray-50'
                    } ${!isUnlocked ? 'opacity-50' : ''}`}
                    onClick={() => handleVideoSelect(video, index)}
                  >
                    <div className="flex items-start space-x-3 space-x-reverse">
                      <div className="flex-shrink-0">
                        {isCompleted ? (
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircleIcon className="w-5 h-5 text-green-600" />
                          </div>
                        ) : isUnlocked ? (
                          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                            <PlayIcon className="w-5 h-5 text-primary-600" />
                          </div>
                        ) : (
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <LockClosedIcon className="w-5 h-5 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 space-x-reverse mb-1">
                          <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            #{index + 1}
                          </span>
                          {video.is_free && (
                            <StarIcon className="w-4 h-4 text-yellow-500" />
                          )}
                        </div>
                        <h4 className={`text-sm font-medium truncate ${
                          isSelected ? 'text-primary-900' : 'text-gray-900'
                        }`}>
                          {video.title}
                        </h4>
                        <div className="flex items-center space-x-2 space-x-reverse mt-1 text-xs text-gray-500">
                          <ClockIcon className="w-3 h-3" />
                          <span>{formatDuration(video.duration)}</span>
                          {!isUnlocked && (
                            <span className="text-red-500">مقفل</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressiveCourseViewer;
