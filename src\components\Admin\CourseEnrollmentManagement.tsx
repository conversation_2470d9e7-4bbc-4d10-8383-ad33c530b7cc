import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserPlusIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  UserIcon,
  BookOpenIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { supabaseService } from '../../services/supabaseService';
import { supabase } from '../../config/supabase';
import { toast } from 'react-hot-toast';

interface Student {
  id: string;
  name: string;
  access_code: string;
  email?: string;
  is_active: boolean;
  created_at: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail_url?: string;
  price: number;
  duration_hours: number;
  level: string;
  is_active: boolean;
}

interface Enrollment {
  id: string;
  student_id: string;
  course_id: string;
  enrolled_at: string;
  progress: number;
  completed_at?: string;
  students: Student;
  courses: Course;
}

const CourseEnrollmentManagement: React.FC = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [showEnrollModal, setShowEnrollModal] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsData, coursesData, enrollmentsData] = await Promise.all([
        supabaseService.getAllStudents(),
        supabaseService.getAllCourses(),
        loadAllEnrollments()
      ]);

      setStudents(studentsData || []);
      setCourses(coursesData || []);
      setEnrollments(enrollmentsData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('فشل في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const loadAllEnrollments = async () => {
    try {
      const { data, error } = await supabase
        .from('student_enrollments')
        .select(`
          *,
          students (
            id,
            name,
            access_code,
            email,
            is_active,
            created_at
          ),
          courses (
            id,
            title,
            description,
            thumbnail_url,
            price,
            duration_hours,
            level,
            is_active
          )
        `)
        .order('enrolled_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error loading enrollments:', error);
      return [];
    }
  };

  const handleEnrollStudent = async () => {
    if (!selectedStudent || !selectedCourse) {
      toast.error('يرجى اختيار الطالب والكورس');
      return;
    }

    try {
      // Check if already enrolled
      const existingEnrollment = enrollments.find(
        e => e.student_id === selectedStudent && e.course_id === selectedCourse
      );

      if (existingEnrollment) {
        toast.error('الطالب مسجل بالفعل في هذا الكورس');
        return;
      }

      await supabaseService.enrollStudent(selectedStudent, selectedCourse);
      toast.success('تم تسجيل الطالب في الكورس بنجاح');
      
      // Reload data
      await loadData();
      
      // Reset form
      setSelectedStudent('');
      setSelectedCourse('');
      setShowEnrollModal(false);
    } catch (error) {
      console.error('Error enrolling student:', error);
      toast.error('فشل في تسجيل الطالب');
    }
  };

  const handleUnenrollStudent = async (enrollmentId: string) => {
    if (!window.confirm('هل أنت متأكد من إلغاء تسجيل الطالب؟')) return;

    try {
      const { error } = await supabase
        .from('student_enrollments')
        .delete()
        .eq('id', enrollmentId);

      if (error) throw error;

      toast.success('تم إلغاء تسجيل الطالب بنجاح');
      await loadData();
    } catch (error) {
      console.error('Error unenrolling student:', error);
      toast.error('فشل في إلغاء التسجيل');
    }
  };

  const filteredEnrollments = enrollments.filter(enrollment => {
    const searchLower = searchTerm.toLowerCase();
    return (
      enrollment.students?.name?.toLowerCase().includes(searchLower) ||
      enrollment.students?.access_code?.toLowerCase().includes(searchLower) ||
      enrollment.courses?.title?.toLowerCase().includes(searchLower)
    );
  });

  const getProgressColor = (progress: number) => {
    if (progress === 0) return 'text-gray-500';
    if (progress < 50) return 'text-yellow-500';
    if (progress < 100) return 'text-blue-500';
    return 'text-green-500';
  };

  const getProgressBgColor = (progress: number) => {
    if (progress === 0) return 'bg-gray-200';
    if (progress < 50) return 'bg-yellow-200';
    if (progress < 100) return 'bg-blue-200';
    return 'bg-green-200';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة تسجيل الطلاب</h1>
          <p className="text-gray-600 mt-1">تفعيل وإدارة تسجيل الطلاب في الكورسات</p>
        </div>
        <button
          onClick={() => setShowEnrollModal(true)}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <UserPlusIcon className="w-5 h-5 ml-2" />
          تسجيل طالب جديد
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <UserIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">إجمالي الطلاب</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <BookOpenIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الكورسات المتاحة</p>
              <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <AcademicCapIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">إجمالي التسجيلات</p>
              <p className="text-2xl font-bold text-gray-900">{enrollments.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <CheckCircleIcon className="w-6 h-6 text-orange-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الكورسات المكتملة</p>
              <p className="text-2xl font-bold text-gray-900">
                {enrollments.filter(e => e.progress === 100).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg p-6 shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث عن طالب أو كورس..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Enrollments Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطالب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكورس
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التقدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ التسجيل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEnrollments.map((enrollment) => (
                <motion.tr
                  key={enrollment.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-primary-600" />
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">
                          {enrollment.students?.name || 'غير محدد'}
                        </div>
                        <div className="text-sm text-gray-500">
                          كود الوصول: {enrollment.students?.access_code}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {enrollment.courses?.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      {enrollment.courses?.level} • {enrollment.courses?.duration_hours} ساعة
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className={`text-sm font-medium ${getProgressColor(enrollment.progress)}`}>
                            {enrollment.progress}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${getProgressBgColor(enrollment.progress)}`}
                            style={{ width: `${enrollment.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarIcon className="w-4 h-4 ml-1" />
                      {new Date(enrollment.enrolled_at).toLocaleDateString('ar-SA')}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {enrollment.progress === 100 ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="w-4 h-4 ml-1" />
                        مكتمل
                      </span>
                    ) : enrollment.progress > 0 ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <ClockIcon className="w-4 h-4 ml-1" />
                        قيد التقدم
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        لم يبدأ
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleUnenrollStudent(enrollment.id)}
                      className="text-red-600 hover:text-red-900 transition-colors"
                    >
                      <XCircleIcon className="w-5 h-5" />
                    </button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Enroll Student Modal */}
      {showEnrollModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-md"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">تسجيل طالب في كورس</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اختر الطالب
                </label>
                <select
                  value={selectedStudent}
                  onChange={(e) => setSelectedStudent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">-- اختر طالب --</option>
                  {students.map((student) => (
                    <option key={student.id} value={student.id}>
                      {student.name} ({student.access_code})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اختر الكورس
                </label>
                <select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">-- اختر كورس --</option>
                  {courses.map((course) => (
                    <option key={course.id} value={course.id}>
                      {course.title}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-3 space-x-reverse mt-6">
              <button
                onClick={() => setShowEnrollModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleEnrollStudent}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                تسجيل
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default CourseEnrollmentManagement;
