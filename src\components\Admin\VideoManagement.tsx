import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  ClockIcon,
  FilmIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { supabaseService } from '../../services/supabaseService';
import { toast } from 'react-hot-toast';

interface Video {
  id: string;
  course_id: string;
  title: string;
  description?: string;
  video_url: string;
  duration: number;
  order_index: number;
  is_free: boolean;
  created_at: string;
  updated_at: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  is_active: boolean;
}

interface VideoFormData {
  title: string;
  description: string;
  video_url: string;
  duration: number;
  is_free: boolean;
}

const VideoManagement: React.FC = () => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingVideo, setEditingVideo] = useState<Video | null>(null);
  const [formData, setFormData] = useState<VideoFormData>({
    title: '',
    description: '',
    video_url: '',
    duration: 0,
    is_free: false
  });

  useEffect(() => {
    loadCourses();
  }, []);

  useEffect(() => {
    if (selectedCourse) {
      loadVideos();
    }
  }, [selectedCourse]);

  const loadCourses = async () => {
    try {
      const coursesData = await supabaseService.getAllCourses();
      setCourses(coursesData || []);
      if (coursesData && coursesData.length > 0) {
        setSelectedCourse(coursesData[0].id);
      }
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('فشل في تحميل الكورسات');
    }
  };

  const loadVideos = async () => {
    if (!selectedCourse) return;

    try {
      setLoading(true);
      const videosData = await supabaseService.getVideosByCourseId(selectedCourse);
      setVideos(videosData || []);
    } catch (error) {
      console.error('Error loading videos:', error);
      toast.error('فشل في تحميل الفيديوهات');
    } finally {
      setLoading(false);
    }
  };

  const handleAddVideo = async () => {
    if (!selectedCourse || !formData.title || !formData.video_url) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      const nextOrderIndex = Math.max(...videos.map(v => v.order_index), 0) + 1;
      
      await supabaseService.createVideo({
        courseId: selectedCourse,
        title: formData.title,
        description: formData.description,
        videoUrl: formData.video_url,
        duration: formData.duration,
        orderIndex: nextOrderIndex
      });

      toast.success('تم إضافة الفيديو بنجاح');
      await loadVideos();
      resetForm();
      setShowAddModal(false);
    } catch (error) {
      console.error('Error adding video:', error);
      toast.error('فشل في إضافة الفيديو');
    }
  };

  const handleEditVideo = async () => {
    if (!editingVideo || !formData.title || !formData.video_url) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      await supabaseService.updateVideo(editingVideo.id, {
        title: formData.title,
        description: formData.description,
        videoUrl: formData.video_url,
        duration: formData.duration
      });

      toast.success('تم تحديث الفيديو بنجاح');
      await loadVideos();
      resetForm();
      setShowEditModal(false);
      setEditingVideo(null);
    } catch (error) {
      console.error('Error updating video:', error);
      toast.error('فشل في تحديث الفيديو');
    }
  };

  const handleDeleteVideo = async (videoId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الفيديو؟')) return;

    try {
      await supabaseService.deleteVideo(videoId);
      toast.success('تم حذف الفيديو بنجاح');
      await loadVideos();
    } catch (error) {
      console.error('Error deleting video:', error);
      toast.error('فشل في حذف الفيديو');
    }
  };

  const handleMoveVideo = async (videoId: string, direction: 'up' | 'down') => {
    const currentVideo = videos.find(v => v.id === videoId);
    if (!currentVideo) return;

    const sortedVideos = [...videos].sort((a, b) => a.order_index - b.order_index);
    const currentIndex = sortedVideos.findIndex(v => v.id === videoId);
    
    if (direction === 'up' && currentIndex === 0) return;
    if (direction === 'down' && currentIndex === sortedVideos.length - 1) return;

    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const targetVideo = sortedVideos[targetIndex];

    try {
      // Swap order indices
      await Promise.all([
        supabaseService.updateVideo(currentVideo.id, { orderIndex: targetVideo.order_index }),
        supabaseService.updateVideo(targetVideo.id, { orderIndex: currentVideo.order_index })
      ]);

      toast.success('تم تحديث ترتيب الفيديو');
      await loadVideos();
    } catch (error) {
      console.error('Error moving video:', error);
      toast.error('فشل في تحديث الترتيب');
    }
  };

  const openEditModal = (video: Video) => {
    setEditingVideo(video);
    setFormData({
      title: video.title,
      description: video.description || '',
      video_url: video.video_url,
      duration: video.duration,
      is_free: video.is_free
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      video_url: '',
      duration: 0,
      is_free: false
    });
  };

  const filteredVideos = videos
    .filter(video => 
      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.description?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => a.order_index - b.order_index);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الفيديوهات</h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل وترتيب فيديوهات الكورسات</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          disabled={!selectedCourse}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PlusIcon className="w-5 h-5 ml-2" />
          إضافة فيديو جديد
        </button>
      </div>

      {/* Course Selection and Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            اختر الكورس
          </label>
          <select
            value={selectedCourse}
            onChange={(e) => setSelectedCourse(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">-- اختر كورس --</option>
            {courses.map((course) => (
              <option key={course.id} value={course.id}>
                {course.title}
              </option>
            ))}
          </select>
        </div>

        <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FilmIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-gray-600">إجمالي الفيديوهات</p>
                <p className="text-xl font-bold text-gray-900">{videos.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ClockIcon className="w-5 h-5 text-green-600" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-gray-600">إجمالي المدة</p>
                <p className="text-xl font-bold text-gray-900">
                  {formatDuration(videos.reduce((sum, v) => sum + v.duration, 0))}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <EyeIcon className="w-5 h-5 text-purple-600" />
              </div>
              <div className="mr-3">
                <p className="text-sm text-gray-600">فيديوهات مجانية</p>
                <p className="text-xl font-bold text-gray-900">
                  {videos.filter(v => v.is_free).length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      {selectedCourse && (
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="relative">
            <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في الفيديوهات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* Videos List */}
      {selectedCourse && (
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          {filteredVideos.length === 0 ? (
            <div className="text-center py-12">
              <FilmIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد فيديوهات</h3>
              <p className="text-gray-500 mb-4">ابدأ بإضافة فيديو جديد لهذا الكورس</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                <PlusIcon className="w-5 h-5 ml-2" />
                إضافة فيديو
              </button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredVideos.map((video, index) => (
                <motion.div
                  key={video.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-6 hover:bg-gray-50"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse flex-1">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          #{video.order_index}
                        </span>
                        <div className="p-2 bg-primary-100 rounded-lg">
                          <PlayIcon className="w-5 h-5 text-primary-600" />
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {video.title}
                          </h3>
                          {video.is_free && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              مجاني
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                          {video.description}
                        </p>
                        <div className="flex items-center space-x-4 space-x-reverse mt-2 text-sm text-gray-500">
                          <span className="flex items-center">
                            <ClockIcon className="w-4 h-4 ml-1" />
                            {formatDuration(video.duration)}
                          </span>
                          <span>
                            تم الإنشاء: {new Date(video.created_at).toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 space-x-reverse">
                      {/* Move buttons */}
                      <button
                        onClick={() => handleMoveVideo(video.id, 'up')}
                        disabled={index === 0}
                        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="تحريك لأعلى"
                      >
                        <ArrowUpIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleMoveVideo(video.id, 'down')}
                        disabled={index === filteredVideos.length - 1}
                        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="تحريك لأسفل"
                      >
                        <ArrowDownIcon className="w-4 h-4" />
                      </button>

                      {/* Action buttons */}
                      <button
                        onClick={() => openEditModal(video)}
                        className="p-2 text-blue-600 hover:text-blue-800"
                        title="تعديل"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteVideo(video.id)}
                        className="p-2 text-red-600 hover:text-red-800"
                        title="حذف"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Add Video Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">إضافة فيديو جديد</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الفيديو *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="أدخل عنوان الفيديو"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف الفيديو
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="أدخل وصف الفيديو"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رابط الفيديو *
                </label>
                <input
                  type="url"
                  value={formData.video_url}
                  onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="https://example.com/video.mp4"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مدة الفيديو (بالثواني)
                </label>
                <input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="0"
                  min="0"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_free"
                  checked={formData.is_free}
                  onChange={(e) => setFormData({ ...formData, is_free: e.target.checked })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="is_free" className="mr-2 block text-sm text-gray-900">
                  فيديو مجاني (يمكن مشاهدته بدون تسجيل)
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 space-x-reverse mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleAddVideo}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                إضافة الفيديو
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Edit Video Modal */}
      {showEditModal && editingVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">تعديل الفيديو</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الفيديو *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف الفيديو
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رابط الفيديو *
                </label>
                <input
                  type="url"
                  value={formData.video_url}
                  onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مدة الفيديو (بالثواني)
                </label>
                <input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  min="0"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 space-x-reverse mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingVideo(null);
                  resetForm();
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleEditVideo}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                حفظ التغييرات
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default VideoManagement;
