{"ast": null, "code": "import React,{useState,useEffect,createContext,useContext}from'react';import{motion,AnimatePresence}from'framer-motion';import{CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XCircleIcon,XMarkIcon,BellIcon,AcademicCapIcon,TrophyIcon,BookOpenIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const NotificationContext=/*#__PURE__*/createContext(undefined);export const useNotifications=()=>{const context=useContext(NotificationContext);if(!context){throw new Error('useNotifications must be used within a NotificationProvider');}return context;};export const NotificationProvider=_ref=>{let{children}=_ref;const[notifications,setNotifications]=useState([]);useEffect(()=>{// Load notifications from localStorage on mount\nconst savedNotifications=localStorage.getItem('alaa_notifications');if(savedNotifications){try{const parsed=JSON.parse(savedNotifications);setNotifications(parsed.map(n=>({...n,timestamp:new Date(n.timestamp)})));}catch(error){console.error('Error loading notifications:',error);}}},[]);useEffect(()=>{// Save notifications to localStorage whenever they change\nlocalStorage.setItem('alaa_notifications',JSON.stringify(notifications));},[notifications]);const addNotification=notification=>{var _notification$autoHid,_notification$duratio;const newNotification={...notification,id:Math.random().toString(36).substring(2,15),timestamp:new Date(),read:false,autoHide:(_notification$autoHid=notification.autoHide)!==null&&_notification$autoHid!==void 0?_notification$autoHid:true,duration:(_notification$duratio=notification.duration)!==null&&_notification$duratio!==void 0?_notification$duratio:5000};setNotifications(prev=>[newNotification,...prev]);// Auto-hide notification if specified\nif(newNotification.autoHide){setTimeout(()=>{removeNotification(newNotification.id);},newNotification.duration);}};const removeNotification=id=>{setNotifications(prev=>prev.filter(n=>n.id!==id));};const markAsRead=id=>{setNotifications(prev=>prev.map(n=>n.id===id?{...n,read:true}:n));};const markAllAsRead=()=>{setNotifications(prev=>prev.map(n=>({...n,read:true})));};const clearAll=()=>{setNotifications([]);};const unreadCount=notifications.filter(n=>!n.read).length;return/*#__PURE__*/_jsx(NotificationContext.Provider,{value:{notifications,addNotification,removeNotification,markAsRead,markAllAsRead,clearAll,unreadCount},children:children});};// Toast Notifications Component\nexport const ToastNotifications=()=>{const{notifications,removeNotification}=useNotifications();const toastNotifications=notifications.filter(n=>n.autoHide&&['success','error','warning','info'].includes(n.type));const getIcon=type=>{switch(type){case'success':return/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-6 h-6 text-green-600\"});case'error':return/*#__PURE__*/_jsx(XCircleIcon,{className:\"w-6 h-6 text-red-600\"});case'warning':return/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"w-6 h-6 text-yellow-600\"});case'info':return/*#__PURE__*/_jsx(InformationCircleIcon,{className:\"w-6 h-6 text-blue-600\"});default:return/*#__PURE__*/_jsx(InformationCircleIcon,{className:\"w-6 h-6 text-blue-600\"});}};const getBackgroundColor=type=>{switch(type){case'success':return'bg-green-50 border-green-200';case'error':return'bg-red-50 border-red-200';case'warning':return'bg-yellow-50 border-yellow-200';case'info':return'bg-blue-50 border-blue-200';default:return'bg-blue-50 border-blue-200';}};return/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-4 right-4 z-50 space-y-2\",children:/*#__PURE__*/_jsx(AnimatePresence,{children:toastNotifications.slice(0,5).map(notification=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,x:300,scale:0.3},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:300,scale:0.5},transition:{duration:0.3},className:`max-w-sm w-full border rounded-lg shadow-lg p-4 ${getBackgroundColor(notification.type)}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:getIcon(notification.type)}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-3 flex-1\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900\",children:notification.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mt-1\",children:notification.message})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>removeNotification(notification.id),className:\"flex-shrink-0 text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-5 h-5\"})})]})},notification.id))})});};// Notification Bell Component\nexport const NotificationBell=()=>{const{notifications,unreadCount,markAllAsRead}=useNotifications();const[isOpen,setIsOpen]=useState(false);const persistentNotifications=notifications.filter(n=>!n.autoHide||['course','certificate','achievement'].includes(n.type));const getNotificationIcon=type=>{switch(type){case'course':return/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-5 h-5 text-blue-600\"});case'certificate':return/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-5 h-5 text-yellow-600\"});case'achievement':return/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-5 h-5 text-purple-600\"});default:return/*#__PURE__*/_jsx(InformationCircleIcon,{className:\"w-5 h-5 text-blue-600\"});}};const formatTime=timestamp=>{const now=new Date();const diff=now.getTime()-timestamp.getTime();const minutes=Math.floor(diff/60000);const hours=Math.floor(diff/3600000);const days=Math.floor(diff/86400000);if(minutes<1)return'الآن';if(minutes<60)return`منذ ${minutes} دقيقة`;if(hours<24)return`منذ ${hours} ساعة`;return`منذ ${days} يوم`;};return/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsOpen(!isOpen),className:\"relative p-2 text-gray-600 hover:text-gray-900 transition-colors\",children:[/*#__PURE__*/_jsx(BellIcon,{className:\"w-6 h-6\"}),unreadCount>0&&/*#__PURE__*/_jsx(\"span\",{className:\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",children:unreadCount>9?'9+':unreadCount})]}),/*#__PURE__*/_jsx(AnimatePresence,{children:isOpen&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40\",onClick:()=>setIsOpen(false)}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:-10,scale:0.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:0.95},transition:{duration:0.2},className:\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-b\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"}),unreadCount>0&&/*#__PURE__*/_jsx(\"button\",{onClick:markAllAsRead,className:\"text-sm text-blue-600 hover:text-blue-800\",children:\"\\u062A\\u062D\\u062F\\u064A\\u062F \\u0627\\u0644\\u0643\\u0644 \\u0643\\u0645\\u0642\\u0631\\u0648\\u0621\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-h-96 overflow-y-auto\",children:persistentNotifications.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"p-8 text-center\",children:[/*#__PURE__*/_jsx(BellIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"divide-y divide-gray-200\",children:persistentNotifications.slice(0,10).map(notification=>/*#__PURE__*/_jsx(\"div\",{className:`p-4 hover:bg-gray-50 transition-colors ${!notification.read?'bg-blue-50':''}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:getNotificationIcon(notification.type)}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h4\",{className:`text-sm font-medium ${!notification.read?'text-gray-900':'text-gray-700'}`,children:notification.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mt-1\",children:notification.message}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400 mt-2\",children:formatTime(notification.timestamp)}),notification.actionUrl&&notification.actionText&&/*#__PURE__*/_jsx(\"a\",{href:notification.actionUrl,className:\"text-sm text-blue-600 hover:text-blue-800 mt-2 inline-block\",children:notification.actionText})]}),!notification.read&&/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2\"})]})},notification.id))})}),persistentNotifications.length>10&&/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-t text-center\",children:/*#__PURE__*/_jsx(\"button\",{className:\"text-sm text-blue-600 hover:text-blue-800\",children:\"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\"})})]})]})})]});};// Notification Hooks for common use cases\nexport const useNotificationActions=()=>{const{addNotification}=useNotifications();return{notifySuccess:(title,message)=>{addNotification({type:'success',title,message});},notifyError:(title,message)=>{addNotification({type:'error',title,message});},notifyWarning:(title,message)=>{addNotification({type:'warning',title,message});},notifyInfo:(title,message)=>{addNotification({type:'info',title,message});},notifyCourseUpdate:(courseTitle,message)=>{addNotification({type:'course',title:`تحديث في الكورس: ${courseTitle}`,message,autoHide:false});},notifyCertificateEarned:courseTitle=>{addNotification({type:'certificate',title:'تهانينا! حصلت على شهادة جديدة',message:`تم إصدار شهادة إتمام كورس \"${courseTitle}\"`,autoHide:false,actionUrl:'/student/certificates',actionText:'عرض الشهادة'});},notifyAchievement:achievement=>{addNotification({type:'achievement',title:'إنجاز جديد!',message:achievement,autoHide:false});}};};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "useContext", "motion", "AnimatePresence", "CheckCircleIcon", "ExclamationTriangleIcon", "InformationCircleIcon", "XCircleIcon", "XMarkIcon", "BellIcon", "AcademicCapIcon", "TrophyIcon", "BookOpenIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "NotificationContext", "undefined", "useNotifications", "context", "Error", "NotificationProvider", "_ref", "children", "notifications", "setNotifications", "savedNotifications", "localStorage", "getItem", "parsed", "JSON", "parse", "map", "n", "timestamp", "Date", "error", "console", "setItem", "stringify", "addNotification", "notification", "_notification$autoHid", "_notification$duratio", "newNotification", "id", "Math", "random", "toString", "substring", "read", "autoHide", "duration", "prev", "setTimeout", "removeNotification", "filter", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "clearAll", "unreadCount", "length", "Provider", "value", "ToastNotifications", "toastNotifications", "includes", "type", "getIcon", "className", "getBackgroundColor", "slice", "div", "initial", "opacity", "x", "scale", "animate", "exit", "transition", "title", "message", "onClick", "NotificationBell", "isOpen", "setIsOpen", "persistentNotifications", "getNotificationIcon", "formatTime", "now", "diff", "getTime", "minutes", "floor", "hours", "days", "y", "actionUrl", "actionText", "href", "useNotificationActions", "notifySuccess", "notifyError", "notify<PERSON><PERSON><PERSON>", "notifyInfo", "notifyCourseUpdate", "courseTitle", "notifyCertificateEarned", "notifyAchievement", "achievement"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/common/NotificationSystem.tsx"], "sourcesContent": ["import React, { useState, useEffect, createContext, useContext } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  XMarkIcon,\n  BellIcon,\n  AcademicCapIcon,\n  TrophyIcon,\n  BookOpenIcon\n} from '@heroicons/react/24/outline';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info' | 'course' | 'certificate' | 'achievement';\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n  actionUrl?: string;\n  actionText?: string;\n  autoHide?: boolean;\n  duration?: number;\n}\n\ninterface NotificationContextType {\n  notifications: Notification[];\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  removeNotification: (id: string) => void;\n  markAsRead: (id: string) => void;\n  markAllAsRead: () => void;\n  clearAll: () => void;\n  unreadCount: number;\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined);\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext);\n  if (!context) {\n    throw new Error('useNotifications must be used within a NotificationProvider');\n  }\n  return context;\n};\n\ninterface NotificationProviderProps {\n  children: React.ReactNode;\n}\n\nexport const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n\n  useEffect(() => {\n    // Load notifications from localStorage on mount\n    const savedNotifications = localStorage.getItem('alaa_notifications');\n    if (savedNotifications) {\n      try {\n        const parsed = JSON.parse(savedNotifications);\n        setNotifications(parsed.map((n: any) => ({\n          ...n,\n          timestamp: new Date(n.timestamp)\n        })));\n      } catch (error) {\n        console.error('Error loading notifications:', error);\n      }\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save notifications to localStorage whenever they change\n    localStorage.setItem('alaa_notifications', JSON.stringify(notifications));\n  }, [notifications]);\n\n  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {\n    const newNotification: Notification = {\n      ...notification,\n      id: Math.random().toString(36).substring(2, 15),\n      timestamp: new Date(),\n      read: false,\n      autoHide: notification.autoHide ?? true,\n      duration: notification.duration ?? 5000\n    };\n\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-hide notification if specified\n    if (newNotification.autoHide) {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, newNotification.duration);\n    }\n  };\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(n => n.id === id ? { ...n, read: true } : n)\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n  };\n\n  const clearAll = () => {\n    setNotifications([]);\n  };\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  return (\n    <NotificationContext.Provider\n      value={{\n        notifications,\n        addNotification,\n        removeNotification,\n        markAsRead,\n        markAllAsRead,\n        clearAll,\n        unreadCount\n      }}\n    >\n      {children}\n    </NotificationContext.Provider>\n  );\n};\n\n// Toast Notifications Component\nexport const ToastNotifications: React.FC = () => {\n  const { notifications, removeNotification } = useNotifications();\n  \n  const toastNotifications = notifications.filter(n => \n    n.autoHide && ['success', 'error', 'warning', 'info'].includes(n.type)\n  );\n\n  const getIcon = (type: Notification['type']) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircleIcon className=\"w-6 h-6 text-green-600\" />;\n      case 'error':\n        return <XCircleIcon className=\"w-6 h-6 text-red-600\" />;\n      case 'warning':\n        return <ExclamationTriangleIcon className=\"w-6 h-6 text-yellow-600\" />;\n      case 'info':\n        return <InformationCircleIcon className=\"w-6 h-6 text-blue-600\" />;\n      default:\n        return <InformationCircleIcon className=\"w-6 h-6 text-blue-600\" />;\n    }\n  };\n\n  const getBackgroundColor = (type: Notification['type']) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n      default:\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      <AnimatePresence>\n        {toastNotifications.slice(0, 5).map((notification) => (\n          <motion.div\n            key={notification.id}\n            initial={{ opacity: 0, x: 300, scale: 0.3 }}\n            animate={{ opacity: 1, x: 0, scale: 1 }}\n            exit={{ opacity: 0, x: 300, scale: 0.5 }}\n            transition={{ duration: 0.3 }}\n            className={`max-w-sm w-full border rounded-lg shadow-lg p-4 ${getBackgroundColor(notification.type)}`}\n          >\n            <div className=\"flex items-start\">\n              <div className=\"flex-shrink-0\">\n                {getIcon(notification.type)}\n              </div>\n              <div className=\"mr-3 flex-1\">\n                <h4 className=\"text-sm font-medium text-gray-900\">\n                  {notification.title}\n                </h4>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  {notification.message}\n                </p>\n              </div>\n              <button\n                onClick={() => removeNotification(notification.id)}\n                className=\"flex-shrink-0 text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </motion.div>\n        ))}\n      </AnimatePresence>\n    </div>\n  );\n};\n\n// Notification Bell Component\nexport const NotificationBell: React.FC = () => {\n  const { notifications, unreadCount, markAllAsRead } = useNotifications();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const persistentNotifications = notifications.filter(n => \n    !n.autoHide || ['course', 'certificate', 'achievement'].includes(n.type)\n  );\n\n  const getNotificationIcon = (type: Notification['type']) => {\n    switch (type) {\n      case 'course':\n        return <BookOpenIcon className=\"w-5 h-5 text-blue-600\" />;\n      case 'certificate':\n        return <TrophyIcon className=\"w-5 h-5 text-yellow-600\" />;\n      case 'achievement':\n        return <AcademicCapIcon className=\"w-5 h-5 text-purple-600\" />;\n      default:\n        return <InformationCircleIcon className=\"w-5 h-5 text-blue-600\" />;\n    }\n  };\n\n  const formatTime = (timestamp: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - timestamp.getTime();\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n\n    if (minutes < 1) return 'الآن';\n    if (minutes < 60) return `منذ ${minutes} دقيقة`;\n    if (hours < 24) return `منذ ${hours} ساعة`;\n    return `منذ ${days} يوم`;\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-900 transition-colors\"\n      >\n        <BellIcon className=\"w-6 h-6\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n            {unreadCount > 9 ? '9+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <div\n              className=\"fixed inset-0 z-40\"\n              onClick={() => setIsOpen(false)}\n            />\n            \n            {/* Notification Panel */}\n            <motion.div\n              initial={{ opacity: 0, y: -10, scale: 0.95 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              exit={{ opacity: 0, y: -10, scale: 0.95 }}\n              transition={{ duration: 0.2 }}\n              className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\"\n            >\n              <div className=\"p-4 border-b\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">الإشعارات</h3>\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"text-sm text-blue-600 hover:text-blue-800\"\n                    >\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"max-h-96 overflow-y-auto\">\n                {persistentNotifications.length === 0 ? (\n                  <div className=\"p-8 text-center\">\n                    <BellIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                    <p className=\"text-gray-500\">لا توجد إشعارات</p>\n                  </div>\n                ) : (\n                  <div className=\"divide-y divide-gray-200\">\n                    {persistentNotifications.slice(0, 10).map((notification) => (\n                      <div\n                        key={notification.id}\n                        className={`p-4 hover:bg-gray-50 transition-colors ${\n                          !notification.read ? 'bg-blue-50' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-start space-x-3 space-x-reverse\">\n                          <div className=\"flex-shrink-0\">\n                            {getNotificationIcon(notification.type)}\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.read ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1\">\n                              {notification.message}\n                            </p>\n                            <p className=\"text-xs text-gray-400 mt-2\">\n                              {formatTime(notification.timestamp)}\n                            </p>\n                            {notification.actionUrl && notification.actionText && (\n                              <a\n                                href={notification.actionUrl}\n                                className=\"text-sm text-blue-600 hover:text-blue-800 mt-2 inline-block\"\n                              >\n                                {notification.actionText}\n                              </a>\n                            )}\n                          </div>\n                          {!notification.read && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2\"></div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              {persistentNotifications.length > 10 && (\n                <div className=\"p-4 border-t text-center\">\n                  <button className=\"text-sm text-blue-600 hover:text-blue-800\">\n                    عرض جميع الإشعارات\n                  </button>\n                </div>\n              )}\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\n// Notification Hooks for common use cases\nexport const useNotificationActions = () => {\n  const { addNotification } = useNotifications();\n\n  return {\n    notifySuccess: (title: string, message: string) => {\n      addNotification({ type: 'success', title, message });\n    },\n\n    notifyError: (title: string, message: string) => {\n      addNotification({ type: 'error', title, message });\n    },\n\n    notifyWarning: (title: string, message: string) => {\n      addNotification({ type: 'warning', title, message });\n    },\n\n    notifyInfo: (title: string, message: string) => {\n      addNotification({ type: 'info', title, message });\n    },\n\n    notifyCourseUpdate: (courseTitle: string, message: string) => {\n      addNotification({\n        type: 'course',\n        title: `تحديث في الكورس: ${courseTitle}`,\n        message,\n        autoHide: false\n      });\n    },\n\n    notifyCertificateEarned: (courseTitle: string) => {\n      addNotification({\n        type: 'certificate',\n        title: 'تهانينا! حصلت على شهادة جديدة',\n        message: `تم إصدار شهادة إتمام كورس \"${courseTitle}\"`,\n        autoHide: false,\n        actionUrl: '/student/certificates',\n        actionText: 'عرض الشهادة'\n      });\n    },\n\n    notifyAchievement: (achievement: string) => {\n      addNotification({\n        type: 'achievement',\n        title: 'إنجاز جديد!',\n        message: achievement,\n        autoHide: false\n      });\n    }\n  };\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,aAAa,CAAEC,UAAU,KAAQ,OAAO,CAC7E,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,eAAe,CACfC,uBAAuB,CACvBC,qBAAqB,CACrBC,WAAW,CACXC,SAAS,CACTC,QAAQ,CACRC,eAAe,CACfC,UAAU,CACVC,YAAY,KACP,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAyBrC,KAAM,CAAAC,mBAAmB,cAAGnB,aAAa,CAAsCoB,SAAS,CAAC,CAEzF,MAAO,MAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,OAAO,CAAGrB,UAAU,CAACkB,mBAAmB,CAAC,CAC/C,GAAI,CAACG,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6DAA6D,CAAC,CAChF,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAMD,MAAO,MAAM,CAAAE,oBAAyD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpF,KAAM,CAACE,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAiB,EAAE,CAAC,CAEtEC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA8B,kBAAkB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACrE,GAAIF,kBAAkB,CAAE,CACtB,GAAI,CACF,KAAM,CAAAG,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACL,kBAAkB,CAAC,CAC7CD,gBAAgB,CAACI,MAAM,CAACG,GAAG,CAAEC,CAAM,GAAM,CACvC,GAAGA,CAAC,CACJC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACF,CAAC,CAACC,SAAS,CACjC,CAAC,CAAC,CAAC,CAAC,CACN,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAENxC,SAAS,CAAC,IAAM,CACd;AACA+B,YAAY,CAACW,OAAO,CAAC,oBAAoB,CAAER,IAAI,CAACS,SAAS,CAACf,aAAa,CAAC,CAAC,CAC3E,CAAC,CAAE,CAACA,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAgB,eAAe,CAAIC,YAA6D,EAAK,KAAAC,qBAAA,CAAAC,qBAAA,CACzF,KAAM,CAAAC,eAA6B,CAAG,CACpC,GAAGH,YAAY,CACfI,EAAE,CAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAC/Cf,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBe,IAAI,CAAE,KAAK,CACXC,QAAQ,EAAAT,qBAAA,CAAED,YAAY,CAACU,QAAQ,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,IAAI,CACvCU,QAAQ,EAAAT,qBAAA,CAAEF,YAAY,CAACW,QAAQ,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,IACrC,CAAC,CAEDlB,gBAAgB,CAAC4B,IAAI,EAAI,CAACT,eAAe,CAAE,GAAGS,IAAI,CAAC,CAAC,CAEpD;AACA,GAAIT,eAAe,CAACO,QAAQ,CAAE,CAC5BG,UAAU,CAAC,IAAM,CACfC,kBAAkB,CAACX,eAAe,CAACC,EAAE,CAAC,CACxC,CAAC,CAAED,eAAe,CAACQ,QAAQ,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAG,kBAAkB,CAAIV,EAAU,EAAK,CACzCpB,gBAAgB,CAAC4B,IAAI,EAAIA,IAAI,CAACG,MAAM,CAACvB,CAAC,EAAIA,CAAC,CAACY,EAAE,GAAKA,EAAE,CAAC,CAAC,CACzD,CAAC,CAED,KAAM,CAAAY,UAAU,CAAIZ,EAAU,EAAK,CACjCpB,gBAAgB,CAAC4B,IAAI,EACnBA,IAAI,CAACrB,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACY,EAAE,GAAKA,EAAE,CAAG,CAAE,GAAGZ,CAAC,CAAEiB,IAAI,CAAE,IAAK,CAAC,CAAGjB,CAAC,CACtD,CAAC,CACH,CAAC,CAED,KAAM,CAAAyB,aAAa,CAAGA,CAAA,GAAM,CAC1BjC,gBAAgB,CAAC4B,IAAI,EAAIA,IAAI,CAACrB,GAAG,CAACC,CAAC,GAAK,CAAE,GAAGA,CAAC,CAAEiB,IAAI,CAAE,IAAK,CAAC,CAAC,CAAC,CAAC,CACjE,CAAC,CAED,KAAM,CAAAS,QAAQ,CAAGA,CAAA,GAAM,CACrBlC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,KAAM,CAAAmC,WAAW,CAAGpC,aAAa,CAACgC,MAAM,CAACvB,CAAC,EAAI,CAACA,CAAC,CAACiB,IAAI,CAAC,CAACW,MAAM,CAE7D,mBACElD,IAAA,CAACK,mBAAmB,CAAC8C,QAAQ,EAC3BC,KAAK,CAAE,CACLvC,aAAa,CACbgB,eAAe,CACfe,kBAAkB,CAClBE,UAAU,CACVC,aAAa,CACbC,QAAQ,CACRC,WACF,CAAE,CAAArC,QAAA,CAEDA,QAAQ,CACmB,CAAC,CAEnC,CAAC,CAED;AACA,MAAO,MAAM,CAAAyC,kBAA4B,CAAGA,CAAA,GAAM,CAChD,KAAM,CAAExC,aAAa,CAAE+B,kBAAmB,CAAC,CAAGrC,gBAAgB,CAAC,CAAC,CAEhE,KAAM,CAAA+C,kBAAkB,CAAGzC,aAAa,CAACgC,MAAM,CAACvB,CAAC,EAC/CA,CAAC,CAACkB,QAAQ,EAAI,CAAC,SAAS,CAAE,OAAO,CAAE,SAAS,CAAE,MAAM,CAAC,CAACe,QAAQ,CAACjC,CAAC,CAACkC,IAAI,CACvE,CAAC,CAED,KAAM,CAAAC,OAAO,CAAID,IAA0B,EAAK,CAC9C,OAAQA,IAAI,EACV,IAAK,SAAS,CACZ,mBAAOxD,IAAA,CAACV,eAAe,EAACoE,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC/D,IAAK,OAAO,CACV,mBAAO1D,IAAA,CAACP,WAAW,EAACiE,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACzD,IAAK,SAAS,CACZ,mBAAO1D,IAAA,CAACT,uBAAuB,EAACmE,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACxE,IAAK,MAAM,CACT,mBAAO1D,IAAA,CAACR,qBAAqB,EAACkE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACpE,QACE,mBAAO1D,IAAA,CAACR,qBAAqB,EAACkE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtE,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAIH,IAA0B,EAAK,CACzD,OAAQA,IAAI,EACV,IAAK,SAAS,CACZ,MAAO,8BAA8B,CACvC,IAAK,OAAO,CACV,MAAO,0BAA0B,CACnC,IAAK,SAAS,CACZ,MAAO,gCAAgC,CACzC,IAAK,MAAM,CACT,MAAO,4BAA4B,CACrC,QACE,MAAO,4BAA4B,CACvC,CACF,CAAC,CAED,mBACExD,IAAA,QAAK0D,SAAS,CAAC,oCAAoC,CAAA9C,QAAA,cACjDZ,IAAA,CAACX,eAAe,EAAAuB,QAAA,CACb0C,kBAAkB,CAACM,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACvC,GAAG,CAAES,YAAY,eAC/C9B,IAAA,CAACZ,MAAM,CAACyE,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC5CC,OAAO,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAE,CACxCE,IAAI,CAAE,CAAEJ,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CACzCG,UAAU,CAAE,CAAE3B,QAAQ,CAAE,GAAI,CAAE,CAC9BiB,SAAS,CAAE,mDAAmDC,kBAAkB,CAAC7B,YAAY,CAAC0B,IAAI,CAAC,EAAG,CAAA5C,QAAA,cAEtGV,KAAA,QAAKwD,SAAS,CAAC,kBAAkB,CAAA9C,QAAA,eAC/BZ,IAAA,QAAK0D,SAAS,CAAC,eAAe,CAAA9C,QAAA,CAC3B6C,OAAO,CAAC3B,YAAY,CAAC0B,IAAI,CAAC,CACxB,CAAC,cACNtD,KAAA,QAAKwD,SAAS,CAAC,aAAa,CAAA9C,QAAA,eAC1BZ,IAAA,OAAI0D,SAAS,CAAC,mCAAmC,CAAA9C,QAAA,CAC9CkB,YAAY,CAACuC,KAAK,CACjB,CAAC,cACLrE,IAAA,MAAG0D,SAAS,CAAC,4BAA4B,CAAA9C,QAAA,CACtCkB,YAAY,CAACwC,OAAO,CACpB,CAAC,EACD,CAAC,cACNtE,IAAA,WACEuE,OAAO,CAAEA,CAAA,GAAM3B,kBAAkB,CAACd,YAAY,CAACI,EAAE,CAAE,CACnDwB,SAAS,CAAC,iDAAiD,CAAA9C,QAAA,cAE3DZ,IAAA,CAACN,SAAS,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,EAzBD5B,YAAY,CAACI,EA0BR,CACb,CAAC,CACa,CAAC,CACf,CAAC,CAEV,CAAC,CAED;AACA,MAAO,MAAM,CAAAsC,gBAA0B,CAAGA,CAAA,GAAM,CAC9C,KAAM,CAAE3D,aAAa,CAAEoC,WAAW,CAAEF,aAAc,CAAC,CAAGxC,gBAAgB,CAAC,CAAC,CACxE,KAAM,CAACkE,MAAM,CAAEC,SAAS,CAAC,CAAG1F,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAAA2F,uBAAuB,CAAG9D,aAAa,CAACgC,MAAM,CAACvB,CAAC,EACpD,CAACA,CAAC,CAACkB,QAAQ,EAAI,CAAC,QAAQ,CAAE,aAAa,CAAE,aAAa,CAAC,CAACe,QAAQ,CAACjC,CAAC,CAACkC,IAAI,CACzE,CAAC,CAED,KAAM,CAAAoB,mBAAmB,CAAIpB,IAA0B,EAAK,CAC1D,OAAQA,IAAI,EACV,IAAK,QAAQ,CACX,mBAAOxD,IAAA,CAACF,YAAY,EAAC4D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3D,IAAK,aAAa,CAChB,mBAAO1D,IAAA,CAACH,UAAU,EAAC6D,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC3D,IAAK,aAAa,CAChB,mBAAO1D,IAAA,CAACJ,eAAe,EAAC8D,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAChE,QACE,mBAAO1D,IAAA,CAACR,qBAAqB,EAACkE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtE,CACF,CAAC,CAED,KAAM,CAAAmB,UAAU,CAAItD,SAAe,EAAK,CACtC,KAAM,CAAAuD,GAAG,CAAG,GAAI,CAAAtD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAuD,IAAI,CAAGD,GAAG,CAACE,OAAO,CAAC,CAAC,CAAGzD,SAAS,CAACyD,OAAO,CAAC,CAAC,CAChD,KAAM,CAAAC,OAAO,CAAG9C,IAAI,CAAC+C,KAAK,CAACH,IAAI,CAAG,KAAK,CAAC,CACxC,KAAM,CAAAI,KAAK,CAAGhD,IAAI,CAAC+C,KAAK,CAACH,IAAI,CAAG,OAAO,CAAC,CACxC,KAAM,CAAAK,IAAI,CAAGjD,IAAI,CAAC+C,KAAK,CAACH,IAAI,CAAG,QAAQ,CAAC,CAExC,GAAIE,OAAO,CAAG,CAAC,CAAE,MAAO,MAAM,CAC9B,GAAIA,OAAO,CAAG,EAAE,CAAE,MAAO,OAAOA,OAAO,QAAQ,CAC/C,GAAIE,KAAK,CAAG,EAAE,CAAE,MAAO,OAAOA,KAAK,OAAO,CAC1C,MAAO,OAAOC,IAAI,MAAM,CAC1B,CAAC,CAED,mBACElF,KAAA,QAAKwD,SAAS,CAAC,UAAU,CAAA9C,QAAA,eACvBV,KAAA,WACEqE,OAAO,CAAEA,CAAA,GAAMG,SAAS,CAAC,CAACD,MAAM,CAAE,CAClCf,SAAS,CAAC,kEAAkE,CAAA9C,QAAA,eAE5EZ,IAAA,CAACL,QAAQ,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC/BT,WAAW,CAAG,CAAC,eACdjD,IAAA,SAAM0D,SAAS,CAAC,8GAA8G,CAAA9C,QAAA,CAC3HqC,WAAW,CAAG,CAAC,CAAG,IAAI,CAAGA,WAAW,CACjC,CACP,EACK,CAAC,cAETjD,IAAA,CAACX,eAAe,EAAAuB,QAAA,CACb6D,MAAM,eACLvE,KAAA,CAAAE,SAAA,EAAAQ,QAAA,eAEEZ,IAAA,QACE0D,SAAS,CAAC,oBAAoB,CAC9Ba,OAAO,CAAEA,CAAA,GAAMG,SAAS,CAAC,KAAK,CAAE,CACjC,CAAC,cAGFxE,KAAA,CAACd,MAAM,CAACyE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEsB,CAAC,CAAE,CAAC,EAAE,CAAEpB,KAAK,CAAE,IAAK,CAAE,CAC7CC,OAAO,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEsB,CAAC,CAAE,CAAC,CAAEpB,KAAK,CAAE,CAAE,CAAE,CACxCE,IAAI,CAAE,CAAEJ,OAAO,CAAE,CAAC,CAAEsB,CAAC,CAAE,CAAC,EAAE,CAAEpB,KAAK,CAAE,IAAK,CAAE,CAC1CG,UAAU,CAAE,CAAE3B,QAAQ,CAAE,GAAI,CAAE,CAC9BiB,SAAS,CAAC,qEAAqE,CAAA9C,QAAA,eAE/EZ,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAA9C,QAAA,cAC3BV,KAAA,QAAKwD,SAAS,CAAC,mCAAmC,CAAA9C,QAAA,eAChDZ,IAAA,OAAI0D,SAAS,CAAC,mCAAmC,CAAA9C,QAAA,CAAC,wDAAS,CAAI,CAAC,CAC/DqC,WAAW,CAAG,CAAC,eACdjD,IAAA,WACEuE,OAAO,CAAExB,aAAc,CACvBW,SAAS,CAAC,2CAA2C,CAAA9C,QAAA,CACtD,8FAED,CAAQ,CACT,EACE,CAAC,CACH,CAAC,cAENZ,IAAA,QAAK0D,SAAS,CAAC,0BAA0B,CAAA9C,QAAA,CACtC+D,uBAAuB,CAACzB,MAAM,GAAK,CAAC,cACnChD,KAAA,QAAKwD,SAAS,CAAC,iBAAiB,CAAA9C,QAAA,eAC9BZ,IAAA,CAACL,QAAQ,EAAC+D,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC7D1D,IAAA,MAAG0D,SAAS,CAAC,eAAe,CAAA9C,QAAA,CAAC,kFAAe,CAAG,CAAC,EAC7C,CAAC,cAENZ,IAAA,QAAK0D,SAAS,CAAC,0BAA0B,CAAA9C,QAAA,CACtC+D,uBAAuB,CAACf,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACvC,GAAG,CAAES,YAAY,eACrD9B,IAAA,QAEE0D,SAAS,CAAE,0CACT,CAAC5B,YAAY,CAACS,IAAI,CAAG,YAAY,CAAG,EAAE,EACrC,CAAA3B,QAAA,cAEHV,KAAA,QAAKwD,SAAS,CAAC,4CAA4C,CAAA9C,QAAA,eACzDZ,IAAA,QAAK0D,SAAS,CAAC,eAAe,CAAA9C,QAAA,CAC3BgE,mBAAmB,CAAC9C,YAAY,CAAC0B,IAAI,CAAC,CACpC,CAAC,cACNtD,KAAA,QAAKwD,SAAS,CAAC,gBAAgB,CAAA9C,QAAA,eAC7BZ,IAAA,OAAI0D,SAAS,CAAE,uBACb,CAAC5B,YAAY,CAACS,IAAI,CAAG,eAAe,CAAG,eAAe,EACrD,CAAA3B,QAAA,CACAkB,YAAY,CAACuC,KAAK,CACjB,CAAC,cACLrE,IAAA,MAAG0D,SAAS,CAAC,4BAA4B,CAAA9C,QAAA,CACtCkB,YAAY,CAACwC,OAAO,CACpB,CAAC,cACJtE,IAAA,MAAG0D,SAAS,CAAC,4BAA4B,CAAA9C,QAAA,CACtCiE,UAAU,CAAC/C,YAAY,CAACP,SAAS,CAAC,CAClC,CAAC,CACHO,YAAY,CAACwD,SAAS,EAAIxD,YAAY,CAACyD,UAAU,eAChDvF,IAAA,MACEwF,IAAI,CAAE1D,YAAY,CAACwD,SAAU,CAC7B5B,SAAS,CAAC,6DAA6D,CAAA9C,QAAA,CAEtEkB,YAAY,CAACyD,UAAU,CACvB,CACJ,EACE,CAAC,CACL,CAACzD,YAAY,CAACS,IAAI,eACjBvC,IAAA,QAAK0D,SAAS,CAAC,qDAAqD,CAAM,CAC3E,EACE,CAAC,EAjCD5B,YAAY,CAACI,EAkCf,CACN,CAAC,CACC,CACN,CACE,CAAC,CAELyC,uBAAuB,CAACzB,MAAM,CAAG,EAAE,eAClClD,IAAA,QAAK0D,SAAS,CAAC,0BAA0B,CAAA9C,QAAA,cACvCZ,IAAA,WAAQ0D,SAAS,CAAC,2CAA2C,CAAA9C,QAAA,CAAC,oGAE9D,CAAQ,CAAC,CACN,CACN,EACS,CAAC,EACb,CACH,CACc,CAAC,EACf,CAAC,CAEV,CAAC,CAED;AACA,MAAO,MAAM,CAAA6E,sBAAsB,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAE5D,eAAgB,CAAC,CAAGtB,gBAAgB,CAAC,CAAC,CAE9C,MAAO,CACLmF,aAAa,CAAEA,CAACrB,KAAa,CAAEC,OAAe,GAAK,CACjDzC,eAAe,CAAC,CAAE2B,IAAI,CAAE,SAAS,CAAEa,KAAK,CAAEC,OAAQ,CAAC,CAAC,CACtD,CAAC,CAEDqB,WAAW,CAAEA,CAACtB,KAAa,CAAEC,OAAe,GAAK,CAC/CzC,eAAe,CAAC,CAAE2B,IAAI,CAAE,OAAO,CAAEa,KAAK,CAAEC,OAAQ,CAAC,CAAC,CACpD,CAAC,CAEDsB,aAAa,CAAEA,CAACvB,KAAa,CAAEC,OAAe,GAAK,CACjDzC,eAAe,CAAC,CAAE2B,IAAI,CAAE,SAAS,CAAEa,KAAK,CAAEC,OAAQ,CAAC,CAAC,CACtD,CAAC,CAEDuB,UAAU,CAAEA,CAACxB,KAAa,CAAEC,OAAe,GAAK,CAC9CzC,eAAe,CAAC,CAAE2B,IAAI,CAAE,MAAM,CAAEa,KAAK,CAAEC,OAAQ,CAAC,CAAC,CACnD,CAAC,CAEDwB,kBAAkB,CAAEA,CAACC,WAAmB,CAAEzB,OAAe,GAAK,CAC5DzC,eAAe,CAAC,CACd2B,IAAI,CAAE,QAAQ,CACda,KAAK,CAAE,oBAAoB0B,WAAW,EAAE,CACxCzB,OAAO,CACP9B,QAAQ,CAAE,KACZ,CAAC,CAAC,CACJ,CAAC,CAEDwD,uBAAuB,CAAGD,WAAmB,EAAK,CAChDlE,eAAe,CAAC,CACd2B,IAAI,CAAE,aAAa,CACnBa,KAAK,CAAE,+BAA+B,CACtCC,OAAO,CAAE,8BAA8ByB,WAAW,GAAG,CACrDvD,QAAQ,CAAE,KAAK,CACf8C,SAAS,CAAE,uBAAuB,CAClCC,UAAU,CAAE,aACd,CAAC,CAAC,CACJ,CAAC,CAEDU,iBAAiB,CAAGC,WAAmB,EAAK,CAC1CrE,eAAe,CAAC,CACd2B,IAAI,CAAE,aAAa,CACnBa,KAAK,CAAE,aAAa,CACpBC,OAAO,CAAE4B,WAAW,CACpB1D,QAAQ,CAAE,KACZ,CAAC,CAAC,CACJ,CACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}