{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{AcademicCapIcon,PlusIcon,TrashIcon,DownloadIcon,MagnifyingGlassIcon,UserIcon,BookOpenIcon,CalendarIcon,TrophyIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{supabaseService}from'../../services/supabaseService';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdvancedCertificatesManagement=()=>{const[certificates,setCertificates]=useState([]);const[students,setStudents]=useState([]);const[courses,setCourses]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showAddModal,setShowAddModal]=useState(false);const[selectedStudent,setSelectedStudent]=useState('');const[selectedCourse,setSelectedCourse]=useState('');const[grade,setGrade]=useState(85);useEffect(()=>{loadData();},[]);const loadData=async()=>{try{setLoading(true);const[certificatesData,studentsData,coursesData]=await Promise.all([loadCertificates(),supabaseService.getAllStudents(),supabaseService.getAllCourses()]);setCertificates(certificatesData||[]);setStudents(studentsData||[]);setCourses(coursesData||[]);}catch(error){console.error('Error loading data:',error);toast.error('فشل في تحميل البيانات');}finally{setLoading(false);}};const loadCertificates=async()=>{try{const{data,error}=await supabaseService.supabase.from('certificates').select(`\n          *,\n          students (\n            id,\n            name,\n            access_code,\n            email\n          ),\n          courses (\n            id,\n            title,\n            description,\n            level\n          )\n        `).order('issued_at',{ascending:false});if(error)throw error;return data;}catch(error){console.error('Error loading certificates:',error);return[];}};const generateVerificationCode=()=>{const chars='ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';let result='CERT-';for(let i=0;i<8;i++){result+=chars.charAt(Math.floor(Math.random()*chars.length));}return result;};const handleIssueCertificate=async()=>{if(!selectedStudent||!selectedCourse){toast.error('يرجى اختيار الطالب والكورس');return;}if(grade<0||grade>100){toast.error('يجب أن تكون الدرجة بين 0 و 100');return;}try{// Check if certificate already exists\nconst existingCertificate=certificates.find(c=>c.student_id===selectedStudent&&c.course_id===selectedCourse);if(existingCertificate){toast.error('الطالب لديه شهادة بالفعل لهذا الكورس');return;}const verificationCode=generateVerificationCode();const{data,error}=await supabaseService.supabase.from('certificates').insert([{student_id:selectedStudent,course_id:selectedCourse,grade:grade,verification_code:verificationCode,issued_at:new Date().toISOString()}]).select().single();if(error)throw error;toast.success('تم إصدار الشهادة بنجاح');await loadData();// Reset form\nsetSelectedStudent('');setSelectedCourse('');setGrade(85);setShowAddModal(false);}catch(error){console.error('Error issuing certificate:',error);toast.error('فشل في إصدار الشهادة');}};const handleDeleteCertificate=async certificateId=>{if(!confirm('هل أنت متأكد من حذف هذه الشهادة؟'))return;try{const{error}=await supabaseService.supabase.from('certificates').delete().eq('id',certificateId);if(error)throw error;toast.success('تم حذف الشهادة بنجاح');await loadData();}catch(error){console.error('Error deleting certificate:',error);toast.error('فشل في حذف الشهادة');}};const generateCertificatePDF=async certificate=>{try{// This would typically generate a PDF certificate\n// For now, we'll just show a success message\ntoast.success('تم إنشاء ملف PDF للشهادة');// In a real implementation, you would:\n// 1. Generate PDF using a library like jsPDF or PDFKit\n// 2. Upload to storage (Supabase Storage)\n// 3. Update certificate record with PDF URL\n}catch(error){console.error('Error generating certificate PDF:',error);toast.error('فشل في إنشاء ملف PDF');}};const filteredCertificates=certificates.filter(certificate=>{var _certificate$students,_certificate$students2,_certificate$students3,_certificate$students4,_certificate$courses,_certificate$courses$,_certificate$verifica;const searchLower=searchTerm.toLowerCase();return((_certificate$students=certificate.students)===null||_certificate$students===void 0?void 0:(_certificate$students2=_certificate$students.name)===null||_certificate$students2===void 0?void 0:_certificate$students2.toLowerCase().includes(searchLower))||((_certificate$students3=certificate.students)===null||_certificate$students3===void 0?void 0:(_certificate$students4=_certificate$students3.access_code)===null||_certificate$students4===void 0?void 0:_certificate$students4.toLowerCase().includes(searchLower))||((_certificate$courses=certificate.courses)===null||_certificate$courses===void 0?void 0:(_certificate$courses$=_certificate$courses.title)===null||_certificate$courses$===void 0?void 0:_certificate$courses$.toLowerCase().includes(searchLower))||((_certificate$verifica=certificate.verification_code)===null||_certificate$verifica===void 0?void 0:_certificate$verifica.toLowerCase().includes(searchLower));});const getGradeColor=grade=>{if(grade>=90)return'text-green-600 bg-green-100';if(grade>=80)return'text-blue-600 bg-blue-100';if(grade>=70)return'text-yellow-600 bg-yellow-100';return'text-red-600 bg-red-100';};const getGradeLabel=grade=>{if(grade>=90)return'ممتاز';if(grade>=80)return'جيد جداً';if(grade>=70)return'جيد';return'مقبول';};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0625\\u062A\\u0645\\u0627\\u0645 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddModal(true),className:\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(TrophyIcon,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:certificates.length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-6 h-6 text-green-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0645\\u0645\\u062A\\u0627\\u0632\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:certificates.filter(c=>c.grade>=90).length})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-purple-100 rounded-lg\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-purple-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0637\\u0644\\u0627\\u0628 \\u062D\\u0627\\u0635\\u0644\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:new Set(certificates.map(c=>c.student_id)).size})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-orange-100 rounded-lg\",children:/*#__PURE__*/_jsx(BookOpenIcon,{className:\"w-6 h-6 text-orange-600\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"\\u0643\\u0648\\u0631\\u0633\\u0627\\u062A \\u0628\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:new Set(certificates.map(c=>c.course_id)).size})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u0623\\u0648 \\u0637\\u0627\\u0644\\u0628 \\u0623\\u0648 \\u0643\\u0648\\u0631\\u0633...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u062F\\u0631\\u062C\\u0629\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredCertificates.map(certificate=>{var _certificate$students5,_certificate$students6,_certificate$courses2,_certificate$courses3;return/*#__PURE__*/_jsxs(motion.tr,{initial:{opacity:0},animate:{opacity:1},className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 h-10 w-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"h-6 w-6 text-primary-600\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mr-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:((_certificate$students5=certificate.students)===null||_certificate$students5===void 0?void 0:_certificate$students5.name)||'غير محدد'}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0648\\u0635\\u0648\\u0644: \",(_certificate$students6=certificate.students)===null||_certificate$students6===void 0?void 0:_certificate$students6.access_code]})]})]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:(_certificate$courses2=certificate.courses)===null||_certificate$courses2===void 0?void 0:_certificate$courses2.title}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:(_certificate$courses3=certificate.courses)===null||_certificate$courses3===void 0?void 0:_certificate$courses3.level})]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGradeColor(certificate.grade)}`,children:[certificate.grade,\"% - \",getGradeLabel(certificate.grade)]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\",children:certificate.verification_code})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CalendarIcon,{className:\"w-4 h-4 ml-1\"}),new Date(certificate.issued_at).toLocaleDateString('ar-SA')]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 space-x-reverse\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>generateCertificatePDF(certificate),className:\"text-blue-600 hover:text-blue-900 transition-colors\",title:\"\\u062A\\u062D\\u0645\\u064A\\u0644 PDF\",children:/*#__PURE__*/_jsx(DownloadIcon,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteCertificate(certificate.id),className:\"text-red-600 hover:text-red-900 transition-colors\",title:\"\\u062D\\u0630\\u0641\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-5 h-5\"})})]})})]},certificate.id);})})]})}),filteredCertificates.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AcademicCapIcon,{className:\"w-12 h-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 mb-4\",children:\"\\u0627\\u0628\\u062F\\u0623 \\u0628\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629 \\u0644\\u0644\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddModal(true),className:\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5 ml-2\"}),\"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0634\\u0647\\u0627\\u062F\\u0629\"]})]})]}),showAddModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},className:\"bg-white rounded-lg p-6 w-full max-w-md\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedStudent,onChange:e=>setSelectedStudent(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- \\u0627\\u062E\\u062A\\u0631 \\u0637\\u0627\\u0644\\u0628 --\"}),students.map(student=>/*#__PURE__*/_jsxs(\"option\",{value:student.id,children:[student.name,\" (\",student.access_code,\")\"]},student.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedCourse,onChange:e=>setSelectedCourse(e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"-- \\u0627\\u062E\\u062A\\u0631 \\u0643\\u0648\\u0631\\u0633 --\"}),courses.map(course=>/*#__PURE__*/_jsx(\"option\",{value:course.id,children:course.title},course.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"\\u0627\\u0644\\u062F\\u0631\\u062C\\u0629 (%)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",min:\"0\",max:\"100\",value:grade,onChange:e=>setGrade(parseInt(e.target.value)||0),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 space-x-reverse mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowAddModal(false),className:\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleIssueCertificate,className:\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",children:\"\\u0625\\u0635\\u062F\\u0627\\u0631 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"})]})]})})]});};export default AdvancedCertificatesManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AcademicCapIcon", "PlusIcon", "TrashIcon", "DownloadIcon", "MagnifyingGlassIcon", "UserIcon", "BookOpenIcon", "CalendarIcon", "TrophyIcon", "CheckCircleIcon", "supabaseService", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "AdvancedCertificatesManagement", "certificates", "setCertificates", "students", "setStudents", "courses", "setCourses", "loading", "setLoading", "searchTerm", "setSearchTerm", "showAddModal", "setShowAddModal", "selectedStudent", "setSelectedStudent", "selectedCourse", "setSelectedCourse", "grade", "setGrade", "loadData", "certificatesData", "studentsData", "coursesData", "Promise", "all", "loadCertificates", "getAllStudents", "getAllCourses", "error", "console", "data", "supabase", "from", "select", "order", "ascending", "generateVerificationCode", "chars", "result", "i", "char<PERSON>t", "Math", "floor", "random", "length", "handleIssueCertificate", "existingCertificate", "find", "c", "student_id", "course_id", "verificationCode", "insert", "verification_code", "issued_at", "Date", "toISOString", "single", "success", "handleDeleteCertificate", "certificateId", "confirm", "delete", "eq", "generateCertificatePDF", "certificate", "filteredCertificates", "filter", "_certificate$students", "_certificate$students2", "_certificate$students3", "_certificate$students4", "_certificate$courses", "_certificate$courses$", "_certificate$verifica", "searchLower", "toLowerCase", "name", "includes", "access_code", "title", "getGradeColor", "getGradeLabel", "className", "children", "onClick", "Set", "map", "size", "type", "placeholder", "value", "onChange", "e", "target", "_certificate$students5", "_certificate$students6", "_certificate$courses2", "_certificate$courses3", "tr", "initial", "opacity", "animate", "level", "toLocaleDateString", "id", "div", "scale", "student", "course", "min", "max", "parseInt"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Admin/AdvancedCertificatesManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  AcademicCapIcon,\n  PlusIcon,\n  TrashIcon,\n  EyeIcon,\n  DownloadIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  BookOpenIcon,\n  CalendarIcon,\n  TrophyIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\nimport { supabaseService } from '../../services/supabaseService';\nimport { toast } from 'react-hot-toast';\n\ninterface Certificate {\n  id: string;\n  student_id: string;\n  course_id: string;\n  certificate_url?: string;\n  grade: number;\n  issued_at: string;\n  verification_code: string;\n  students: {\n    id: string;\n    name: string;\n    access_code: string;\n    email?: string;\n  };\n  courses: {\n    id: string;\n    title: string;\n    description: string;\n    level: string;\n  };\n}\n\ninterface Student {\n  id: string;\n  name: string;\n  access_code: string;\n  email?: string;\n}\n\ninterface Course {\n  id: string;\n  title: string;\n  description: string;\n  level: string;\n}\n\nconst AdvancedCertificatesManagement: React.FC = () => {\n  const [certificates, setCertificates] = useState<Certificate[]>([]);\n  const [students, setStudents] = useState<Student[]>([]);\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [selectedStudent, setSelectedStudent] = useState<string>('');\n  const [selectedCourse, setSelectedCourse] = useState<string>('');\n  const [grade, setGrade] = useState<number>(85);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [certificatesData, studentsData, coursesData] = await Promise.all([\n        loadCertificates(),\n        supabaseService.getAllStudents(),\n        supabaseService.getAllCourses()\n      ]);\n\n      setCertificates(certificatesData || []);\n      setStudents(studentsData || []);\n      setCourses(coursesData || []);\n    } catch (error) {\n      console.error('Error loading data:', error);\n      toast.error('فشل في تحميل البيانات');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCertificates = async () => {\n    try {\n      const { data, error } = await supabaseService.supabase\n        .from('certificates')\n        .select(`\n          *,\n          students (\n            id,\n            name,\n            access_code,\n            email\n          ),\n          courses (\n            id,\n            title,\n            description,\n            level\n          )\n        `)\n        .order('issued_at', { ascending: false });\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('Error loading certificates:', error);\n      return [];\n    }\n  };\n\n  const generateVerificationCode = () => {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'CERT-';\n    for (let i = 0; i < 8; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  };\n\n  const handleIssueCertificate = async () => {\n    if (!selectedStudent || !selectedCourse) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n\n    if (grade < 0 || grade > 100) {\n      toast.error('يجب أن تكون الدرجة بين 0 و 100');\n      return;\n    }\n\n    try {\n      // Check if certificate already exists\n      const existingCertificate = certificates.find(\n        c => c.student_id === selectedStudent && c.course_id === selectedCourse\n      );\n\n      if (existingCertificate) {\n        toast.error('الطالب لديه شهادة بالفعل لهذا الكورس');\n        return;\n      }\n\n      const verificationCode = generateVerificationCode();\n\n      const { data, error } = await supabaseService.supabase\n        .from('certificates')\n        .insert([{\n          student_id: selectedStudent,\n          course_id: selectedCourse,\n          grade: grade,\n          verification_code: verificationCode,\n          issued_at: new Date().toISOString()\n        }])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      toast.success('تم إصدار الشهادة بنجاح');\n      await loadData();\n      \n      // Reset form\n      setSelectedStudent('');\n      setSelectedCourse('');\n      setGrade(85);\n      setShowAddModal(false);\n    } catch (error) {\n      console.error('Error issuing certificate:', error);\n      toast.error('فشل في إصدار الشهادة');\n    }\n  };\n\n  const handleDeleteCertificate = async (certificateId: string) => {\n    if (!confirm('هل أنت متأكد من حذف هذه الشهادة؟')) return;\n\n    try {\n      const { error } = await supabaseService.supabase\n        .from('certificates')\n        .delete()\n        .eq('id', certificateId);\n\n      if (error) throw error;\n\n      toast.success('تم حذف الشهادة بنجاح');\n      await loadData();\n    } catch (error) {\n      console.error('Error deleting certificate:', error);\n      toast.error('فشل في حذف الشهادة');\n    }\n  };\n\n  const generateCertificatePDF = async (certificate: Certificate) => {\n    try {\n      // This would typically generate a PDF certificate\n      // For now, we'll just show a success message\n      toast.success('تم إنشاء ملف PDF للشهادة');\n      \n      // In a real implementation, you would:\n      // 1. Generate PDF using a library like jsPDF or PDFKit\n      // 2. Upload to storage (Supabase Storage)\n      // 3. Update certificate record with PDF URL\n      \n    } catch (error) {\n      console.error('Error generating certificate PDF:', error);\n      toast.error('فشل في إنشاء ملف PDF');\n    }\n  };\n\n  const filteredCertificates = certificates.filter(certificate => {\n    const searchLower = searchTerm.toLowerCase();\n    return (\n      certificate.students?.name?.toLowerCase().includes(searchLower) ||\n      certificate.students?.access_code?.toLowerCase().includes(searchLower) ||\n      certificate.courses?.title?.toLowerCase().includes(searchLower) ||\n      certificate.verification_code?.toLowerCase().includes(searchLower)\n    );\n  });\n\n  const getGradeColor = (grade: number) => {\n    if (grade >= 90) return 'text-green-600 bg-green-100';\n    if (grade >= 80) return 'text-blue-600 bg-blue-100';\n    if (grade >= 70) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  };\n\n  const getGradeLabel = (grade: number) => {\n    if (grade >= 90) return 'ممتاز';\n    if (grade >= 80) return 'جيد جداً';\n    if (grade >= 70) return 'جيد';\n    return 'مقبول';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">إدارة الشهادات</h1>\n          <p className=\"text-gray-600 mt-1\">إصدار وإدارة شهادات إتمام الكورسات</p>\n        </div>\n        <button\n          onClick={() => setShowAddModal(true)}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <PlusIcon className=\"w-5 h-5 ml-2\" />\n          إصدار شهادة جديدة\n        </button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <TrophyIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">إجمالي الشهادات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{certificates.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <CheckCircleIcon className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">شهادات ممتازة</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {certificates.filter(c => c.grade >= 90).length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <UserIcon className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">طلاب حاصلين على شهادات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {new Set(certificates.map(c => c.student_id)).size}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-orange-100 rounded-lg\">\n              <BookOpenIcon className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"mr-4\">\n              <p className=\"text-sm text-gray-600\">كورسات بشهادات</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {new Set(certificates.map(c => c.course_id)).size}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n        <div className=\"relative\">\n          <MagnifyingGlassIcon className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"البحث عن شهادة أو طالب أو كورس...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          />\n        </div>\n      </div>\n\n      {/* Certificates Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الطالب\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الكورس\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الدرجة\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  رمز التحقق\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  تاريخ الإصدار\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  الإجراءات\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredCertificates.map((certificate) => (\n                <motion.tr\n                  key={certificate.id}\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  className=\"hover:bg-gray-50\"\n                >\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center\">\n                          <UserIcon className=\"h-6 w-6 text-primary-600\" />\n                        </div>\n                      </div>\n                      <div className=\"mr-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {certificate.students?.name || 'غير محدد'}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          كود الوصول: {certificate.students?.access_code}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {certificate.courses?.title}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {certificate.courses?.level}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGradeColor(certificate.grade)}`}>\n                      {certificate.grade}% - {getGradeLabel(certificate.grade)}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded\">\n                      {certificate.verification_code}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    <div className=\"flex items-center\">\n                      <CalendarIcon className=\"w-4 h-4 ml-1\" />\n                      {new Date(certificate.issued_at).toLocaleDateString('ar-SA')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <button\n                        onClick={() => generateCertificatePDF(certificate)}\n                        className=\"text-blue-600 hover:text-blue-900 transition-colors\"\n                        title=\"تحميل PDF\"\n                      >\n                        <DownloadIcon className=\"w-5 h-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleDeleteCertificate(certificate.id)}\n                        className=\"text-red-600 hover:text-red-900 transition-colors\"\n                        title=\"حذف\"\n                      >\n                        <TrashIcon className=\"w-5 h-5\" />\n                      </button>\n                    </div>\n                  </td>\n                </motion.tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {filteredCertificates.length === 0 && (\n          <div className=\"text-center py-12\">\n            <AcademicCapIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد شهادات</h3>\n            <p className=\"text-gray-500 mb-4\">ابدأ بإصدار شهادة جديدة للطلاب</p>\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5 ml-2\" />\n              إصدار شهادة\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Issue Certificate Modal */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-lg p-6 w-full max-w-md\"\n          >\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">إصدار شهادة جديدة</h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  اختر الطالب\n                </label>\n                <select\n                  value={selectedStudent}\n                  onChange={(e) => setSelectedStudent(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value=\"\">-- اختر طالب --</option>\n                  {students.map((student) => (\n                    <option key={student.id} value={student.id}>\n                      {student.name} ({student.access_code})\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  اختر الكورس\n                </label>\n                <select\n                  value={selectedCourse}\n                  onChange={(e) => setSelectedCourse(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                >\n                  <option value=\"\">-- اختر كورس --</option>\n                  {courses.map((course) => (\n                    <option key={course.id} value={course.id}>\n                      {course.title}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الدرجة (%)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={grade}\n                  onChange={(e) => setGrade(parseInt(e.target.value) || 0)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 space-x-reverse mt-6\">\n              <button\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                إلغاء\n              </button>\n              <button\n                onClick={handleIssueCertificate}\n                className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                إصدار الشهادة\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdvancedCertificatesManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,eAAe,CACfC,QAAQ,CACRC,SAAS,CAETC,YAAY,CACZC,mBAAmB,CACnBC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,UAAU,CACVC,eAAe,KAEV,6BAA6B,CACpC,OAASC,eAAe,KAAQ,gCAAgC,CAChE,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAsCxC,KAAM,CAAAC,8BAAwC,CAAGA,CAAA,GAAM,CACrD,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACsB,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjC,QAAQ,CAAS,EAAE,CAAC,CAClE,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACoC,KAAK,CAAEC,QAAQ,CAAC,CAAGrC,QAAQ,CAAS,EAAE,CAAC,CAE9CC,SAAS,CAAC,IAAM,CACdqC,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACFX,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAACY,gBAAgB,CAAEC,YAAY,CAAEC,WAAW,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACtEC,gBAAgB,CAAC,CAAC,CAClB/B,eAAe,CAACgC,cAAc,CAAC,CAAC,CAChChC,eAAe,CAACiC,aAAa,CAAC,CAAC,CAChC,CAAC,CAEFzB,eAAe,CAACkB,gBAAgB,EAAI,EAAE,CAAC,CACvChB,WAAW,CAACiB,YAAY,EAAI,EAAE,CAAC,CAC/Bf,UAAU,CAACgB,WAAW,EAAI,EAAE,CAAC,CAC/B,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CjC,KAAK,CAACiC,KAAK,CAAC,uBAAuB,CAAC,CACtC,CAAC,OAAS,CACRpB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAEK,IAAI,CAAEF,KAAM,CAAC,CAAG,KAAM,CAAAlC,eAAe,CAACqC,QAAQ,CACnDC,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE3C,GAAIP,KAAK,CAAE,KAAM,CAAAA,KAAK,CACtB,MAAO,CAAAE,IAAI,CACb,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,MAAO,EAAE,CACX,CACF,CAAC,CAED,KAAM,CAAAQ,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,KAAK,CAAG,sCAAsC,CACpD,GAAI,CAAAC,MAAM,CAAG,OAAO,CACpB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC1BD,MAAM,EAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGN,KAAK,CAACO,MAAM,CAAC,CAAC,CAClE,CACA,MAAO,CAAAN,MAAM,CACf,CAAC,CAED,KAAM,CAAAO,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CAAChC,eAAe,EAAI,CAACE,cAAc,CAAE,CACvCpB,KAAK,CAACiC,KAAK,CAAC,4BAA4B,CAAC,CACzC,OACF,CAEA,GAAIX,KAAK,CAAG,CAAC,EAAIA,KAAK,CAAG,GAAG,CAAE,CAC5BtB,KAAK,CAACiC,KAAK,CAAC,gCAAgC,CAAC,CAC7C,OACF,CAEA,GAAI,CACF;AACA,KAAM,CAAAkB,mBAAmB,CAAG7C,YAAY,CAAC8C,IAAI,CAC3CC,CAAC,EAAIA,CAAC,CAACC,UAAU,GAAKpC,eAAe,EAAImC,CAAC,CAACE,SAAS,GAAKnC,cAC3D,CAAC,CAED,GAAI+B,mBAAmB,CAAE,CACvBnD,KAAK,CAACiC,KAAK,CAAC,sCAAsC,CAAC,CACnD,OACF,CAEA,KAAM,CAAAuB,gBAAgB,CAAGf,wBAAwB,CAAC,CAAC,CAEnD,KAAM,CAAEN,IAAI,CAAEF,KAAM,CAAC,CAAG,KAAM,CAAAlC,eAAe,CAACqC,QAAQ,CACnDC,IAAI,CAAC,cAAc,CAAC,CACpBoB,MAAM,CAAC,CAAC,CACPH,UAAU,CAAEpC,eAAe,CAC3BqC,SAAS,CAAEnC,cAAc,CACzBE,KAAK,CAAEA,KAAK,CACZoC,iBAAiB,CAAEF,gBAAgB,CACnCG,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAAC,CAAC,CACFvB,MAAM,CAAC,CAAC,CACRwB,MAAM,CAAC,CAAC,CAEX,GAAI7B,KAAK,CAAE,KAAM,CAAAA,KAAK,CAEtBjC,KAAK,CAAC+D,OAAO,CAAC,wBAAwB,CAAC,CACvC,KAAM,CAAAvC,QAAQ,CAAC,CAAC,CAEhB;AACAL,kBAAkB,CAAC,EAAE,CAAC,CACtBE,iBAAiB,CAAC,EAAE,CAAC,CACrBE,QAAQ,CAAC,EAAE,CAAC,CACZN,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,MAAOgB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDjC,KAAK,CAACiC,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAA+B,uBAAuB,CAAG,KAAO,CAAAC,aAAqB,EAAK,CAC/D,GAAI,CAACC,OAAO,CAAC,kCAAkC,CAAC,CAAE,OAElD,GAAI,CACF,KAAM,CAAEjC,KAAM,CAAC,CAAG,KAAM,CAAAlC,eAAe,CAACqC,QAAQ,CAC7CC,IAAI,CAAC,cAAc,CAAC,CACpB8B,MAAM,CAAC,CAAC,CACRC,EAAE,CAAC,IAAI,CAAEH,aAAa,CAAC,CAE1B,GAAIhC,KAAK,CAAE,KAAM,CAAAA,KAAK,CAEtBjC,KAAK,CAAC+D,OAAO,CAAC,sBAAsB,CAAC,CACrC,KAAM,CAAAvC,QAAQ,CAAC,CAAC,CAClB,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDjC,KAAK,CAACiC,KAAK,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAoC,sBAAsB,CAAG,KAAO,CAAAC,WAAwB,EAAK,CACjE,GAAI,CACF;AACA;AACAtE,KAAK,CAAC+D,OAAO,CAAC,0BAA0B,CAAC,CAEzC;AACA;AACA;AACA;AAEF,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDjC,KAAK,CAACiC,KAAK,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAsC,oBAAoB,CAAGjE,YAAY,CAACkE,MAAM,CAACF,WAAW,EAAI,KAAAG,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC9D,KAAM,CAAAC,WAAW,CAAGlE,UAAU,CAACmE,WAAW,CAAC,CAAC,CAC5C,MACE,EAAAR,qBAAA,CAAAH,WAAW,CAAC9D,QAAQ,UAAAiE,qBAAA,kBAAAC,sBAAA,CAApBD,qBAAA,CAAsBS,IAAI,UAAAR,sBAAA,iBAA1BA,sBAAA,CAA4BO,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,KAAAL,sBAAA,CAC/DL,WAAW,CAAC9D,QAAQ,UAAAmE,sBAAA,kBAAAC,sBAAA,CAApBD,sBAAA,CAAsBS,WAAW,UAAAR,sBAAA,iBAAjCA,sBAAA,CAAmCK,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,KAAAH,oBAAA,CACtEP,WAAW,CAAC5D,OAAO,UAAAmE,oBAAA,kBAAAC,qBAAA,CAAnBD,oBAAA,CAAqBQ,KAAK,UAAAP,qBAAA,iBAA1BA,qBAAA,CAA4BG,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,KAAAD,qBAAA,CAC/DT,WAAW,CAACZ,iBAAiB,UAAAqB,qBAAA,iBAA7BA,qBAAA,CAA+BE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,EAEtE,CAAC,CAAC,CAEF,KAAM,CAAAM,aAAa,CAAIhE,KAAa,EAAK,CACvC,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,6BAA6B,CACrD,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,2BAA2B,CACnD,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,+BAA+B,CACvD,MAAO,yBAAyB,CAClC,CAAC,CAED,KAAM,CAAAiE,aAAa,CAAIjE,KAAa,EAAK,CACvC,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,OAAO,CAC/B,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,UAAU,CAClC,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,KAAK,CAC7B,MAAO,OAAO,CAChB,CAAC,CAED,GAAIV,OAAO,CAAE,CACX,mBACEV,IAAA,QAAKsF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDvF,IAAA,QAAKsF,SAAS,CAAC,mEAAmE,CAAM,CAAC,CACtF,CAAC,CAEV,CAEA,mBACEpF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBrF,KAAA,QAAKoF,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iFAAc,CAAI,CAAC,cACpEvF,IAAA,MAAGsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,0LAAkC,CAAG,CAAC,EACrE,CAAC,cACNrF,KAAA,WACEsF,OAAO,CAAEA,CAAA,GAAMzE,eAAe,CAAC,IAAI,CAAE,CACrCuE,SAAS,CAAC,gHAAgH,CAAAC,QAAA,eAE1HvF,IAAA,CAACZ,QAAQ,EAACkG,SAAS,CAAC,cAAc,CAAE,CAAC,+FAEvC,EAAQ,CAAC,EACN,CAAC,cAGNpF,KAAA,QAAKoF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvF,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCvF,IAAA,CAACL,UAAU,EAAC2F,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC7C,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uFAAe,CAAG,CAAC,cACxDvF,IAAA,MAAGsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEnF,YAAY,CAAC2C,MAAM,CAAI,CAAC,EACtE,CAAC,EACH,CAAC,CACH,CAAC,cAEN/C,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CvF,IAAA,CAACJ,eAAe,EAAC0F,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACnD,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2EAAa,CAAG,CAAC,cACtDvF,IAAA,MAAGsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5CnF,YAAY,CAACkE,MAAM,CAACnB,CAAC,EAAIA,CAAC,CAAC/B,KAAK,EAAI,EAAE,CAAC,CAAC2B,MAAM,CAC9C,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAEN/C,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvF,IAAA,CAACR,QAAQ,EAAC8F,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC7C,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uHAAsB,CAAG,CAAC,cAC/DvF,IAAA,MAAGsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5C,GAAI,CAAAE,GAAG,CAACrF,YAAY,CAACsF,GAAG,CAACvC,CAAC,EAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAACuC,IAAI,CACjD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAEN3F,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvF,IAAA,CAACP,YAAY,EAAC6F,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACjD,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,MAAGsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iFAAc,CAAG,CAAC,cACvDvF,IAAA,MAAGsF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5C,GAAI,CAAAE,GAAG,CAACrF,YAAY,CAACsF,GAAG,CAACvC,CAAC,EAAIA,CAAC,CAACE,SAAS,CAAC,CAAC,CAACsC,IAAI,CAChD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN3F,IAAA,QAAKsF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACvDrF,KAAA,QAAKoF,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBvF,IAAA,CAACT,mBAAmB,EAAC+F,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5GtF,IAAA,UACE4F,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2JAAmC,CAC/CC,KAAK,CAAElF,UAAW,CAClBmF,QAAQ,CAAGC,CAAC,EAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CR,SAAS,CAAC,uHAAuH,CAClI,CAAC,EACC,CAAC,CACH,CAAC,cAGNpF,KAAA,QAAKoF,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEvF,IAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BrF,KAAA,UAAOoF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDvF,IAAA,UAAOsF,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BrF,KAAA,OAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,sCAEhG,CAAI,CAAC,cACLvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,yDAEhG,CAAI,CAAC,cACLvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,2EAEhG,CAAI,CAAC,cACLvF,IAAA,OAAIsF,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,wDAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRvF,IAAA,UAAOsF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDlB,oBAAoB,CAACqB,GAAG,CAAEtB,WAAW,OAAA8B,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,oBACpCnG,KAAA,CAAChB,MAAM,CAACoH,EAAE,EAERC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBlB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5BvF,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,QAAKsF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCvF,IAAA,QAAKsF,SAAS,CAAC,wEAAwE,CAAAC,QAAA,cACrFvF,IAAA,CAACR,QAAQ,EAAC8F,SAAS,CAAC,0BAA0B,CAAE,CAAC,CAC9C,CAAC,CACH,CAAC,cACNpF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvF,IAAA,QAAKsF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C,EAAAW,sBAAA,CAAA9B,WAAW,CAAC9D,QAAQ,UAAA4F,sBAAA,iBAApBA,sBAAA,CAAsBlB,IAAI,GAAI,UAAU,CACtC,CAAC,cACN9E,KAAA,QAAKoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,2DACzB,EAAAY,sBAAA,CAAC/B,WAAW,CAAC9D,QAAQ,UAAA6F,sBAAA,iBAApBA,sBAAA,CAAsBjB,WAAW,EAC3C,CAAC,EACH,CAAC,EACH,CAAC,CACJ,CAAC,cACLhF,KAAA,OAAIoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBvF,IAAA,QAAKsF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAAa,qBAAA,CAC/ChC,WAAW,CAAC5D,OAAO,UAAA4F,qBAAA,iBAAnBA,qBAAA,CAAqBjB,KAAK,CACxB,CAAC,cACNnF,IAAA,QAAKsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAAc,qBAAA,CACnCjC,WAAW,CAAC5D,OAAO,UAAA6F,qBAAA,iBAAnBA,qBAAA,CAAqBK,KAAK,CACxB,CAAC,EACJ,CAAC,cACL1G,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCrF,KAAA,SAAMoF,SAAS,CAAE,2EAA2EF,aAAa,CAAChB,WAAW,CAAChD,KAAK,CAAC,EAAG,CAAAmE,QAAA,EAC5HnB,WAAW,CAAChD,KAAK,CAAC,MAAI,CAACiE,aAAa,CAACjB,WAAW,CAAChD,KAAK,CAAC,EACpD,CAAC,CACL,CAAC,cACLpB,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCvF,IAAA,QAAKsF,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC3EnB,WAAW,CAACZ,iBAAiB,CAC3B,CAAC,CACJ,CAAC,cACLxD,IAAA,OAAIsF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAC/DrF,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,CAACN,YAAY,EAAC4F,SAAS,CAAC,cAAc,CAAE,CAAC,CACxC,GAAI,CAAA5B,IAAI,CAACU,WAAW,CAACX,SAAS,CAAC,CAACkD,kBAAkB,CAAC,OAAO,CAAC,EACzD,CAAC,CACJ,CAAC,cACL3G,IAAA,OAAIsF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7DrF,KAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DvF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMrB,sBAAsB,CAACC,WAAW,CAAE,CACnDkB,SAAS,CAAC,qDAAqD,CAC/DH,KAAK,CAAC,oCAAW,CAAAI,QAAA,cAEjBvF,IAAA,CAACV,YAAY,EAACgG,SAAS,CAAC,SAAS,CAAE,CAAC,CAC9B,CAAC,cACTtF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAM1B,uBAAuB,CAACM,WAAW,CAACwC,EAAE,CAAE,CACvDtB,SAAS,CAAC,mDAAmD,CAC7DH,KAAK,CAAC,oBAAK,CAAAI,QAAA,cAEXvF,IAAA,CAACX,SAAS,EAACiG,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GA/DAlB,WAAW,CAACwC,EAgER,CAAC,EACb,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELvC,oBAAoB,CAACtB,MAAM,GAAK,CAAC,eAChC7C,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvF,IAAA,CAACb,eAAe,EAACmG,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACpEtF,IAAA,OAAIsF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4EAAc,CAAI,CAAC,cAC1EvF,IAAA,MAAGsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,kKAA8B,CAAG,CAAC,cACpErF,KAAA,WACEsF,OAAO,CAAEA,CAAA,GAAMzE,eAAe,CAAC,IAAI,CAAE,CACrCuE,SAAS,CAAC,gHAAgH,CAAAC,QAAA,eAE1HvF,IAAA,CAACZ,QAAQ,EAACkG,SAAS,CAAC,cAAc,CAAE,CAAC,gEAEvC,EAAQ,CAAC,EACN,CACN,EACE,CAAC,CAGLxE,YAAY,eACXd,IAAA,QAAKsF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FrF,KAAA,CAAChB,MAAM,CAAC2H,GAAG,EACTN,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEM,KAAK,CAAE,IAAK,CAAE,CACrCL,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEM,KAAK,CAAE,CAAE,CAAE,CAClCxB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEnDvF,IAAA,OAAIsF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,8FAAiB,CAAI,CAAC,cAE7ErF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRrF,KAAA,WACE4F,KAAK,CAAE9E,eAAgB,CACvB+E,QAAQ,CAAGC,CAAC,EAAK/E,kBAAkB,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDR,SAAS,CAAC,iHAAiH,CAAAC,QAAA,eAE3HvF,IAAA,WAAQ8F,KAAK,CAAC,EAAE,CAAAP,QAAA,CAAC,yDAAe,CAAQ,CAAC,CACxCjF,QAAQ,CAACoF,GAAG,CAAEqB,OAAO,eACpB7G,KAAA,WAAyB4F,KAAK,CAAEiB,OAAO,CAACH,EAAG,CAAArB,QAAA,EACxCwB,OAAO,CAAC/B,IAAI,CAAC,IAAE,CAAC+B,OAAO,CAAC7B,WAAW,CAAC,GACvC,GAFa6B,OAAO,CAACH,EAEb,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN1G,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+DAEhE,CAAO,CAAC,cACRrF,KAAA,WACE4F,KAAK,CAAE5E,cAAe,CACtB6E,QAAQ,CAAGC,CAAC,EAAK7E,iBAAiB,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDR,SAAS,CAAC,iHAAiH,CAAAC,QAAA,eAE3HvF,IAAA,WAAQ8F,KAAK,CAAC,EAAE,CAAAP,QAAA,CAAC,yDAAe,CAAQ,CAAC,CACxC/E,OAAO,CAACkF,GAAG,CAAEsB,MAAM,eAClBhH,IAAA,WAAwB8F,KAAK,CAAEkB,MAAM,CAACJ,EAAG,CAAArB,QAAA,CACtCyB,MAAM,CAAC7B,KAAK,EADF6B,MAAM,CAACJ,EAEZ,CACT,CAAC,EACI,CAAC,EACN,CAAC,cAEN1G,KAAA,QAAAqF,QAAA,eACEvF,IAAA,UAAOsF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,0CAEhE,CAAO,CAAC,cACRvF,IAAA,UACE4F,IAAI,CAAC,QAAQ,CACbqB,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTpB,KAAK,CAAE1E,KAAM,CACb2E,QAAQ,CAAGC,CAAC,EAAK3E,QAAQ,CAAC8F,QAAQ,CAACnB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAAI,CAAC,CAAE,CACzDR,SAAS,CAAC,iHAAiH,CAC5H,CAAC,EACC,CAAC,EACH,CAAC,cAENpF,KAAA,QAAKoF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DvF,IAAA,WACEwF,OAAO,CAAEA,CAAA,GAAMzE,eAAe,CAAC,KAAK,CAAE,CACtCuE,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAC/F,gCAED,CAAQ,CAAC,cACTvF,IAAA,WACEwF,OAAO,CAAExC,sBAAuB,CAChCsC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,CAClG,2EAED,CAAQ,CAAC,EACN,CAAC,EACI,CAAC,CACV,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAApF,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}