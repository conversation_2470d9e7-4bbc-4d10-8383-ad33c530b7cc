{"ast": null, "code": "import React,{useState,useEffect}from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Toaster}from'react-hot-toast';// Services\nimport{authService}from'./services/authService';// Components\nimport LoadingSpinner from'./components/common/LoadingSpinner';import LoginPage from'./pages/LoginPage';import AdminDashboard from'./pages/admin/AdminDashboard';import StudentDashboard from'./pages/student/StudentDashboard';import AIAssistant from'./components/AIAssistant/AIAssistant';import SessionWarning from'./components/common/SessionWarning';import{NotificationProvider,ToastNotifications}from'./components/common/NotificationSystem';// Types\n// Styles\nimport'./App.css';// Helper functions to check user types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const isAdmin=user=>{return user.role==='admin';};const isStudent=user=>{return user.role==='student';};function App(){const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[firebaseUser,setFirebaseUser]=useState(null);useEffect(()=>{const unsubscribe=authService.onAuthStateChange(async firebaseUser=>{setFirebaseUser(firebaseUser);if(firebaseUser){// User is signed in, get user data\ntry{// This would be implemented based on your auth logic\n// For now, we'll handle it in the login components\n}catch(error){console.error('Error getting user data:',error);}}else{setUser(null);}setLoading(false);});return()=>unsubscribe();},[]);const handleLogin=userData=>{setUser(userData);};const handleLogout=async()=>{try{await authService.logout();setUser(null);}catch(error){console.error('Logout error:',error);}};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{});}return/*#__PURE__*/_jsx(NotificationProvider,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App min-h-screen bg-gray-50\",dir:\"rtl\",children:/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:user?/*#__PURE__*/_jsx(Navigate,{to:user.role==='admin'?'/admin':'/student',replace:true}):/*#__PURE__*/_jsx(LoginPage,{onLogin:handleLogin})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/*\",element:user&&isAdmin(user)?/*#__PURE__*/_jsx(AdminDashboard,{user:user,onLogout:handleLogout}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/student/*\",element:user&&isStudent(user)?/*#__PURE__*/_jsx(StudentDashboard,{user:user,onLogout:handleLogout}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:user?user.role==='admin'?'/admin':'/student':'/login',replace:true})})]}),user&&user.role==='student'&&/*#__PURE__*/_jsx(AIAssistant,{}),/*#__PURE__*/_jsx(Toaster,{position:\"top-center\",toastOptions:{duration:4000,style:{background:'#363636',color:'#fff',fontFamily:'Cairo, sans-serif',direction:'rtl'},success:{style:{background:'#10b981'}},error:{style:{background:'#ef4444'}}}}),/*#__PURE__*/_jsx(SessionWarning,{}),/*#__PURE__*/_jsx(ToastNotifications,{})]})})});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "authService", "LoadingSpinner", "LoginPage", "AdminDashboard", "StudentDashboard", "AIAssistant", "SessionWarning", "NotificationProvider", "ToastNotifications", "jsx", "_jsx", "jsxs", "_jsxs", "isAdmin", "user", "role", "isStudent", "App", "setUser", "loading", "setLoading", "firebaseUser", "setFirebaseUser", "unsubscribe", "onAuthStateChange", "error", "console", "handleLogin", "userData", "handleLogout", "logout", "children", "className", "dir", "path", "element", "to", "replace", "onLogin", "onLogout", "position", "toastOptions", "duration", "style", "background", "color", "fontFamily", "direction", "success"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { User as FirebaseUser } from 'firebase/auth';\n\n// Services\nimport { authService } from './services/authService';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport LoginPage from './pages/LoginPage';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AIAssistant from './components/AIAssistant/AIAssistant';\nimport SessionWarning from './components/common/SessionWarning';\nimport { NotificationProvider, ToastNotifications } from './components/common/NotificationSystem';\n\n// Types\nimport { User } from './types';\n\n// Styles\nimport './App.css';\n\n// Helper functions to check user types\nconst isAdmin = (user: User): boolean => {\n  return user.role === 'admin';\n};\n\nconst isStudent = (user: User): boolean => {\n  return user.role === 'student';\n};\n\nfunction App() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n\n  useEffect(() => {\n    const unsubscribe = authService.onAuthStateChange(async (firebaseUser) => {\n      setFirebaseUser(firebaseUser);\n      \n      if (firebaseUser) {\n        // User is signed in, get user data\n        try {\n          // This would be implemented based on your auth logic\n          // For now, we'll handle it in the login components\n        } catch (error) {\n          console.error('Error getting user data:', error);\n        }\n      } else {\n        setUser(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const handleLogin = (userData: User) => {\n    setUser(userData);\n  };\n\n  const handleLogout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  return (\n    <NotificationProvider>\n      <div className=\"App min-h-screen bg-gray-50\" dir=\"rtl\">\n        <Router>\n        <Routes>\n          {/* Public Routes */}\n          <Route \n            path=\"/login\" \n            element={\n              user ? (\n                <Navigate to={user.role === 'admin' ? '/admin' : '/student'} replace />\n              ) : (\n                <LoginPage onLogin={handleLogin} />\n              )\n            } \n          />\n          \n          {/* Protected Admin Routes */}\n          <Route\n            path=\"/admin/*\"\n            element={\n              user && isAdmin(user) ? (\n                <AdminDashboard user={user as any} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n\n          {/* Protected Student Routes */}\n          <Route\n            path=\"/student/*\"\n            element={\n              user && isStudent(user) ? (\n                <StudentDashboard user={user as any} onLogout={handleLogout} />\n              ) : (\n                <Navigate to=\"/login\" replace />\n              )\n            }\n          />\n          \n          {/* Default Route */}\n          <Route \n            path=\"/\" \n            element={\n              <Navigate to={\n                user \n                  ? (user.role === 'admin' ? '/admin' : '/student')\n                  : '/login'\n              } replace />\n            } \n          />\n        </Routes>\n        \n        {/* AI Assistant - Available for students */}\n        {user && user.role === 'student' && <AIAssistant />}\n        \n        {/* Toast Notifications */}\n        <Toaster\n          position=\"top-center\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n              fontFamily: 'Cairo, sans-serif',\n              direction: 'rtl'\n            },\n            success: {\n              style: {\n                background: '#10b981',\n              },\n            },\n            error: {\n              style: {\n                background: '#ef4444',\n              },\n            },\n          }}\n        />\n\n        {/* Session Warning */}\n        <SessionWarning />\n\n        {/* Toast Notifications */}\n        <ToastNotifications />\n      </Router>\n    </div>\n    </NotificationProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,OAAO,KAAQ,iBAAiB,CAGzC;AACA,OAASC,WAAW,KAAQ,wBAAwB,CAEpD;AACA,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAC/D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,MAAO,CAAAC,WAAW,KAAM,sCAAsC,CAC9D,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAC/D,OAASC,oBAAoB,CAAEC,kBAAkB,KAAQ,wCAAwC,CAEjG;AAGA;AACA,MAAO,WAAW,CAElB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,OAAO,CAAIC,IAAU,EAAc,CACvC,MAAO,CAAAA,IAAI,CAACC,IAAI,GAAK,OAAO,CAC9B,CAAC,CAED,KAAM,CAAAC,SAAS,CAAIF,IAAU,EAAc,CACzC,MAAO,CAAAA,IAAI,CAACC,IAAI,GAAK,SAAS,CAChC,CAAC,CAED,QAAS,CAAAE,GAAGA,CAAA,CAAG,CACb,KAAM,CAACH,IAAI,CAAEI,OAAO,CAAC,CAAG1B,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAsB,IAAI,CAAC,CAE3EC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8B,WAAW,CAAGvB,WAAW,CAACwB,iBAAiB,CAAC,KAAO,CAAAH,YAAY,EAAK,CACxEC,eAAe,CAACD,YAAY,CAAC,CAE7B,GAAIA,YAAY,CAAE,CAChB;AACA,GAAI,CACF;AACA;AAAA,CACA,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CAAC,IAAM,CACLP,OAAO,CAAC,IAAI,CAAC,CACf,CAEAE,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CAEF,MAAO,IAAMG,WAAW,CAAC,CAAC,CAC5B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,WAAW,CAAIC,QAAc,EAAK,CACtCV,OAAO,CAACU,QAAQ,CAAC,CACnB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAA7B,WAAW,CAAC8B,MAAM,CAAC,CAAC,CAC1BZ,OAAO,CAAC,IAAI,CAAC,CACf,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,GAAIN,OAAO,CAAE,CACX,mBAAOT,IAAA,CAACT,cAAc,GAAE,CAAC,CAC3B,CAEA,mBACES,IAAA,CAACH,oBAAoB,EAAAwB,QAAA,cACnBrB,IAAA,QAAKsB,SAAS,CAAC,6BAA6B,CAACC,GAAG,CAAC,KAAK,CAAAF,QAAA,cACpDnB,KAAA,CAACjB,MAAM,EAAAoC,QAAA,eACPnB,KAAA,CAAChB,MAAM,EAAAmC,QAAA,eAELrB,IAAA,CAACb,KAAK,EACJqC,IAAI,CAAC,QAAQ,CACbC,OAAO,CACLrB,IAAI,cACFJ,IAAA,CAACZ,QAAQ,EAACsC,EAAE,CAAEtB,IAAI,CAACC,IAAI,GAAK,OAAO,CAAG,QAAQ,CAAG,UAAW,CAACsB,OAAO,MAAE,CAAC,cAEvE3B,IAAA,CAACR,SAAS,EAACoC,OAAO,CAAEX,WAAY,CAAE,CAErC,CACF,CAAC,cAGFjB,IAAA,CAACb,KAAK,EACJqC,IAAI,CAAC,UAAU,CACfC,OAAO,CACLrB,IAAI,EAAID,OAAO,CAACC,IAAI,CAAC,cACnBJ,IAAA,CAACP,cAAc,EAACW,IAAI,CAAEA,IAAY,CAACyB,QAAQ,CAAEV,YAAa,CAAE,CAAC,cAE7DnB,IAAA,CAACZ,QAAQ,EAACsC,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAElC,CACF,CAAC,cAGF3B,IAAA,CAACb,KAAK,EACJqC,IAAI,CAAC,YAAY,CACjBC,OAAO,CACLrB,IAAI,EAAIE,SAAS,CAACF,IAAI,CAAC,cACrBJ,IAAA,CAACN,gBAAgB,EAACU,IAAI,CAAEA,IAAY,CAACyB,QAAQ,CAAEV,YAAa,CAAE,CAAC,cAE/DnB,IAAA,CAACZ,QAAQ,EAACsC,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAElC,CACF,CAAC,cAGF3B,IAAA,CAACb,KAAK,EACJqC,IAAI,CAAC,GAAG,CACRC,OAAO,cACLzB,IAAA,CAACZ,QAAQ,EAACsC,EAAE,CACVtB,IAAI,CACCA,IAAI,CAACC,IAAI,GAAK,OAAO,CAAG,QAAQ,CAAG,UAAU,CAC9C,QACL,CAACsB,OAAO,MAAE,CACZ,CACF,CAAC,EACI,CAAC,CAGRvB,IAAI,EAAIA,IAAI,CAACC,IAAI,GAAK,SAAS,eAAIL,IAAA,CAACL,WAAW,GAAE,CAAC,cAGnDK,IAAA,CAACX,OAAO,EACNyC,QAAQ,CAAC,YAAY,CACrBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,SAAS,CACrBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,mBAAmB,CAC/BC,SAAS,CAAE,KACb,CAAC,CACDC,OAAO,CAAE,CACPL,KAAK,CAAE,CACLC,UAAU,CAAE,SACd,CACF,CAAC,CACDnB,KAAK,CAAE,CACLkB,KAAK,CAAE,CACLC,UAAU,CAAE,SACd,CACF,CACF,CAAE,CACH,CAAC,cAGFlC,IAAA,CAACJ,cAAc,GAAE,CAAC,cAGlBI,IAAA,CAACF,kBAAkB,GAAE,CAAC,EAChB,CAAC,CACN,CAAC,CACgB,CAAC,CAE3B,CAEA,cAAe,CAAAS,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}