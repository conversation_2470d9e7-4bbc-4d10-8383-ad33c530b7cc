{"ast": null, "code": "import React,{useState}from'react';import{motion,AnimatePresence}from'framer-motion';import{Bars3Icon,UserCircleIcon,ArrowRightOnRectangleIcon,CogIcon}from'@heroicons/react/24/outline';// Types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentHeader=_ref=>{let{user,onMenuClick,onLogout}=_ref;const[showProfileMenu,setShowProfileMenu]=useState(false);return/*#__PURE__*/_jsxs(\"header\",{className:\"bg-white shadow-sm border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between px-6 py-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onMenuClick,className:\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",children:/*#__PURE__*/_jsx(Bars3Icon,{className:\"w-6 h-6\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden lg:block\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:[\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \",user.name||'الطالب']}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u0627\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u0645 \\u0645\\u0639 \\u0645\\u0646\\u0635\\u0629 ALaa Abd Hamied | \\u0641\\u0631\\u064A\\u0642 ALaa Abd Elhamied 2025\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 space-x-reverse\",children:[/*#__PURE__*/_jsx(NotificationBell,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowProfileMenu(!showProfileMenu),className:\"flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserCircleIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden md:block text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium\",children:user.name||'الطالب'}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[\"\\u0643\\u0648\\u062F: \",user.accessCode]})]})]}),/*#__PURE__*/_jsx(AnimatePresence,{children:showProfileMenu&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:\"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"py-2\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",children:[/*#__PURE__*/_jsx(UserCircleIcon,{className:\"w-4 h-4 ml-2\"}),\"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"w-4 h-4 ml-2\"}),\"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"]}),/*#__PURE__*/_jsx(\"hr\",{className:\"my-2\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:onLogout,className:\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",children:[/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"w-4 h-4 ml-2\"}),\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"]})]})})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:hidden px-6 pb-4\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-xl font-bold text-gray-900\",children:[\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \",user.name||'الطالب']}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644: \",user.accessCode]})]}),showProfileMenu&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40\",onClick:()=>setShowProfileMenu(false)})]});};export default StudentHeader;", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Bars3Icon", "UserCircleIcon", "ArrowRightOnRectangleIcon", "CogIcon", "jsx", "_jsx", "jsxs", "_jsxs", "StudentHeader", "_ref", "user", "onMenuClick", "onLogout", "showProfileMenu", "setShowProfileMenu", "className", "children", "onClick", "name", "NotificationBell", "accessCode", "div", "initial", "opacity", "y", "animate", "exit"], "sources": ["C:/Users/<USER>/Desktop/مشروع/src/components/Student/StudentHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  UserCircleIcon,\n  ArrowRightOnRectangleIcon,\n  CogIcon\n} from '@heroicons/react/24/outline';\n\n// Types\nimport { Student } from '../../types';\n\ninterface StudentHeaderProps {\n  user: Student;\n  onMenuClick: () => void;\n  onLogout: () => void;\n}\n\nconst StudentHeader: React.FC<StudentHeaderProps> = ({ user, onMenuClick, onLogout }) => {\n  const [showProfileMenu, setShowProfileMenu] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"flex items-center justify-between px-6 py-4\">\n        {/* Left Side - Menu Button */}\n        <div className=\"flex items-center\">\n          <button\n            onClick={onMenuClick}\n            className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <Bars3Icon className=\"w-6 h-6\" />\n          </button>\n          \n          <div className=\"hidden lg:block\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              مرحباً، {user.name || 'الطالب'}\n            </h1>\n            <p className=\"text-sm text-gray-500\">\n              استمر في رحلة التعلم مع منصة ALaa Abd Hamied | فريق ALaa Abd Elhamied 2025\n            </p>\n          </div>\n        </div>\n\n        {/* Right Side - Profile */}\n        <div className=\"flex items-center space-x-4 space-x-reverse\">\n          {/* Notifications */}\n          <NotificationBell />\n\n          {/* Profile Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowProfileMenu(!showProfileMenu)}\n              className=\"flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n            >\n              <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\">\n                <UserCircleIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium\">{user.name || 'الطالب'}</p>\n                <p className=\"text-xs text-gray-500\">كود: {user.accessCode}</p>\n              </div>\n            </button>\n\n            <AnimatePresence>\n              {showProfileMenu && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  className=\"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50\"\n                >\n                  <div className=\"py-2\">\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      <UserCircleIcon className=\"w-4 h-4 ml-2\" />\n                      الملف الشخصي\n                    </button>\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      <CogIcon className=\"w-4 h-4 ml-2\" />\n                      الإعدادات\n                    </button>\n                    <hr className=\"my-2\" />\n                    <button\n                      onClick={onLogout}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <ArrowRightOnRectangleIcon className=\"w-4 h-4 ml-2\" />\n                      تسجيل الخروج\n                    </button>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Title */}\n      <div className=\"lg:hidden px-6 pb-4\">\n        <h1 className=\"text-xl font-bold text-gray-900\">\n          مرحباً، {user.name || 'الطالب'}\n        </h1>\n        <p className=\"text-sm text-gray-500\">\n          كود الدخول: {user.accessCode}\n        </p>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showProfileMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowProfileMenu(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default StudentHeader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,SAAS,CACTC,cAAc,CACdC,yBAAyB,CACzBC,OAAO,KACF,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASA,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAAqC,IAApC,CAAEC,IAAI,CAAEC,WAAW,CAAEC,QAAS,CAAC,CAAAH,IAAA,CAClF,KAAM,CAACI,eAAe,CAAEC,kBAAkB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAE7D,mBACEU,KAAA,WAAQQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC7DT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1DT,KAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCX,IAAA,WACEY,OAAO,CAAEN,WAAY,CACrBI,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAExFX,IAAA,CAACL,SAAS,EAACe,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,cAETR,KAAA,QAAKQ,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BT,KAAA,OAAIQ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,6CACvC,CAACN,IAAI,CAACQ,IAAI,EAAI,QAAQ,EAC5B,CAAC,cACLb,IAAA,MAAGU,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mNAErC,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAGNT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1DX,IAAA,CAACc,gBAAgB,GAAE,CAAC,cAGpBZ,KAAA,QAAKQ,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBT,KAAA,WACEU,OAAO,CAAEA,CAAA,GAAMH,kBAAkB,CAAC,CAACD,eAAe,CAAE,CACpDE,SAAS,CAAC,2HAA2H,CAAAC,QAAA,eAErIX,IAAA,QAAKU,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFX,IAAA,CAACJ,cAAc,EAACc,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC9C,CAAC,cACNR,KAAA,QAAKQ,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCX,IAAA,MAAGU,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEN,IAAI,CAACQ,IAAI,EAAI,QAAQ,CAAI,CAAC,cAC9DX,KAAA,MAAGQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,sBAAK,CAACN,IAAI,CAACU,UAAU,EAAI,CAAC,EAC5D,CAAC,EACA,CAAC,cAETf,IAAA,CAACN,eAAe,EAAAiB,QAAA,CACbH,eAAe,eACdR,IAAA,CAACP,MAAM,CAACuB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC5BT,SAAS,CAAC,qFAAqF,CAAAC,QAAA,cAE/FT,KAAA,QAAKQ,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBT,KAAA,WAAQQ,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eAC5FX,IAAA,CAACJ,cAAc,EAACc,SAAS,CAAC,cAAc,CAAE,CAAC,sEAE7C,EAAQ,CAAC,cACTR,KAAA,WAAQQ,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eAC5FX,IAAA,CAACF,OAAO,EAACY,SAAS,CAAC,cAAc,CAAE,CAAC,yDAEtC,EAAQ,CAAC,cACTV,IAAA,OAAIU,SAAS,CAAC,MAAM,CAAE,CAAC,cACvBR,KAAA,WACEU,OAAO,CAAEL,QAAS,CAClBG,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAEnFX,IAAA,CAACH,yBAAyB,EAACa,SAAS,CAAC,cAAc,CAAE,CAAC,sEAExD,EAAQ,CAAC,EACN,CAAC,CACI,CACb,CACc,CAAC,EACf,CAAC,EACH,CAAC,EACH,CAAC,cAGNR,KAAA,QAAKQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCT,KAAA,OAAIQ,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAAC,6CACtC,CAACN,IAAI,CAACQ,IAAI,EAAI,QAAQ,EAC5B,CAAC,cACLX,KAAA,MAAGQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,2DACvB,CAACN,IAAI,CAACU,UAAU,EAC3B,CAAC,EACD,CAAC,CAGLP,eAAe,eACdR,IAAA,QACEU,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAMH,kBAAkB,CAAC,KAAK,CAAE,CAC1C,CACF,EACK,CAAC,CAEb,CAAC,CAED,cAAe,CAAAN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}