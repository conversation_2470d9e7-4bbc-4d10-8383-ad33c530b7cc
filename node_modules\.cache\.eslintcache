[{"C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts": "3", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx": "4", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx": "7", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx": "8", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts": "9", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx": "13", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx": "15", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx": "16", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts": "17", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx": "18", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx": "20", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx": "21", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx": "22", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx": "23", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx": "24", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx": "25", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx": "26", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx": "29", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx": "30", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx": "31", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx": "32", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx": "33", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts": "34", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts": "35", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\supabaseService.ts": "36", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\supabase.ts": "37", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddCourseModal.tsx": "38", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddStudentModal.tsx": "39", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditStudentModal.tsx": "40", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditCourseModal.tsx": "41", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveCard.tsx": "42", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveGrid.tsx": "43", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveButton.tsx": "44", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveText.tsx": "45", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\NotificationSystem.tsx": "46", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\SessionWarning.tsx": "47", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\hooks\\useSessionPersistence.ts": "48", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\ProgressiveCourseViewer.tsx": "49", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CourseEnrollmentManagement.tsx": "50", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdvancedCertificatesManagement.tsx": "51", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\VideoManagement.tsx": "52", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\EditableStudentProfile.tsx": "53", "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\RealTimeStudentOverview.tsx": "54"}, {"size": 287, "mtime": 1752677446241, "results": "55", "hashOfConfig": "56"}, {"size": 4540, "mtime": 1752713704172, "results": "57", "hashOfConfig": "56"}, {"size": 5809, "mtime": 1752713292061, "results": "58", "hashOfConfig": "56"}, {"size": 11174, "mtime": 1752712738911, "results": "59", "hashOfConfig": "56"}, {"size": 2907, "mtime": 1752714066644, "results": "60", "hashOfConfig": "56"}, {"size": 3238, "mtime": 1752712282861, "results": "61", "hashOfConfig": "56"}, {"size": 1550, "mtime": 1752676894244, "results": "62", "hashOfConfig": "56"}, {"size": 25700, "mtime": 1752693728641, "results": "63", "hashOfConfig": "56"}, {"size": 833, "mtime": 1752681975589, "results": "64", "hashOfConfig": "56"}, {"size": 7357, "mtime": 1752708812641, "results": "65", "hashOfConfig": "56"}, {"size": 10172, "mtime": 1752677555299, "results": "66", "hashOfConfig": "56"}, {"size": 4452, "mtime": 1752715494273, "results": "67", "hashOfConfig": "56"}, {"size": 7678, "mtime": 1752682443047, "results": "68", "hashOfConfig": "56"}, {"size": 6169, "mtime": 1752711738135, "results": "69", "hashOfConfig": "56"}, {"size": 8039, "mtime": 1752708757961, "results": "70", "hashOfConfig": "56"}, {"size": 9336, "mtime": 1752684234560, "results": "71", "hashOfConfig": "56"}, {"size": 8385, "mtime": 1752676831470, "results": "72", "hashOfConfig": "56"}, {"size": 4977, "mtime": 1752677160727, "results": "73", "hashOfConfig": "56"}, {"size": 2740, "mtime": 1752677099694, "results": "74", "hashOfConfig": "56"}, {"size": 3926, "mtime": 1752677124767, "results": "75", "hashOfConfig": "56"}, {"size": 8369, "mtime": 1752677265900, "results": "76", "hashOfConfig": "56"}, {"size": 5660, "mtime": 1752677299906, "results": "77", "hashOfConfig": "56"}, {"size": 10600, "mtime": 1752685683050, "results": "78", "hashOfConfig": "56"}, {"size": 15419, "mtime": 1752687029487, "results": "79", "hashOfConfig": "56"}, {"size": 9659, "mtime": 1752714301068, "results": "80", "hashOfConfig": "56"}, {"size": 10091, "mtime": 1752707036293, "results": "81", "hashOfConfig": "56"}, {"size": 5141, "mtime": 1752680890303, "results": "82", "hashOfConfig": "56"}, {"size": 10772, "mtime": 1752693802266, "results": "83", "hashOfConfig": "56"}, {"size": 10901, "mtime": 1752693192972, "results": "84", "hashOfConfig": "56"}, {"size": 11787, "mtime": 1752710776013, "results": "85", "hashOfConfig": "56"}, {"size": 11382, "mtime": 1752681329670, "results": "86", "hashOfConfig": "56"}, {"size": 8199, "mtime": 1752681362339, "results": "87", "hashOfConfig": "56"}, {"size": 15051, "mtime": 1752684390342, "results": "88", "hashOfConfig": "56"}, {"size": 625, "mtime": 1752693499027, "results": "89", "hashOfConfig": "56"}, {"size": 9649, "mtime": 1752715200924, "results": "90", "hashOfConfig": "56"}, {"size": 15963, "mtime": 1752711803035, "results": "91", "hashOfConfig": "56"}, {"size": 1613, "mtime": 1752685035924, "results": "92", "hashOfConfig": "56"}, {"size": 9036, "mtime": 1752686724623, "results": "93", "hashOfConfig": "56"}, {"size": 7840, "mtime": 1752685885732, "results": "94", "hashOfConfig": "56"}, {"size": 7935, "mtime": 1752685920414, "results": "95", "hashOfConfig": "56"}, {"size": 9770, "mtime": 1752686944770, "results": "96", "hashOfConfig": "56"}, {"size": 2214, "mtime": 1752708896031, "results": "97", "hashOfConfig": "56"}, {"size": 2052, "mtime": 1752692149480, "results": "98", "hashOfConfig": "56"}, {"size": 3349, "mtime": 1752708927929, "results": "99", "hashOfConfig": "56"}, {"size": 1810, "mtime": 1752692164556, "results": "100", "hashOfConfig": "56"}, {"size": 13589, "mtime": 1752713623232, "results": "101", "hashOfConfig": "56"}, {"size": 4592, "mtime": 1752712618233, "results": "102", "hashOfConfig": "56"}, {"size": 6103, "mtime": 1752712592177, "results": "103", "hashOfConfig": "56"}, {"size": 18186, "mtime": 1752714934767, "results": "104", "hashOfConfig": "56"}, {"size": 17836, "mtime": 1752714255451, "results": "105", "hashOfConfig": "56"}, {"size": 19746, "mtime": 1752714181023, "results": "106", "hashOfConfig": "56"}, {"size": 24266, "mtime": 1752714044239, "results": "107", "hashOfConfig": "56"}, {"size": 15051, "mtime": 1752714711247, "results": "108", "hashOfConfig": "56"}, {"size": 19201, "mtime": 1752714493086, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbgu1t", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\App.tsx", ["272"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\authService.ts", ["273", "274"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\student\\StudentDashboard.tsx", ["275"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\AIAssistant\\AIAssistant.tsx", ["276", "277", "278"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\DashboardOverview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoriesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\courseService.ts", ["279"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CategoryModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CoursesManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\StudentsManagement.tsx", ["280"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\QuizzesManagement.tsx", ["281", "282", "283", "284", "285", "286"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CertificatesManagement.tsx", ["287", "288"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\SettingsPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCourses.tsx", ["289", "290", "291", "292"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\CourseViewer.tsx", ["293", "294", "295", "296", "297", "298", "299", "300", "301", "302"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\QuizPage.tsx", ["303"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\MyCertificates.tsx", ["304"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\StudentProfile.tsx", ["305", "306"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\data\\defaultAdmin.ts", ["307", "308"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\dataService.ts", ["309", "310", "311", "312", "313", "314", "315", "316"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\services\\supabaseService.ts", ["317", "318", "319", "320", "321", "322"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\config\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddCourseModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\AddStudentModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditStudentModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\modals\\EditCourseModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\ResponsiveText.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\common\\SessionWarning.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\hooks\\useSessionPersistence.ts", ["323"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\ProgressiveCourseViewer.tsx", ["324", "325", "326"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\CourseEnrollmentManagement.tsx", ["327"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\AdvancedCertificatesManagement.tsx", ["328", "329", "330", "331"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Admin\\VideoManagement.tsx", ["332"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\EditableStudentProfile.tsx", ["333", "334"], [], "C:\\Users\\<USER>\\Desktop\\مشروع\\src\\components\\Student\\RealTimeStudentOverview.tsx", ["335", "336", "337", "338", "339", "340", "341"], [], {"ruleId": "342", "severity": 1, "message": "343", "line": 36, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 36, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "346", "line": 2, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 29}, {"ruleId": "342", "severity": 1, "message": "347", "line": 8, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 8, "endColumn": 14}, {"ruleId": "342", "severity": 1, "message": "348", "line": 10, "column": 8, "nodeType": "344", "messageId": "345", "endLine": 10, "endColumn": 20}, {"ruleId": "342", "severity": 1, "message": "349", "line": 4, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 4, "endColumn": 26}, {"ruleId": "342", "severity": 1, "message": "350", "line": 7, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 7, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "351", "line": 11, "column": 26, "nodeType": "344", "messageId": "345", "endLine": 11, "endColumn": 43}, {"ruleId": "342", "severity": 1, "message": "352", "line": 14, "column": 44, "nodeType": "344", "messageId": "345", "endLine": 14, "endColumn": 56}, {"ruleId": "342", "severity": 1, "message": "353", "line": 15, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 15, "endColumn": 21}, {"ruleId": "342", "severity": 1, "message": "354", "line": 24, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 24, "endColumn": 17}, {"ruleId": "342", "severity": 1, "message": "355", "line": 25, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 25, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "356", "line": 26, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 26, "endColumn": 23}, {"ruleId": "342", "severity": 1, "message": "357", "line": 27, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 27, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "358", "line": 104, "column": 9, "nodeType": "344", "messageId": "345", "endLine": 104, "endColumn": 24}, {"ruleId": "342", "severity": 1, "message": "359", "line": 110, "column": 9, "nodeType": "344", "messageId": "345", "endLine": 110, "endColumn": 26}, {"ruleId": "360", "severity": 1, "message": "361", "line": 28, "column": 6, "nodeType": "362", "endLine": 28, "endColumn": 8, "suggestions": "363"}, {"ruleId": "360", "severity": 1, "message": "364", "line": 85, "column": 6, "nodeType": "362", "endLine": 85, "endColumn": 8, "suggestions": "365"}, {"ruleId": "342", "severity": 1, "message": "366", "line": 2, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 16}, {"ruleId": "342", "severity": 1, "message": "367", "line": 7, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 7, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "368", "line": 27, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 27, "endColumn": 16}, {"ruleId": "360", "severity": 1, "message": "369", "line": 43, "column": 6, "nodeType": "362", "endLine": 43, "endColumn": 15, "suggestions": "370"}, {"ruleId": "342", "severity": 1, "message": "371", "line": 11, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 11, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "372", "line": 12, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 12, "endColumn": 18}, {"ruleId": "342", "severity": 1, "message": "373", "line": 13, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 13, "endColumn": 12}, {"ruleId": "342", "severity": 1, "message": "374", "line": 18, "column": 8, "nodeType": "344", "messageId": "345", "endLine": 18, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "375", "line": 33, "column": 9, "nodeType": "344", "messageId": "345", "endLine": 33, "endColumn": 17}, {"ruleId": "342", "severity": 1, "message": "354", "line": 38, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 38, "endColumn": 17}, {"ruleId": "342", "severity": 1, "message": "376", "line": 39, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 39, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "377", "line": 40, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 40, "endColumn": 23}, {"ruleId": "342", "severity": 1, "message": "378", "line": 40, "column": 25, "nodeType": "344", "messageId": "345", "endLine": 40, "endColumn": 41}, {"ruleId": "360", "severity": 1, "message": "379", "line": 46, "column": 6, "nodeType": "362", "endLine": 46, "endColumn": 16, "suggestions": "380"}, {"ruleId": "342", "severity": 1, "message": "381", "line": 22, "column": 20, "nodeType": "344", "messageId": "345", "endLine": 22, "endColumn": 31}, {"ruleId": "342", "severity": 1, "message": "382", "line": 1, "column": 17, "nodeType": "344", "messageId": "345", "endLine": 1, "endColumn": 25}, {"ruleId": "342", "severity": 1, "message": "383", "line": 5, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 5, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "384", "line": 6, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 6, "endColumn": 10}, {"ruleId": "342", "severity": 1, "message": "347", "line": 1, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 1, "endColumn": 14}, {"ruleId": "342", "severity": 1, "message": "385", "line": 1, "column": 16, "nodeType": "344", "messageId": "345", "endLine": 1, "endColumn": 21}, {"ruleId": "342", "severity": 1, "message": "386", "line": 1, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 1, "endColumn": 12}, {"ruleId": "342", "severity": 1, "message": "387", "line": 2, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 20}, {"ruleId": "342", "severity": 1, "message": "388", "line": 2, "column": 22, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 29}, {"ruleId": "342", "severity": 1, "message": "389", "line": 2, "column": 31, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 34}, {"ruleId": "342", "severity": 1, "message": "390", "line": 2, "column": 36, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 42}, {"ruleId": "342", "severity": 1, "message": "391", "line": 2, "column": 44, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 50}, {"ruleId": "342", "severity": 1, "message": "392", "line": 2, "column": 52, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 61}, {"ruleId": "342", "severity": 1, "message": "393", "line": 2, "column": 63, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 72}, {"ruleId": "342", "severity": 1, "message": "394", "line": 2, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 17}, {"ruleId": "342", "severity": 1, "message": "368", "line": 2, "column": 19, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 25}, {"ruleId": "342", "severity": 1, "message": "395", "line": 2, "column": 27, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 35}, {"ruleId": "342", "severity": 1, "message": "396", "line": 2, "column": 37, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 41}, {"ruleId": "342", "severity": 1, "message": "397", "line": 2, "column": 43, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 54}, {"ruleId": "342", "severity": 1, "message": "385", "line": 2, "column": 56, "nodeType": "344", "messageId": "345", "endLine": 2, "endColumn": 61}, {"ruleId": "360", "severity": 1, "message": "398", "line": 41, "column": 6, "nodeType": "362", "endLine": 41, "endColumn": 8, "suggestions": "399"}, {"ruleId": "342", "severity": 1, "message": "400", "line": 11, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 11, "endColumn": 16}, {"ruleId": "342", "severity": 1, "message": "401", "line": 12, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 12, "endColumn": 17}, {"ruleId": "360", "severity": 1, "message": "379", "line": 90, "column": 6, "nodeType": "362", "endLine": 90, "endColumn": 26, "suggestions": "402"}, {"ruleId": "360", "severity": 1, "message": "403", "line": 61, "column": 6, "nodeType": "362", "endLine": 61, "endColumn": 8, "suggestions": "404"}, {"ruleId": "342", "severity": 1, "message": "405", "line": 7, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 7, "endColumn": 10}, {"ruleId": "342", "severity": 1, "message": "406", "line": 15, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 15, "endColumn": 14}, {"ruleId": "360", "severity": 1, "message": "403", "line": 70, "column": 6, "nodeType": "362", "endLine": 70, "endColumn": 8, "suggestions": "407"}, {"ruleId": "342", "severity": 1, "message": "408", "line": 154, "column": 15, "nodeType": "344", "messageId": "345", "endLine": 154, "endColumn": 19}, {"ruleId": "360", "severity": 1, "message": "409", "line": 71, "column": 6, "nodeType": "362", "endLine": 71, "endColumn": 22, "suggestions": "410"}, {"ruleId": "342", "severity": 1, "message": "411", "line": 16, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 16, "endColumn": 25}, {"ruleId": "360", "severity": 1, "message": "412", "line": 48, "column": 6, "nodeType": "362", "endLine": 48, "endColumn": 12, "suggestions": "413"}, {"ruleId": "342", "severity": 1, "message": "414", "line": 6, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 6, "endColumn": 28}, {"ruleId": "342", "severity": 1, "message": "415", "line": 7, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 7, "endColumn": 19}, {"ruleId": "342", "severity": 1, "message": "416", "line": 13, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 13, "endColumn": 18}, {"ruleId": "342", "severity": 1, "message": "417", "line": 15, "column": 3, "nodeType": "344", "messageId": "345", "endLine": 15, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "411", "line": 19, "column": 10, "nodeType": "344", "messageId": "345", "endLine": 19, "endColumn": 25}, {"ruleId": "360", "severity": 1, "message": "418", "line": 74, "column": 6, "nodeType": "362", "endLine": 74, "endColumn": 12, "suggestions": "419"}, {"ruleId": "342", "severity": 1, "message": "420", "line": 230, "column": 11, "nodeType": "344", "messageId": "345", "endLine": 230, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'firebaseUser' is assigned a value but never used.", "Identifier", "unusedVar", "'signInWithEmailAndPassword' is defined but never used.", "'User' is defined but never used.", "'CourseViewer' is defined but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'SparklesIcon' is defined but never used.", "'SparklesIconSolid' is defined but never used.", "'deleteObject' is defined but never used.", "'authService' is defined but never used.", "'loading' is assigned a value but never used.", "'showAddModal' is assigned a value but never used.", "'showEditModal' is assigned a value but never used.", "'selectedQuiz' is assigned a value but never used.", "'handleQuizAdded' is assigned a value but never used.", "'handleQuizUpdated' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCertificates'. Either include it or remove the dependency array.", "ArrayExpression", ["421"], "React Hook React.useEffect has a missing dependency: 'mockCertificates'. Either include it or remove the dependency array.", ["422"], "'motion' is defined but never used.", "'DocumentIcon' is defined but never used.", "'Course' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEnrolledCourses'. Either include it or remove the dependency array.", ["423"], "'BookOpenIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'navigate' is assigned a value but never used.", "'error' is assigned a value but never used.", "'videoProgress' is assigned a value but never used.", "'setVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCourseData'. Either include it or remove the dependency array.", ["424"], "'setTimeLeft' is assigned a value but never used.", "'useState' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", "'Admin' is defined but never used.", "'db' is defined but never used.", "'collection' is defined but never used.", "'getDocs' is defined but never used.", "'doc' is defined but never used.", "'getDoc' is defined but never used.", "'addDoc' is defined but never used.", "'updateDoc' is defined but never used.", "'deleteDoc' is defined but never used.", "'Student' is defined but never used.", "'Category' is defined but never used.", "'Quiz' is defined but never used.", "'Certificate' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkSessionValidity' and 'loadSession'. Either include them or remove the dependency array.", ["425"], "'ArrowLeftIcon' is defined but never used.", "'ArrowRightIcon' is defined but never used.", ["426"], "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["427"], "'EyeIcon' is defined but never used.", "'XCircleIcon' is defined but never used.", ["428"], "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadVideos'. Either include it or remove the dependency array.", ["429"], "'supabaseService' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadStudentStats'. Either include it or remove the dependency array.", ["430"], "'ClipboardDocumentListIcon' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadRecentActivity' and 'loadStudentStats'. Either include them or remove the dependency array.", ["431"], "'today' is assigned a value but never used.", {"desc": "432", "fix": "433"}, {"desc": "434", "fix": "435"}, {"desc": "436", "fix": "437"}, {"desc": "438", "fix": "439"}, {"desc": "440", "fix": "441"}, {"desc": "442", "fix": "443"}, {"desc": "444", "fix": "445"}, {"desc": "444", "fix": "446"}, {"desc": "447", "fix": "448"}, {"desc": "449", "fix": "450"}, {"desc": "451", "fix": "452"}, "Update the dependencies array to be: [loadCertificates]", {"range": "453", "text": "454"}, "Update the dependencies array to be: [mockCertificates]", {"range": "455", "text": "456"}, "Update the dependencies array to be: [loadEnrolledCourses, user.id]", {"range": "457", "text": "458"}, "Update the dependencies array to be: [courseId, loadCourseData]", {"range": "459", "text": "460"}, "Update the dependencies array to be: [checkSessionValidity, loadSession]", {"range": "461", "text": "462"}, "Update the dependencies array to be: [courseId, loadCourseData, navigate]", {"range": "463", "text": "464"}, "Update the dependencies array to be: [loadData]", {"range": "465", "text": "466"}, {"range": "467", "text": "466"}, "Update the dependencies array to be: [loadVideos, selectedCourse]", {"range": "468", "text": "469"}, "Update the dependencies array to be: [loadStudentStats, user]", {"range": "470", "text": "471"}, "Update the dependencies array to be: [loadRecentActivity, loadStudentStats, user]", {"range": "472", "text": "473"}, [758, 760], "[loadCertificates]", [2586, 2588], "[mockCertificates]", [1233, 1242], "[loadEnrolledCourses, user.id]", [1286, 1296], "[courseId, loadCourseData]", [1186, 1188], "[checkSessionValidity, loadSession]", [2262, 2282], "[courseId, loadCourseData, navigate]", [1539, 1541], "[loadData]", [1680, 1682], [1637, 1653], "[loadVideos, selectedCourse]", [1282, 1288], "[loadStudentStats, user]", [1804, 1810], "[loadRecentActivity, loadStudentStats, user]"]